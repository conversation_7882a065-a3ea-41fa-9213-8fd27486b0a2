FROM maven:3.9.5-eclipse-temurin-17-alpine AS build
WORKDIR /app

# 复制 Maven 配置文件
COPY pom.xml .

# 下载依赖项（利用 Docker 缓存）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用
RUN mvn package -DskipTests

# 运行阶段
FROM eclipse-temurin:17-jre-alpine
WORKDIR /app

# 创建上传目录
RUN mkdir -p /app/uploads && chmod 777 /app/uploads

# 复制构建产物
COPY --from=build /app/target/one-moment-backend-0.0.1-SNAPSHOT.jar app.jar

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "app.jar"]
