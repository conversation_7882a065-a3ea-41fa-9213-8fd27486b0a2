package com.onemoment.service;

import com.onemoment.model.Role;
import com.onemoment.model.User;
import com.onemoment.repository.RoleRepository;
import com.onemoment.repository.UserRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;

/**
 * 默认用户服务
 * 提供默认用户，用于无认证模式
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DefaultUserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    private static final String DEFAULT_USERNAME = "default_user";
    private static final String DEFAULT_PASSWORD = "password";
    private static final String DEFAULT_ROLE = "ROLE_USER";

    private User defaultUser;

    /**
     * 初始化默认用户
     */
    @PostConstruct
    @Transactional
    public void init() {
        log.info("初始化默认用户");

        // 查找或创建默认角色
        Role userRole = roleRepository.findByName(DEFAULT_ROLE)
                .orElseGet(() -> {
                    log.info("创建默认角色: {}", DEFAULT_ROLE);
                    Role role = new Role();
                    role.setName(DEFAULT_ROLE);
                    return roleRepository.save(role);
                });

        // 查找或创建默认用户
        Optional<User> existingUser = userRepository.findByUsername(DEFAULT_USERNAME);

        if (existingUser.isPresent()) {
            log.info("使用已存在的默认用户: {}", DEFAULT_USERNAME);
            defaultUser = existingUser.get();
        } else {
            log.info("创建默认用户: {}", DEFAULT_USERNAME);
            defaultUser = User.builder()
                    .username(DEFAULT_USERNAME)
                    .password(passwordEncoder.encode(DEFAULT_PASSWORD))
                    .nickname("默认用户")
                    .email("<EMAIL>")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .enabled(true)
                    .accountNonExpired(true)
                    .accountNonLocked(true)
                    .credentialsNonExpired(true)
                    .roles(Set.of(userRole))
                    .build();

            defaultUser = userRepository.save(defaultUser);
        }

        log.info("默认用户初始化完成: id={}", defaultUser.getId());

        // 初始化测试账号
        initTestAccount(userRole);
    }

    /**
     * 初始化测试账号
     */
    private void initTestAccount(Role userRole) {
        String testPhone = "***********";
        String testPassword = "123456";

        // 检查测试账号是否已存在
        if (userRepository.findByPhone(testPhone).isPresent()) {
            log.info("测试账号已存在: {}", testPhone);
            return;
        }

        // 创建测试账号
        User testUser = User.builder()
                .username(testPhone)
                .password(passwordEncoder.encode(testPassword))
                .nickname("测试用户")
                .phone(testPhone)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .roles(Set.of(userRole))
                .build();

        userRepository.save(testUser);
        log.info("测试账号创建成功: phone={}, password={}", testPhone, testPassword);
    }

    /**
     * 获取默认用户
     *
     * @return 默认用户
     */
    public User getDefaultUser() {
        return defaultUser;
    }
}
