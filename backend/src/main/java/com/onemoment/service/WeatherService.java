package com.onemoment.service;

import com.onemoment.dto.WeatherDto;
import com.onemoment.model.Weather;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 天气服务接口
 */
public interface WeatherService {

    /**
     * 获取指定城市的最新天气
     *
     * @param cityName 城市名称
     * @return 天气信息
     */
    WeatherDto getLatestWeather(String cityName);

    /**
     * 获取指定城市和日期的天气
     *
     * @param cityName 城市名称
     * @param date     日期
     * @return 天气信息
     */
    Optional<WeatherDto> getWeatherByDate(String cityName, LocalDate date);

    /**
     * 更新天气信息
     *
     * @param weatherDto 天气信息
     * @return 更新后的天气信息
     */
    WeatherDto updateWeather(WeatherDto weatherDto);

    /**
     * 模拟天气数据
     *
     * @param cityName 城市名称
     * @return 模拟的天气数据
     */
    WeatherDto mockWeatherData(String cityName);
}
