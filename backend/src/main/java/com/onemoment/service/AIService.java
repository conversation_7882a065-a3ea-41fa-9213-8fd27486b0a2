package com.onemoment.service;

import com.onemoment.model.Moment;

import java.util.List;
import java.util.Map;

/**
 * AI服务接口
 */
public interface AIService {

    /**
     * 分析瞬间内容
     *
     * @param moment 瞬间
     * @return 分析结果
     */
    Map<String, Object> analyzeMoment(Moment moment);

    /**
     * 异步分析瞬间内容
     *
     * @param moment 瞬间
     */
    void analyzeAsync(Moment moment);

    /**
     * 生成鼓励消息
     *
     * @param moments 最近的瞬间列表
     * @return 鼓励消息
     */
    String generateEncouragement(List<Moment> moments);

    /**
     * 生成月度总结
     *
     * @param moments 月度瞬间列表
     * @return 月度总结
     */
    Map<String, Object> generateMonthlySummary(List<Moment> moments);

    /**
     * 提取关键词
     *
     * @param text 文本内容
     * @return 关键词列表
     */
    List<String> extractKeywords(String text);

    /**
     * 分析情绪
     *
     * @param text 文本内容
     * @return 情绪类型
     */
    String analyzeMood(String text);

    /**
     * 与AI聊天
     *
     * @param userId  用户ID
     * @param message 消息
     * @return AI回复
     */
    String chat(Long userId, String message);

    /**
     * 获取自定义提示词
     *
     * @param moments 最近的瞬间列表
     * @return 自定义提示词
     */
    String getCustomPrompt(List<Moment> moments);
}
