package com.onemoment.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.green.model.v20180509.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemoment.service.ContentSafetyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 阿里云内容安全服务实现类
 */
@Service
@Slf4j
public class AliyunGreenContentSafetyService implements ContentSafetyService {

    @Value("${aliyun.green.region-id}")
    private String regionId;

    @Value("${aliyun.green.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.green.access-key-secret}")
    private String accessKeySecret;

    @Value("${app.content-safety.enabled:true}")
    private boolean enabled;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 心理危机关键词列表
    private static final Set<String> CRISIS_KEYWORDS = new HashSet<>(Arrays.asList(
            "自杀", "轻生", "自残", "割腕", "跳楼", "上吊", "服毒", "抑郁", "绝望", "活不下去", "生不如死"
    ));

    @Override
    public boolean checkTextSafety(String text) {
        if (!enabled) {
            return true;
        }

        // 检查是否包含心理危机关键词
        if (containsCrisisKeywords(text)) {
            log.warn("文本内容包含心理危机关键词: {}", text);
            return false;
        }

        try {
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint(regionId, "Green", "green." + regionId + ".aliyuncs.com");
            IAcsClient client = new DefaultAcsClient(profile);

            TextScanRequest textScanRequest = new TextScanRequest();
            textScanRequest.setAcceptFormat(FormatType.JSON);
            textScanRequest.setHttpContentType(FormatType.JSON);
            textScanRequest.setMethod(com.aliyuncs.http.MethodType.POST);
            textScanRequest.setEncoding("UTF-8");
            textScanRequest.setRegionId(regionId);

            List<Map<String, Object>> tasks = new ArrayList<>();
            Map<String, Object> task = new HashMap<>();
            task.put("dataId", UUID.randomUUID().toString());
            task.put("content", text);
            tasks.add(task);

            Map<String, Object> content = new HashMap<>();
            content.put("scenes", Arrays.asList("antispam"));
            content.put("tasks", tasks);

            textScanRequest.setHttpContent(objectMapper.writeValueAsString(content).getBytes(StandardCharsets.UTF_8), "UTF-8", FormatType.JSON);

            HttpResponse httpResponse = client.doAction(textScanRequest);
            if (httpResponse.isSuccess()) {
                JsonNode responseNode = objectMapper.readTree(new String(httpResponse.getHttpContent(), StandardCharsets.UTF_8));
                if (200 == responseNode.get("code").asInt()) {
                    JsonNode taskResults = responseNode.get("data");
                    for (JsonNode taskResult : taskResults) {
                        if (200 == taskResult.get("code").asInt()) {
                            JsonNode sceneResults = taskResult.get("results");
                            for (JsonNode sceneResult : sceneResults) {
                                String scene = sceneResult.get("scene").asText();
                                String suggestion = sceneResult.get("suggestion").asText();
                                if ("antispam".equals(scene) && !"pass".equals(suggestion)) {
                                    log.warn("文本内容未通过安全检查: {}, 建议: {}", text, suggestion);
                                    return false;
                                }
                            }
                        } else {
                            log.error("文本内容检查失败: {}", taskResult.get("msg").asText());
                            return false;
                        }
                    }
                } else {
                    log.error("文本内容检查请求失败: {}", responseNode.get("msg").asText());
                    return false;
                }
            } else {
                log.error("文本内容检查HTTP请求失败: {}", httpResponse.getStatus());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("文本内容检查异常", e);
            return false;
        }
    }

    @Override
    public boolean checkFileSafety(MultipartFile file) {
        if (!enabled) {
            return true;
        }

        // 简单实现，仅检查文件类型
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }

        // 允许的文件类型
        return contentType.startsWith("image/") ||
               contentType.startsWith("video/") ||
               contentType.startsWith("audio/");
    }

    @Override
    public boolean checkUrlSafety(String url) {
        // 简单实现，实际项目中应该调用阿里云内容安全服务的图片检测接口
        return true;
    }

    @Override
    public boolean containsSensitiveWords(String text) {
        if (!enabled || text == null) {
            return false;
        }

        // 简单实现，实际项目中应该使用敏感词库和DFA算法
        return false;
    }

    @Override
    public boolean containsCrisisKeywords(String text) {
        if (!enabled || text == null) {
            return false;
        }

        // 检查是否包含心理危机关键词
        for (String keyword : CRISIS_KEYWORDS) {
            if (text.contains(keyword)) {
                return true;
            }
        }

        return false;
    }
}
