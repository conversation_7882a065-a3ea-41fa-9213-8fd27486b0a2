package com.onemoment.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.onemoment.model.AIAnalysis;
import com.onemoment.model.Moment;
import com.onemoment.repository.AIAnalysisRepository;
import com.onemoment.repository.MomentRepository;
import com.onemoment.service.AIService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.Resource;
import java.util.Properties;
import java.io.IOException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.PostConstruct;

/**
 * 通义千问AI服务实现类
 * 负责与阿里云通义千问大模型API交互，提供内容分析、情感分析、关键词提取等功能
 */
@Service
@Slf4j
@RequiredArgsConstructor  // 自动生成包含所有final字段的构造函数
public class TongyiAIService implements AIService {

    // 核心依赖注入
    private final RestTemplate restTemplate;  // 用于发送HTTP请求
    private final ObjectMapper objectMapper;  // JSON处理工具
    private final MomentRepository momentRepository;  // 瞬间数据访问
    private final AIAnalysisRepository aiAnalysisRepository;  // AI分析结果数据访问
    private final ResourceLoader resourceLoader;  // 资源加载器，用于加载配置文件

    // 配置属性注入
    @Value("${ai.service.tongyi.api-key}")  // 从配置文件中注入API密钥
    private String apiKey;

    @Value("${ai.service.tongyi.api-url}")  // 从配置文件中注入API地址
    private String apiUrl;

    private final String DEFAULT_MODEL = "qwen-max";  // 默认使用的模型名称

    private String systemPrompt;  // 存储系统提示词

    /**
     * 初始化方法，在服务创建后自动执行
     * 加载提示词配置文件
     */
    @PostConstruct
    public void init() {
        try {
            // 加载悦己AI助手的系统提示词
            Resource resource = resourceLoader.getResource("classpath:qwen_prompts.md");
            systemPrompt = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            log.info("成功加载悦己AI助手系统提示词");
        } catch (IOException e) {
            log.error("加载悦己AI助手系统提示词失败", e);
            systemPrompt = "你是一位专业的心理健康助手，名为\"悦己\"，专注于帮助用户进行自我关怀和情感支持。";  // 默认提示词
        }
    }



    /**
     * 分析瞬间内容，提取情绪、标签和摘要
     *
     * @param moment 要分析的瞬间对象
     * @return 分析结果，包含情绪、标签和摘要
     */
    @Override
    public Map<String, Object> analyzeMoment(Moment moment) {
        try {
            // 获取文本内容，如果为空则使用默认描述
            String content = moment.getTextContent();
            if (content == null || content.isEmpty()) {
                content = "用户分享了一张图片/视频/音频";
            }

            // 构建分析提示词
            String prompt = String.format(
                    "分析以下内容，提取关键信息，包括：\n" +
                    "1. 主要情绪（积极、中性、消极）\n" +
                    "2. 可能的标签（最多5个）\n" +
                    "3. 内容摘要（不超过50字）\n\n" +
                    "内容：%s", content);

            // 调用AI接口获取分析结果
            String response = chatCompletion(prompt);
            Map<String, Object> analysis = parseAnalysisResponse(response);

            // 保存分析结果到数据库
            AIAnalysis aiAnalysis = AIAnalysis.builder()
                    .moment(moment)
                    .provider("tongyi")
                    .model(DEFAULT_MODEL)
                    .analysisData(analysis)
                    .build();
            aiAnalysisRepository.save(aiAnalysis);

            return analysis;
        } catch (Exception e) {
            log.error("分析瞬间内容失败", e);
            return Collections.emptyMap();  // 发生异常时返回空映射
        }
    }

    /**
     * 异步分析瞬间内容
     * 使用@Async注解实现异步处理，不阻塞主线程
     *
     * @param moment 要分析的瞬间对象
     */
    @Override
    @Async
    public void analyzeAsync(Moment moment) {
        try {
            analyzeMoment(moment);
        } catch (Exception e) {
            log.error("异步分析瞬间内容失败", e);
        }
    }

    /**
     * 根据用户最近的瞬间生成鼓励消息
     *
     * @param moments 用户最近的瞬间列表
     * @return 生成的鼓励消息
     */
    @Override
    public String generateEncouragement(List<Moment> moments) {
        try {
            // 提取最近瞬间的文本内容
            String recentContents = moments.stream()
                    .filter(moment -> moment.getTextContent() != null && !moment.getTextContent().isEmpty())
                    .map(moment -> moment.getTextContent())
                    .collect(Collectors.joining("\n\n"));

            // 构建鼓励消息提示词
            String prompt = String.format(
                    "基于用户最近的记录内容，给出温暖的鼓励和支持。用户最近的记录：\n%s\n\n请给出积极正面的回应。",
                    recentContents);

            return chatCompletion(prompt, systemPrompt);
        } catch (Exception e) {
            log.error("生成鼓励消息失败", e);
            return "每一天都有新的可能，希望你继续记录生活中的美好瞬间！";  // 发生异常时返回默认鼓励消息
        }
    }

    @Override
    public Map<String, Object> generateMonthlySummary(List<Moment> monthlyMoments) {
        try {
            // 提取月度瞬间的文本内容
            StringBuilder contentBuilder = new StringBuilder();

            for (Moment moment : monthlyMoments) {
                if (moment.getTextContent() != null && !moment.getTextContent().isEmpty()) {
                    contentBuilder.append(moment.getCreatedAt().getDayOfMonth())
                        .append("日: ")
                        .append(moment.getTextContent())
                        .append("\n\n");
                }
            }

            String monthlyContents = contentBuilder.toString();

            String prompt = String.format(
                    "请为用户的月度记录生成一个温暖的总结，包括情感变化、成长亮点和积极建议。月度记录：\n%s",
                    monthlyContents);

            String response = chatCompletion(prompt, systemPrompt);

            return parseSummaryResponse(response);
        } catch (Exception e) {
            log.error("生成月度总结失败", e);
            return Collections.emptyMap();
        }
    }

    public boolean checkContentSafety(String text) {
        try {
            String prompt = String.format(
                    "请判断以下内容是否安全，只回答\"是\"或\"否\"。内容：%s", text);

            String response = chatCompletion(prompt);

            return response.contains("是");
        } catch (Exception e) {
            log.error("检查敏感内容异常", e);
            return false;
        }
    }

    @Override
    public List<String> extractKeywords(String text) {
        try {
            if (text == null || text.isEmpty()) {
                return Collections.emptyList();
            }

            String prompt = String.format(
                    "从以下内容中提取3-5个关键词，每个关键词不超过4个字，用逗号分隔：\n\n%s", text);

            String response = chatCompletion(prompt);
            return Arrays.asList(response.split("[,，、]"));
        } catch (Exception e) {
            log.error("提取关键词失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public String analyzeMood(String text) {
        try {
            if (text == null || text.isEmpty()) {
                return null;
            }

            String prompt = String.format(
                    "分析以下文本的情绪，只回复一个词：开心、平静、中性、悲伤或愤怒。\n\n文本：%s", text);

            String response = chatCompletion(prompt);

            if (response.contains("开心")) {
                return "happy";
            } else if (response.contains("平静")) {
                return "calm";
            } else if (response.contains("中性")) {
                return "neutral";
            } else if (response.contains("悲伤")) {
                return "sad";
            } else if (response.contains("愤怒")) {
                return "angry";
            }

            return null;
        } catch (Exception e) {
            log.error("分析情绪失败", e);
            return null;
        }
    }

    @Override
    public String chat(Long userId, String message) {
        try {
            // 直接使用悦己AI助手的系统提示词进行聊天
            return chatCompletion(message, systemPrompt);
        } catch (Exception e) {
            log.error("悦己AI聊天失败", e);
            return "抱歉，我遇到了一些问题，无法正常回应。请稍后再试。";
        }
    }

    @Override
    public String getCustomPrompt(List<Moment> moments) {
        try {
            // 提取最近瞬间的文本内容和情绪
            String recentContentsWithMood = moments.stream()
                    .filter(moment -> moment.getTextContent() != null && !moment.getTextContent().isEmpty())
                    .map(moment -> String.format("内容: %s, 情绪: %s",
                            moment.getTextContent(),
                            moment.getMood() != null ? moment.getMood() : "未知"))
                    .collect(Collectors.joining("\n"));

            String prompt = String.format(
                    "基于用户最近的记录，生成个性化的对话提示词。用户记录：\n%s",
                    recentContentsWithMood);

            return chatCompletion(prompt, systemPrompt);
        } catch (Exception e) {
            log.error("生成自定义提示词失败", e);
            return "请以温暖、理解的方式与用户交流，关注他们的情感需求。";
        }
    }

    /**
     * 调用通义千问API进行聊天
     *
     * @param message 用户消息
     * @return AI回复
     */
    private String chatCompletion(String message) {
        return chatCompletion(message, null);
    }

    /**
     * 调用通义千问API进行聊天
     *
     * @param message      用户消息
     * @param systemPrompt 系统提示词
     * @return AI回复
     */
    private String chatCompletion(String message, String systemPrompt) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);

            ObjectNode requestBody = objectMapper.createObjectNode();

            // 设置模型
            requestBody.put("model", DEFAULT_MODEL);

            // 构建输入参数
            ObjectNode inputNode = objectMapper.createObjectNode();
            ArrayNode messagesNode = objectMapper.createArrayNode();

            // 添加系统提示词
            if (systemPrompt != null && !systemPrompt.isEmpty()) {
                ObjectNode systemMessage = objectMapper.createObjectNode();
                systemMessage.put("role", "system");
                systemMessage.put("content", systemPrompt);
                messagesNode.add(systemMessage);
            }

            // 添加用户消息
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", message);
            messagesNode.add(userMessage);

            // 设置输入参数
            inputNode.set("messages", messagesNode);
            requestBody.set("input", inputNode);

            // 设置参数
            ObjectNode parametersNode = objectMapper.createObjectNode();
            parametersNode.put("temperature", 0.7);
            parametersNode.put("top_p", 0.9);
            parametersNode.put("result_format", "text");
            // 增加最大生成token数，减少超时可能性
            parametersNode.put("max_tokens", 1500);
            requestBody.set("parameters", parametersNode);

            log.debug("通义千问API请求: {}", requestBody.toString());

            // 发送请求，使用重试机制
            HttpEntity<String> request = new HttpEntity<>(requestBody.toString(), headers);
            String response = null;
            int maxRetries = 3;
            int retryCount = 0;

            while (retryCount < maxRetries) {
                try {
                    response = restTemplate.postForObject(apiUrl + "services/aigc/text-generation/generation", request, String.class);
                    break; // 成功获取响应，跳出循环
                } catch (org.springframework.web.client.ResourceAccessException e) {
                    // 只有在超时或连接问题时重试
                    if (e.getMessage().contains("Read timed out") || e.getMessage().contains("Connection")) {
                        retryCount++;
                        log.warn("AI请求超时，正在重试 ({}/{})", retryCount, maxRetries);
                        if (retryCount >= maxRetries) {
                            throw e; // 重试次数用完，抛出异常
                        }
                        // 指数退避策略
                        try {
                            Thread.sleep(1000 * (long)Math.pow(2, retryCount));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("重试过程被中断", ie);
                        }
                    } else {
                        throw e; // 其他类型的异常直接抛出
                    }
                }
            }

            log.debug("通义千问API响应: {}", response);

            // 解析响应
            JsonNode responseNode = objectMapper.readTree(response);

            // 新版API响应格式
            if (responseNode.has("output") && responseNode.get("output").has("text")) {
                return responseNode.get("output").get("text").asText();
            }

            // 旧版API响应格式（兼容）
            if (responseNode.has("choices") && responseNode.get("choices").isArray() &&
                responseNode.get("choices").size() > 0 &&
                responseNode.get("choices").get(0).has("message") &&
                responseNode.get("choices").get(0).get("message").has("content")) {
                return responseNode.get("choices").get(0).get("message").get("content").asText();
            }

            // 无法解析响应
            log.error("无法解析通义千问API响应: {}", response);
            return "抱歉，我无法理解您的问题。请稍后再试。";
        } catch (org.springframework.web.client.ResourceAccessException e) {
            // 处理超时异常
            log.error("调用通义千问API超时", e);
            if (e.getMessage().contains("Read timed out")) {
                return "抱歉，AI服务响应超时，请稍后再试。您可以尝试简化问题或分多次提问。";
            } else if (e.getMessage().contains("Connection")) {
                return "抱歉，AI服务连接失败，请检查网络连接并稍后再试。";
            } else {
                log.error("调用通义千问API失败: {}", e.getMessage(), e);
                return "抱歉，AI服务暂时不可用，请稍后再试。";
            }
        } catch (Exception e) {
            log.error("调用通义千问API失败", e);
            // 返回友好错误信息而不是抛出异常，避免整个请求失败
            return "抱歉，AI服务出现问题，请稍后再试。错误信息: " + e.getMessage();
        }
    }

    /**
     * 解析分析响应
     *
     * @param response AI响应
     * @return 分析结果
     */
    private Map<String, Object> parseAnalysisResponse(String response) {
        Map<String, Object> analysis = new HashMap<>();

        try {
            if (response.contains("主要情绪")) {
                String emotionPattern = "主要情绪[：:]\\s*(.+)";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(emotionPattern);
                java.util.regex.Matcher matcher = pattern.matcher(response);
                if (matcher.find()) {
                    analysis.put("emotion", matcher.group(1).trim());
                }
            }

            if (response.contains("标签")) {
                String tagsPattern = "标签[：:]\\s*(.+)";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tagsPattern);
                java.util.regex.Matcher matcher = pattern.matcher(response);
                if (matcher.find()) {
                    String tagsStr = matcher.group(1).trim();
                    List<String> tags = Arrays.stream(tagsStr.split("[,，、]"))
                            .map(String::trim)
                            .filter(tag -> !tag.isEmpty())
                            .collect(Collectors.toList());
                    analysis.put("tags", tags);
                }
            }

            if (response.contains("内容摘要")) {
                String summaryPattern = "内容摘要[：:]\\s*(.+)";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(summaryPattern);
                java.util.regex.Matcher matcher = pattern.matcher(response);
                if (matcher.find()) {
                    analysis.put("summary", matcher.group(1).trim());
                }
            }
        } catch (Exception e) {
            log.error("解析分析响应失败", e);
        }

        return analysis;
    }

    /**
     * 解析月度总结响应
     *
     * @param response AI响应
     * @return 解析后的月度总结
     */
    private Map<String, Object> parseSummaryResponse(String response) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 简单解析，实际项目中可能需要更复杂的解析逻辑
            result.put("summary", response);
            result.put("generated_at", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        } catch (Exception e) {
            log.error("解析月度总结响应失败", e);
        }
        return result;
    }
}
