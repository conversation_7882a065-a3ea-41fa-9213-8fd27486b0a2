package com.onemoment.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.aliyun.oss.model.PutObjectRequest;
import com.onemoment.dto.OssUploadResponse;
import com.onemoment.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 阿里云OSS存储服务实现类
 */
@Service
@Slf4j
public class AliyunOssStorageService implements StorageService {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Value("${aliyun.oss.url-prefix}")
    private String urlPrefix;

    @Value("${aliyun.oss.dir-prefix}")
    private String dirPrefix;

    @Override
    public OssUploadResponse uploadFile(Long userId, MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String objectKey = generateObjectKey(userId, extension);

        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            InputStream inputStream = file.getInputStream();

            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, inputStream);
            ossClient.putObject(putObjectRequest);
            inputStream.close();

            String fileUrl = urlPrefix + objectKey;

            // 构建上传响应，保留完整的对象键
            return OssUploadResponse.builder()
                    .fileUrl(fileUrl)
                    .objectKey(objectKey) // 保留完整的对象键，包括路径和扩展名
                    .fileName(originalFilename)
                    .fileSize(file.getSize())
                    .contentType(file.getContentType())
                    .build();
        } catch (IOException e) {
            log.error("上传文件到OSS失败", e);
            throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public String generatePresignedUrl(String fileIdentifier, int expiration) {
        OSS ossClient = null;
        try {
            // 根据文件标识符构建完整的对象键
            // 如果fileIdentifier已经是完整的对象键（包含路径），则直接使用
            String objectKey;
            if (fileIdentifier.contains("/")) {
                objectKey = fileIdentifier;
            } else {
                // 使用默认用户ID 1L
                objectKey = reconstructObjectKey(1L, fileIdentifier);
            }

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            Date expireDate = new Date(System.currentTimeMillis() + expiration * 1000L);
            return ossClient.generatePresignedUrl(bucketName, objectKey, expireDate).toString();
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", fileIdentifier, e);
            throw new RuntimeException("生成预签名URL失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public String generateUploadToken(Long userId, String fileName, String contentType) {
        String extension = getFileExtension(fileName);
        String objectKey = generateObjectKey(userId, extension);

        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            long expireTime = 30;
            long expireEndTime = System.currentTimeMillis() + expireTime * 1000;
            Date expiration = new Date(expireEndTime);

            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dirPrefix);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            return String.format("{\"accessKeyId\":\"%s\",\"policy\":\"%s\",\"signature\":\"%s\",\"dir\":\"%s\",\"host\":\"%s\",\"expire\":%d,\"key\":\"%s\"}",
                    accessKeyId, encodedPolicy, postSignature, dirPrefix, urlPrefix, expireEndTime / 1000, objectKey);
        } catch (Exception e) {
            log.error("生成上传凭证失败", e);
            throw new RuntimeException("生成上传凭证失败", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public void deleteFile(String fileIdentifier) {
        OSS ossClient = null;
        try {
            // 根据文件标识符构建完整的对象键
            // 如果fileIdentifier已经是完整的对象键（包含路径），则直接使用
            String objectKey;
            if (fileIdentifier.contains("/")) {
                objectKey = fileIdentifier;
            } else {
                // 使用默认用户ID 1L
                objectKey = reconstructObjectKey(1L, fileIdentifier);
            }

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            ossClient.deleteObject(bucketName, objectKey);
        } catch (Exception e) {
            log.error("删除OSS文件失败: {}", fileIdentifier, e);
            throw new RuntimeException("删除文件失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public Map<String, Object> downloadFile(String objectKey) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            // 检查文件是否存在
            boolean exists = ossClient.doesObjectExist(bucketName, objectKey);
            if (!exists) {
                throw new RuntimeException("文件不存在: " + objectKey);
            }

            // 获取文件元数据
            com.aliyun.oss.model.OSSObject ossObject = ossClient.getObject(bucketName, objectKey);

            // 读取文件内容
            InputStream inputStream = ossObject.getObjectContent();
            byte[] content = inputStream.readAllBytes();
            inputStream.close();

            // 获取文件元数据
            String contentType = ossObject.getObjectMetadata().getContentType();
            if (contentType == null || contentType.isEmpty()) {
                contentType = "application/octet-stream";
            }

            // 从对象键中提取文件名
            String fileName = objectKey.substring(objectKey.lastIndexOf('/') + 1);

            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("contentType", contentType);
            result.put("fileName", fileName);

            return result;
        } catch (Exception e) {
            log.error("从OSS下载文件失败: objectKey={}", objectKey, e);
            throw new RuntimeException("下载文件失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public byte[] getFile(String objectKey) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            // 检查文件是否存在
            boolean exists = ossClient.doesObjectExist(bucketName, objectKey);
            if (!exists) {
                log.warn("文件不存在: {}", objectKey);
                return null;
            }

            // 获取文件
            com.aliyun.oss.model.OSSObject ossObject = ossClient.getObject(bucketName, objectKey);

            // 读取文件内容
            InputStream inputStream = ossObject.getObjectContent();
            byte[] content = inputStream.readAllBytes();
            inputStream.close();

            return content;
        } catch (Exception e) {
            log.error("从OSS获取文件失败: objectKey={}", objectKey, e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 生成对象键
     *
     * @param userId    用户ID
     * @param extension 文件扩展名（包含.）
     * @return 对象键
     */
    private String generateObjectKey(Long userId, String extension) {
        // 使用 /moment/userId/ 作为目录结构
        // 文件名使用当前时间+UUID，并保留文件扩展名
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 确保扩展名以.开头
        if (extension != null && !extension.isEmpty() && !extension.startsWith(".")) {
            extension = "." + extension;
        }

        return String.format("moment/%d/%s%s%s", userId, timestamp, uuid, extension);
    }

    /**
     * 获取文件唯一标识符（不包含目录结构）
     *
     * @param objectKey 完整的对象键
     * @return 文件唯一标识符
     */
    private String getFileIdentifier(String objectKey) {
        // 从完整的对象键中提取文件唯一标识符（不包含目录结构）
        int lastSlashIndex = objectKey.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < objectKey.length() - 1) {
            String fileName = objectKey.substring(lastSlashIndex + 1);
            // 去掉扩展名，只保留UUID部分
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex != -1) {
                return fileName.substring(0, lastDotIndex);
            }
            return fileName;
        }
        return objectKey;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }

    /**
     * 根据文件标识符重建完整的对象键
     * 注意：由于无法确定原始的目录结构，这里使用一种启发式方法
     * 实际应用中，应该在数据库中存储完整的对象键和文件标识符的映射关系
     *
     * @param userId 用户ID
     * @param fileIdentifier 文件唯一标识符
     * @return 重建的对象键
     */
    private String reconstructObjectKey(Long userId, String fileIdentifier) {
        String filePath = generateObjectKey(userId, fileIdentifier);
        log.info("filePath with userId: {}", filePath);
        return filePath;
    }

    /**
     * 根据文件标识符重建完整的对象键
     * 这个方法用于处理已经是完整对象键的情况
     *
     * @param objectKey 对象键
     * @return 对象键
     */
    private String reconstructObjectKey(String objectKey) {
        // 如果已经是完整的对象键，直接返回
        log.info("Using direct objectKey: {}", objectKey);
        return objectKey;
    }
}
