package com.onemoment.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemoment.dto.WeatherDto;
import com.onemoment.model.Weather;
import com.onemoment.repository.WeatherRepository;
import com.onemoment.service.WeatherService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 高德地图天气服务实现类
 * 使用高德地图MCP API获取天气数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AmapWeatherService implements WeatherService {

    private final WeatherRepository weatherRepository;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${amap.key:3e39ae9f0e1d8c8a1d7e0a9e7e8d6f5e}")
    private String amapKey;

    @Value("${amap.weather.url:https://restapi.amap.com/v3/weather/weatherInfo}")
    private String amapWeatherUrl;

    @Override
    public WeatherDto getLatestWeather(String cityName) {
        log.info("获取城市最新天气: {}", cityName);

        // 直接从高德地图API获取最新天气数据，不使用数据库缓存
        return fetchWeatherFromAmap(cityName);
    }

    @Override
    public Optional<WeatherDto> getWeatherByDate(String cityName, LocalDate date) {
        log.info("获取城市指定日期天气: {}, {}，直接返回当前天气", cityName, date);
        // 由于不再使用数据库缓存，无法获取历史天气数据，直接返回当前天气
        return Optional.of(fetchWeatherFromAmap(cityName));
    }

    @Override
    public WeatherDto updateWeather(WeatherDto weatherDto) {
        log.info("更新天气数据: {}", weatherDto);

        // 转换为实体
        Weather weather = convertToEntity(weatherDto);

        // 保存到数据库
        Weather savedWeather = weatherRepository.save(weather);

        // 返回保存后的数据
        return convertToDto(savedWeather);
    }

    @Override
    public WeatherDto mockWeatherData(String cityName) {
        log.info("不再使用模拟数据，从高德地图API获取真实天气数据: {}", cityName);
        return fetchWeatherFromAmap(cityName);
    }



    /**
     * 从高德地图API获取天气数据
     *
     * @param cityName 城市名称
     * @return 天气数据
     */
    private WeatherDto fetchWeatherFromAmap(String cityName) {
        try {
            // 构建请求URL
            String url = String.format("%s?key=%s&city=%s&extensions=base",
                    amapWeatherUrl, amapKey, cityName);

            log.info("请求高德地图天气API: {}", url);

            // 发送请求
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            // 解析响应
            JsonNode root = objectMapper.readTree(response.getBody());

            // 检查状态码
            if (!"1".equals(root.path("status").asText())) {
                log.error("高德地图API返回错误: {}", root.path("info").asText());
                throw new RuntimeException("获取天气数据失败: " + root.path("info").asText());
            }

            // 获取天气数据
            JsonNode lives = root.path("lives");
            if (lives.isEmpty()) {
                log.error("高德地图API返回的天气数据为空");
                throw new RuntimeException("获取天气数据失败: 返回的天气数据为空");
            }

            JsonNode weatherData = lives.get(0);

            // 解析天气数据
            String weatherCondition = weatherData.path("weather").asText();

            // 安全解析温度
            double temperature;
            try {
                temperature = Double.parseDouble(weatherData.path("temperature").asText());
            } catch (NumberFormatException e) {
                log.warn("解析温度失败，使用默认值: {}", weatherData.path("temperature").asText());
                temperature = 20.0;
            }

            // 安全解析湿度
            int humidity;
            try {
                humidity = Integer.parseInt(weatherData.path("humidity").asText());
            } catch (NumberFormatException e) {
                log.warn("解析湿度失败，使用默认值: {}", weatherData.path("humidity").asText());
                humidity = 50;
            }

            // 安全解析风力
            double windSpeed;
            try {
                String windPower = weatherData.path("windpower").asText();
                // 处理特殊情况，如 "≤3" 这样的值
                windPower = windPower.replaceAll("[^0-9.]", "");

                // 如果处理后为空字符串，使用默认值
                if (windPower.isEmpty()) {
                    log.warn("风力值处理后为空，使用默认值: {}", weatherData.path("windpower").asText());
                    windSpeed = 2.0;
                } else {
                    windSpeed = Double.parseDouble(windPower);
                }
            } catch (NumberFormatException e) {
                log.warn("解析风力失败，使用默认值: {}", weatherData.path("windpower").asText());
                windSpeed = 2.0;
            }

            String windDirection = weatherData.path("winddirection").asText() + "风";

            // 创建天气DTO直接返回，不保存到数据库
            return WeatherDto.builder()
                    .cityName(cityName)
                    .weatherCondition(weatherCondition)
                    .temperature(temperature)
                    .humidity(humidity)
                    .windSpeed(windSpeed)
                    .windDirection(windDirection)
                    .updatedAt(LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            log.error("获取高德地图天气数据失败", e);

            // 创建一个默认的天气数据直接返回，不保存到数据库
            log.info("API调用失败，返回默认天气数据");
            return WeatherDto.builder()
                    .cityName(cityName)
                    .weatherCondition("未知")
                    .temperature(20.0)
                    .humidity(50)
                    .windSpeed(2.0)
                    .windDirection("未知")
                    .updatedAt(LocalDateTime.now())
                    .build();
        }
    }

    /**
     * 将实体转换为DTO
     */
    private WeatherDto convertToDto(Weather weather) {
        return WeatherDto.builder()
                .id(weather.getId())
                .cityName(weather.getCityName())
                .weatherCondition(weather.getWeatherCondition())
                .temperature(weather.getTemperature())
                .humidity(weather.getHumidity())
                .windSpeed(weather.getWindSpeed())
                .windDirection(weather.getWindDirection())
                .updatedAt(weather.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     */
    private Weather convertToEntity(WeatherDto weatherDto) {
        return Weather.builder()
                .id(weatherDto.getId())
                .cityName(weatherDto.getCityName())
                .weatherCondition(weatherDto.getWeatherCondition())
                .temperature(weatherDto.getTemperature())
                .humidity(weatherDto.getHumidity())
                .windSpeed(weatherDto.getWindSpeed())
                .windDirection(weatherDto.getWindDirection())
                .build();
    }
}
