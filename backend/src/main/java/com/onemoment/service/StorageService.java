package com.onemoment.service;

import com.onemoment.dto.OssUploadResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 存储服务接口
 */
public interface StorageService {

    /**
     * 上传文件
     *
     * @param userId 用户ID
     * @param file   文件
     * @return 上传响应，包含文件URL和其他信息
     */
    OssUploadResponse uploadFile(Long userId, MultipartFile file);

    /**
     * 生成文件的临时访问URL
     *
     * @param fileIdentifier 文件唯一标识符
     * @param expiration 过期时间（秒）
     * @return 临时访问URL
     */
    String generatePresignedUrl(String fileIdentifier, int expiration);

    /**
     * 生成上传凭证
     *
     * @param userId 用户ID
     * @param fileName 文件名
     * @param contentType 内容类型
     * @return 上传凭证
     */
    String generateUploadToken(Long userId, String fileName, String contentType);

    /**
     * 删除文件
     *
     * @param fileIdentifier 文件唯一标识符
     */
    void deleteFile(String fileIdentifier);

    /**
     * 下载文件
     *
     * @param objectKey 对象键（完整路径）
     * @return 包含文件内容和元数据的Map，键包括：content（字节数组）、contentType（内容类型）、fileName（文件名）
     */
    Map<String, Object> downloadFile(String objectKey);

    /**
     * 获取文件内容
     *
     * @param objectKey 对象键（完整路径）
     * @return 文件内容的字节数组
     */
    byte[] getFile(String objectKey);
}
