package com.onemoment.service;

import com.onemoment.dto.*;
import com.onemoment.model.Role;
import com.onemoment.model.User;
import com.onemoment.repository.RoleRepository;
import com.onemoment.repository.UserRepository;
import com.onemoment.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册响应
     */
    @Transactional
    public LoginResponse register(RegisterRequest request) {
        // 检查手机号是否已存在
        if (userRepository.findByPhone(request.getPhone()).isPresent()) {
            throw new RuntimeException("手机号已被注册");
        }

        // 创建用户
        User user = User.builder()
                .username(request.getPhone()) // 使用手机号作为用户名
                .password(passwordEncoder.encode(request.getPassword()))
                .nickname(request.getNickname() != null ? request.getNickname() : "用户" + request.getPhone().substring(7))
                .phone(request.getPhone())
                .email(request.getEmail())
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .roles(new HashSet<>())
                .build();

        // 添加默认角色
        Optional<Role> userRole = roleRepository.findByName("ROLE_USER");
        if (userRole.isPresent()) {
            user.getRoles().add(userRole.get());
        }

        // 保存用户
        User savedUser = userRepository.save(user);
        log.info("用户注册成功: userId={}, phone={}", savedUser.getId(), savedUser.getPhone());

        // 生成令牌
        return generateTokenResponse(savedUser);
    }

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @Transactional
    public LoginResponse login(LoginRequest request) {
        if ("phone".equals(request.getLoginType())) {
            return phoneLogin(request);
        } else if ("wechat".equals(request.getLoginType())) {
            return wechatLogin(request);
        } else {
            throw new RuntimeException("不支持的登录类型");
        }
    }

    /**
     * 手机号登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    private LoginResponse phoneLogin(LoginRequest request) {
        if (request.getPhone() == null || request.getPassword() == null) {
            throw new RuntimeException("手机号和密码不能为空");
        }

        // 查找用户
        User user = userRepository.findByPhone(request.getPhone())
                .orElseThrow(() -> new RuntimeException("手机号或密码错误"));

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("手机号或密码错误");
        }

        // 检查用户状态
        if (!user.isEnabled()) {
            throw new RuntimeException("账户已被禁用");
        }

        // 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);

        log.info("用户登录成功: userId={}, phone={}", user.getId(), user.getPhone());

        // 生成令牌
        return generateTokenResponse(user);
    }

    /**
     * 微信登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    private LoginResponse wechatLogin(LoginRequest request) {
        if (request.getWechatCode() == null) {
            throw new RuntimeException("微信授权码不能为空");
        }

        // TODO: 实现微信OAuth登录逻辑
        // 1. 使用授权码获取access_token
        // 2. 使用access_token获取用户信息
        // 3. 查找或创建用户
        
        throw new RuntimeException("微信登录功能暂未实现");
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的令牌响应
     */
    public LoginResponse refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌
            String username = jwtUtil.getUsernameFromToken(refreshToken);
            if (username == null || jwtUtil.isTokenExpired(refreshToken)) {
                throw new RuntimeException("刷新令牌无效或已过期");
            }

            // 查找用户
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 生成新的令牌
            return generateTokenResponse(user);
        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage());
            throw new RuntimeException("刷新令牌失败");
        }
    }

    /**
     * 生成令牌响应
     *
     * @param user 用户
     * @return 令牌响应
     */
    private LoginResponse generateTokenResponse(User user) {
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

        UserDto userDto = UserDto.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatarUrl(user.getAvatarUrl())
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .roles(user.getRoles().stream()
                        .map(Role::getName)
                        .collect(Collectors.toSet()))
                .build();

        return LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(86400L) // 24小时
                .user(userDto)
                .build();
    }

    /**
     * 初始化测试账号
     */
    @Transactional
    public void initTestAccount() {
        String testPhone = "***********";
        String testPassword = "123456";

        // 检查测试账号是否已存在
        if (userRepository.findByPhone(testPhone).isPresent()) {
            log.info("测试账号已存在: {}", testPhone);
            return;
        }

        // 创建测试账号
        User testUser = User.builder()
                .username(testPhone)
                .password(passwordEncoder.encode(testPassword))
                .nickname("测试用户")
                .phone(testPhone)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .roles(new HashSet<>())
                .build();

        // 添加默认角色
        Optional<Role> userRole = roleRepository.findByName("ROLE_USER");
        if (userRole.isPresent()) {
            testUser.getRoles().add(userRole.get());
        }

        userRepository.save(testUser);
        log.info("测试账号创建成功: phone={}, password={}", testPhone, testPassword);
    }
}
