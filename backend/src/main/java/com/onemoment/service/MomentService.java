package com.onemoment.service;

import com.onemoment.dto.MomentDto;
import com.onemoment.dto.MomentRequest;
import com.onemoment.dto.OssUploadResponse;
import com.onemoment.model.Moment;
import com.onemoment.model.Tag;
import com.onemoment.model.User;
import com.onemoment.repository.MomentRepository;
import com.onemoment.repository.TagRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 瞬间服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MomentService {

    private final MomentRepository momentRepository;
    private final TagRepository tagRepository;
    private final StorageService storageService;
    private final ContentSafetyService contentSafetyService;
    private final AIService aiService;

    /**
     * 获取用户的瞬间列表
     *
     * @param user     用户
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Transactional(readOnly = true)
    public Page<MomentDto> getMomentsByUser(User user, Pageable pageable) {
        return momentRepository.findByUserAndIsDeletedFalseOrderByCreatedAtDesc(user, pageable)
                .map(this::mapToMomentDto);
    }

    /**
     * 获取指定日期的瞬间
     *
     * @param user 用户
     * @param date 日期
     * @return 瞬间列表
     */
    @Transactional(readOnly = true)
    public List<MomentDto> getMomentsByDate(User user, LocalDate date) {
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        return getMomentsByDateRange(user, startOfDay.toLocalDate(), endOfDay.toLocalDate());
    }

    /**
     * 获取指定日期范围的瞬间
     *
     * @param user      用户
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 瞬间列表
     */
    @Transactional(readOnly = true)
    public List<MomentDto> getMomentsByDateRange(User user, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startOfDay = startDate.atStartOfDay();
        LocalDateTime endOfDay = endDate.atTime(LocalTime.MAX);

        return momentRepository.findByUserAndIsDeletedFalseAndCreatedAtBetweenOrderByCreatedAtDesc(
                        user, startOfDay, endOfDay).stream()
                .map(this::mapToMomentDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定标签的瞬间
     *
     * @param user     用户
     * @param tag      标签
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Transactional(readOnly = true)
    public Page<MomentDto> getMomentsByTag(User user, String tag, Pageable pageable) {
        return momentRepository.findByUserAndTagName(user, tag, pageable)
                .map(this::mapToMomentDto);
    }

    /**
     * 搜索瞬间
     *
     * @param user     用户
     * @param keyword  关键词
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Transactional(readOnly = true)
    public Page<MomentDto> searchMoments(User user, String keyword, Pageable pageable) {
        return momentRepository.searchByUserAndKeyword(user, keyword, pageable)
                .map(this::mapToMomentDto);
    }

    /**
     * 获取随机瞬间
     *
     * @param user 用户
     * @return 瞬间
     */
    @Transactional(readOnly = true)
    public MomentDto getRandomMoment(User user) {
        Moment moment = momentRepository.findRandomByUser(user.getId());
        if (moment == null) {
            throw new RuntimeException("没有找到瞬间");
        }
        return mapToMomentDto(moment);
    }

    /**
     * 获取指定ID的瞬间
     *
     * @param user     用户
     * @param momentId 瞬间ID
     * @return 瞬间
     */
    @Transactional(readOnly = true)
    public MomentDto getMoment(User user, Long momentId) {
        Moment moment = momentRepository.findById(momentId)
                .orElseThrow(() -> new RuntimeException("瞬间不存在"));

        // 检查权限
        if (!moment.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("没有权限访问此瞬间");
        }

        return mapToMomentDto(moment);
    }

    /**
     * 获取指定ID的瞬间实体
     *
     * @param momentId 瞬间ID
     * @return 瞬间实体
     */
    @Transactional(readOnly = true)
    public Moment getMomentById(Long momentId) {
        return momentRepository.findById(momentId)
                .orElseThrow(() -> new RuntimeException("瞬间不存在"));
    }

    /**
     * 创建瞬间
     *
     * @param user    用户
     * @param request 瞬间请求
     * @return 创建的瞬间
     */
    @Transactional
    public MomentDto createMoment(User user, MomentRequest request) {
        // 检查今日是否已有瞬间
        LocalDate today = LocalDate.now();
        List<MomentDto> todayMoments = getMomentsByDate(user, today);
        if (!todayMoments.isEmpty()) {
            throw new RuntimeException("今日已发布瞬间，请编辑现有瞬间");
        }

        // 检查内容安全
        if (request.getTextContent() != null && !request.getTextContent().isEmpty()) {
            contentSafetyService.checkTextSafety(request.getTextContent());
        }

        // 创建瞬间
        Moment moment = Moment.builder()
                .user(user)
                .contentType(request.getContentType())
                .textContent(request.getTextContent())
                .mediaUrl(request.getMediaUrl())
                .mediaObjectKey(request.getMediaObjectKey())
                .locationName(request.getLocationName())
                .latitude(request.getLatitude())
                .longitude(request.getLongitude())
                .weather(request.getWeather())
                .temperature(request.getTemperature())
                .mood(request.getMood())
                .isDeleted(false)
                .tags(new HashSet<>())
                .build();

        // 处理标签
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            Set<Tag> tags = request.getTags().stream()
                    .map(this::getOrCreateTag)
                    .collect(Collectors.toSet());
            moment.setTags(tags);
        }

        // 保存瞬间
        Moment savedMoment = momentRepository.save(moment);

        // 异步分析内容
        // aiService.analyzeAsync(savedMoment);

        return mapToMomentDto(savedMoment);
    }

    /**
     * 上传媒体文件
     *
     * @param user 用户
     * @param file 媒体文件
     * @return 上传响应，包含文件URL和对象键
     */
    public OssUploadResponse uploadMedia(User user, MultipartFile file) {
        // 检查文件安全
        contentSafetyService.checkFileSafety(file);

        // 上传文件
        try {
            OssUploadResponse response = storageService.uploadFile(user.getId(), file);
            log.info("文件上传成功: userId={}, fileUrl={}, objectKey={}",
                    user.getId(), response.getFileUrl(), response.getObjectKey());
            return response;
        } catch (Exception e) {
            log.error("文件上传失败: userId={}, fileName={}, error={}",
                    user.getId(), file.getOriginalFilename(), e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新瞬间
     *
     * @param user     用户
     * @param momentId 瞬间ID
     * @param request  瞬间请求
     * @return 更新的瞬间
     */
    @Transactional
    public MomentDto updateMoment(User user, Long momentId, MomentRequest request) {
        // 获取瞬间
        Moment moment = momentRepository.findById(momentId)
                .orElseThrow(() -> new RuntimeException("瞬间不存在"));

        // 检查权限
        if (!moment.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("没有权限修改此瞬间");
        }

        // 检查内容安全
        if (request.getTextContent() != null && !request.getTextContent().isEmpty()) {
            contentSafetyService.checkTextSafety(request.getTextContent());
        }

        // 更新瞬间
        // 确保contentType不为null，如果为null则保持原值或设置默认值
        if (request.getContentType() != null && !request.getContentType().isEmpty()) {
            moment.setContentType(request.getContentType());
        } else if (moment.getContentType() == null || moment.getContentType().isEmpty()) {
            // 如果原值也为null，根据是否有媒体内容设置默认值
            if (request.getMediaUrl() != null && !request.getMediaUrl().isEmpty()) {
                moment.setContentType("IMAGE"); // 默认为图片类型
            } else {
                moment.setContentType("TEXT"); // 默认为文本类型
            }
        }

        moment.setTextContent(request.getTextContent());
        moment.setMediaUrl(request.getMediaUrl());
        moment.setMediaObjectKey(request.getMediaObjectKey());
        moment.setLocationName(request.getLocationName());
        moment.setLatitude(request.getLatitude());
        moment.setLongitude(request.getLongitude());
        moment.setWeather(request.getWeather());
        moment.setTemperature(request.getTemperature());
        moment.setMood(request.getMood());

        // 处理标签
        if (request.getTags() != null) {
            Set<Tag> tags = request.getTags().stream()
                    .map(this::getOrCreateTag)
                    .collect(Collectors.toSet());
            moment.setTags(tags);
        }

        // 保存瞬间
        Moment updatedMoment = momentRepository.save(moment);

        return mapToMomentDto(updatedMoment);
    }

    /**
     * 删除瞬间
     *
     * @param user     用户
     * @param momentId 瞬间ID
     */
    @Transactional
    public void deleteMoment(User user, Long momentId) {
        // 获取瞬间
        Moment moment = momentRepository.findById(momentId)
                .orElseThrow(() -> new RuntimeException("瞬间不存在"));

        // 检查权限
        if (!moment.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("没有权限删除此瞬间");
        }

        // 标记为已删除
        moment.setDeleted(true);
        moment.setDeletedAt(LocalDateTime.now());

        // 保存瞬间
        momentRepository.save(moment);
    }

    /**
     * 获取或创建标签
     *
     * @param tagName 标签名
     * @return 标签
     */
    private Tag getOrCreateTag(String tagName) {
        return tagRepository.findByName(tagName)
                .orElseGet(() -> {
                    Tag tag = Tag.builder()
                            .name(tagName)
                            .build();
                    return tagRepository.save(tag);
                });
    }

    /**
     * 将瞬间实体映射为DTO
     *
     * @param moment 瞬间实体
     * @return 瞬间DTO
     */
    private MomentDto mapToMomentDto(Moment moment) {
        return MomentDto.builder()
                .id(moment.getId())
                .userId(moment.getUser().getId())
                .username(moment.getUser().getUsername())
                .contentType(moment.getContentType())
                .textContent(moment.getTextContent())
                .mediaUrl(moment.getMediaUrl())
                .mediaObjectKey(moment.getMediaObjectKey())
                .thumbnailUrl(moment.getThumbnailUrl())
                .createdAt(moment.getCreatedAt())
                .locationName(moment.getLocationName())
                .latitude(moment.getLatitude())
                .longitude(moment.getLongitude())
                .weather(moment.getWeather())
                .temperature(moment.getTemperature())
                .mood(moment.getMood())
                .tags(moment.getTags().stream()
                        .map(Tag::getName)
                        .collect(Collectors.toSet()))
                .reviewStatus(moment.getContentReview() != null ? moment.getContentReview().getStatus() : null)
                .build();
    }
}
