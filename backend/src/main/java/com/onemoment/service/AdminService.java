package com.onemoment.service;

import com.onemoment.dto.MomentDto;
import com.onemoment.dto.ReviewRequest;
import com.onemoment.dto.UserDto;
import com.onemoment.model.ContentReview;
import com.onemoment.model.Moment;
import com.onemoment.model.Role;
import com.onemoment.model.User;
import com.onemoment.repository.ContentReviewRepository;
import com.onemoment.repository.MomentRepository;
import com.onemoment.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员服务实现类
 */
@Service
@RequiredArgsConstructor
public class AdminService {

    private final UserRepository userRepository;
    private final MomentRepository momentRepository;
    private final ContentReviewRepository contentReviewRepository;

    /**
     * 获取用户列表
     *
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    @Transactional(readOnly = true)
    public Page<UserDto> getUsers(Pageable pageable) {
        return userRepository.findAll(pageable)
                .map(this::mapToUserDto);
    }

    /**
     * 获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    @Transactional(readOnly = true)
    public UserDto getUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return mapToUserDto(user);
    }

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     */
    @Transactional
    public void disableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setEnabled(false);
        userRepository.save(user);
    }

    /**
     * 启用用户
     *
     * @param userId 用户ID
     */
    @Transactional
    public void enableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setEnabled(true);
        userRepository.save(user);
    }

    /**
     * 获取待审核的瞬间列表
     *
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Transactional(readOnly = true)
    public Page<MomentDto> getPendingMoments(Pageable pageable) {
        return momentRepository.findPendingReview(pageable)
                .map(this::mapToMomentDto);
    }

    /**
     * 审核瞬间
     *
     * @param momentId 瞬间ID
     * @param request  审核请求
     */
    @Transactional
    public void reviewMoment(Long momentId, ReviewRequest request) {
        Moment moment = momentRepository.findById(momentId)
                .orElseThrow(() -> new RuntimeException("瞬间不存在"));

        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User reviewer = (User) authentication.getPrincipal();

        // 创建或更新审核记录
        ContentReview review = moment.getContentReview();
        if (review == null) {
            review = ContentReview.builder()
                    .moment(moment)
                    .reviewer(reviewer)
                    .status(request.getStatus())
                    .reason(request.getReason())
                    .build();
        } else {
            review.setReviewer(reviewer);
            review.setStatus(request.getStatus());
            review.setReason(request.getReason());
            review.setUpdatedAt(LocalDateTime.now());
        }

        // 保存审核记录
        contentReviewRepository.save(review);

        // 如果审核不通过，标记瞬间为已删除
        if ("REJECTED".equals(request.getStatus())) {
            moment.setDeleted(true);
            moment.setDeletedAt(LocalDateTime.now());
            momentRepository.save(moment);
        }
    }

    /**
     * 获取系统统计数据
     *
     * @return 统计数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();

        // 用户统计
        stats.put("totalUsers", userRepository.count());
        stats.put("activeUsers", userRepository.countByEnabled(true));

        // 瞬间统计
        stats.put("totalMoments", momentRepository.count());
        stats.put("pendingReviews", momentRepository.countPendingReview());

        // 其他统计数据...

        return stats;
    }

    /**
     * 将用户实体映射为DTO
     *
     * @param user 用户实体
     * @return 用户DTO
     */
    private UserDto mapToUserDto(User user) {
        return UserDto.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatarUrl(user.getAvatarUrl())
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .roles(user.getRoles().stream()
                        .map(Role::getName)
                        .collect(Collectors.toSet()))
                .build();
    }

    /**
     * 将瞬间实体映射为DTO
     *
     * @param moment 瞬间实体
     * @return 瞬间DTO
     */
    private MomentDto mapToMomentDto(Moment moment) {
        return MomentDto.builder()
                .id(moment.getId())
                .userId(moment.getUser().getId())
                .username(moment.getUser().getUsername())
                .contentType(moment.getContentType())
                .textContent(moment.getTextContent())
                .mediaUrl(moment.getMediaUrl())
                .thumbnailUrl(moment.getThumbnailUrl())
                .createdAt(moment.getCreatedAt())
                .locationName(moment.getLocationName())
                .latitude(moment.getLatitude())
                .longitude(moment.getLongitude())
                .weather(moment.getWeather())
                .temperature(moment.getTemperature())
                .mood(moment.getMood())
                .tags(moment.getTags().stream()
                        .map(tag -> tag.getName())
                        .collect(Collectors.toSet()))
                .reviewStatus(moment.getContentReview() != null ? moment.getContentReview().getStatus() : null)
                .build();
    }
}
