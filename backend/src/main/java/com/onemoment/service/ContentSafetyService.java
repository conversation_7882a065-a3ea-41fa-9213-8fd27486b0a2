package com.onemoment.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 内容安全服务接口
 */
public interface ContentSafetyService {

    /**
     * 检查文本内容安全
     *
     * @param text 文本内容
     * @return 是否安全
     */
    boolean checkTextSafety(String text);

    /**
     * 检查文件内容安全
     *
     * @param file 文件
     * @return 是否安全
     */
    boolean checkFileSafety(MultipartFile file);

    /**
     * 检查URL内容安全
     *
     * @param url URL
     * @return 是否安全
     */
    boolean checkUrlSafety(String url);

    /**
     * 检查是否包含敏感词
     *
     * @param text 文本内容
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWords(String text);

    /**
     * 检查是否包含心理危机关键词
     *
     * @param text 文本内容
     * @return 是否包含心理危机关键词
     */
    boolean containsCrisisKeywords(String text);
}
