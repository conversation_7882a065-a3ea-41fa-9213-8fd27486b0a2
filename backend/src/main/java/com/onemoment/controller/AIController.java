package com.onemoment.controller;

import com.onemoment.dto.ApiResponse;
import com.onemoment.model.User;
import com.onemoment.service.AIService;
import com.onemoment.service.DefaultUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI控制器
 * 处理与AI服务相关的请求
 */
@RestController
@RequestMapping("/ai")
@RequiredArgsConstructor
@Slf4j
public class AIController {

    private final AIService aiService;
    private final DefaultUserService defaultUserService;

    /**
     * 分析瞬间内容
     *
     * @param request 包含内容的请求
     * @return 分析结果
     */
    @PostMapping("/analyzeMoment")
    public ResponseEntity<ApiResponse<Map<String, Object>>> analyzeMoment(@RequestBody Map<String, String> request) {
        String content = request.get("content");
        User user = defaultUserService.getDefaultUser();
        log.info("分析瞬间内容请求: userId={}, contentLength={}", user.getId(), content.length());

        try {
            // 分析情绪
            String mood = aiService.analyzeMood(content);

            // 提取关键词
            List<String> keywords = aiService.extractKeywords(content);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("mood", mood);
            result.put("keywords", keywords);
            result.put("analysis", "AI分析了您的内容，感受到了您的" + (mood != null ? mood : "情绪") + "。");

            return ResponseEntity.ok(ApiResponse.success("分析成功", result));
        } catch (Exception e) {
            log.error("分析瞬间内容失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("分析内容失败: " + e.getMessage()));
        }
    }

    /**
     * 与AI聊天
     *
     * @param request 包含消息的请求
     * @return AI回复
     */
    @PostMapping("/chat")
    public ResponseEntity<ApiResponse<Map<String, Object>>> chat(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        User user = defaultUserService.getDefaultUser();
        log.info("AI聊天请求: userId={}, messageLength={}", user.getId(), message.length());

        try {
            // 调用AI服务
            String response = aiService.chat(user.getId(), message);

            // 确保响应不为空
            if (response == null || response.isEmpty()) {
                log.warn("AI服务返回空响应，使用默认回复");
                response = "抱歉，我暂时无法回应您的消息。请稍后再试。";
            }

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("message", response);
            result.put("timestamp", System.currentTimeMillis());

            log.info("AI聊天响应: {}", response.substring(0, Math.min(response.length(), 100)) + (response.length() > 100 ? "..." : ""));
            return ResponseEntity.ok(ApiResponse.success("聊天成功", result));
        } catch (Exception e) {
            log.error("AI聊天失败: userId={}, error={}", user.getId(), e.getMessage(), e);

            // 构建错误响应，但仍然返回200状态码和有效的数据结构
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("message", "抱歉，我暂时无法回应您的消息。请稍后再试。(错误: " + e.getMessage() + ")");
            errorResult.put("timestamp", System.currentTimeMillis());
            errorResult.put("error", e.getMessage());

            return ResponseEntity.ok(ApiResponse.success("聊天失败，但返回了默认回复", errorResult));
        }
    }

    /**
     * 获取当前AI模型
     *
     * @return 当前AI模型信息
     */
    @PostMapping("/getCurrentModel")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentModel() {
        User user = defaultUserService.getDefaultUser();
        log.info("获取当前AI模型请求: userId={}", user.getId());

        try {
            // 构建响应
            Map<String, Object> model = new HashMap<>();
            model.put("id", "1");
            model.put("name", "通义千问");
            model.put("provider", "通义千问");
            model.put("description", "阿里云通义千问大模型");
            model.put("features", List.of("文本分析", "情感识别", "聊天"));

            return ResponseEntity.ok(ApiResponse.success("获取成功", model));
        } catch (Exception e) {
            log.error("获取当前AI模型失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取当前AI模型失败: " + e.getMessage()));
        }
    }

    /**
     * 获取可用的AI模型列表
     *
     * @return AI模型列表
     */
    @PostMapping("/getAvailableModels")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getAvailableModels() {
        User user = defaultUserService.getDefaultUser();
        log.info("获取可用AI模型列表请求: userId={}", user.getId());

        try {
            // 构建响应
            Map<String, Object> model1 = new HashMap<>();
            model1.put("id", "1");
            model1.put("name", "通义千问");
            model1.put("provider", "通义千问");
            model1.put("description", "阿里云通义千问大模型");
            model1.put("features", List.of("文本分析", "情感识别", "聊天"));

            List<Map<String, Object>> models = List.of(model1);

            return ResponseEntity.ok(ApiResponse.success("获取成功", models));
        } catch (Exception e) {
            log.error("获取可用AI模型列表失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取可用AI模型列表失败: " + e.getMessage()));
        }
    }
}
