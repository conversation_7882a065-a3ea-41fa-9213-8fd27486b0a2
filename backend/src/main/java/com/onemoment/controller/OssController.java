package com.onemoment.controller;

import com.onemoment.dto.ApiResponse;
import com.onemoment.dto.OssUploadResponse;
import com.onemoment.model.User;
import com.onemoment.service.DefaultUserService;
import com.onemoment.service.StorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * OSS控制器
 * 处理与阿里云OSS相关的请求
 */
@RestController
@RequestMapping("/oss")
@RequiredArgsConstructor
@Slf4j
public class OssController {

    private final StorageService storageService;
    private final DefaultUserService defaultUserService;

    /**
     * 获取OSS配置
     * 用于前端直传文件到OSS
     *
     * @return OSS配置
     */
    @PostMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOssConfig() {
        User user = defaultUserService.getDefaultUser();
        log.info("获取OSS配置请求: userId={}", user.getId());

        try {
            // 生成上传凭证
            String uploadToken = storageService.generateUploadToken(user.getId(), "temp.jpg", "image/jpeg");

            // 解析上传凭证
            Map<String, Object> config = new HashMap<>();
            config.put("data", uploadToken);

            return ResponseEntity.ok(ApiResponse.success("获取OSS配置成功", config));
        } catch (Exception e) {
            log.error("获取OSS配置失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取OSS配置失败: " + e.getMessage()));
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ResponseEntity<ApiResponse<OssUploadResponse>> upload(@RequestParam("file") MultipartFile file) {
        User user = defaultUserService.getDefaultUser();
        log.info("上传文件到OSS请求: userId={}, fileName={}, fileSize={}, contentType={}",
                user.getId(), file.getOriginalFilename(), file.getSize(), file.getContentType());

        try {
            OssUploadResponse response = storageService.uploadFile(user.getId(), file);
            log.info("文件上传成功: userId={}, fileUrl={}", user.getId(), response.getFileUrl());
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", response));
        } catch (Exception e) {
            log.error("上传文件失败: userId={}, fileName={}, error={}",
                    user.getId(), file.getOriginalFilename(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("上传文件失败: " + e.getMessage()));
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param request 包含对象键的请求
     * @return 文件访问URL
     */
    @PostMapping("/getFileUrl")
    public ResponseEntity<ApiResponse<String>> getFileUrl(@RequestBody Map<String, String> request) {
        String objectKey = request.get("objectKey");
        int expiration = Integer.parseInt(request.getOrDefault("expiration", "3600"));

        log.info("获取文件访问URL请求: objectKey={}, expiration={}", objectKey, expiration);

        try {
            String fileUrl = storageService.generatePresignedUrl(objectKey, expiration);
            return ResponseEntity.ok(ApiResponse.success("获取文件访问URL成功", fileUrl));
        } catch (Exception e) {
            log.error("获取文件访问URL失败: objectKey={}, error={}", objectKey, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取文件访问URL失败: " + e.getMessage()));
        }
    }

    /**
     * 删除文件
     *
     * @param request 包含对象键的请求
     * @return 删除结果
     */
    @PostMapping("/deleteFile")
    public ResponseEntity<ApiResponse<Void>> deleteFile(@RequestBody Map<String, String> request) {
        String objectKey = request.get("objectKey");
        log.info("删除文件请求: objectKey={}", objectKey);

        try {
            storageService.deleteFile(objectKey);
            return ResponseEntity.ok(ApiResponse.success("删除文件成功", null));
        } catch (Exception e) {
            log.error("删除文件失败: objectKey={}, error={}", objectKey, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("删除文件失败: " + e.getMessage()));
        }
    }

    /**
     * 下载文件
     * 从阿里云OSS下载文件并返回给客户端
     *
     * @param objectKey 对象键
     * @return 文件内容
     */
    @GetMapping("/download/{objectKey}")
    public ResponseEntity<?> downloadFile(@PathVariable String objectKey) {
        log.info("下载文件请求: objectKey={}", objectKey);

        try {
            // 获取文件内容和元数据
            Map<String, Object> result = storageService.downloadFile(objectKey);

            byte[] fileContent = (byte[]) result.get("content");
            String contentType = (String) result.get("contentType");
            String fileName = (String) result.get("fileName");

            // 设置响应头
            return ResponseEntity.ok()
                    .header("Content-Disposition", "inline; filename=\"" + fileName + "\"")
                    .contentType(org.springframework.http.MediaType.parseMediaType(contentType))
                    .body(fileContent);
        } catch (Exception e) {
            log.error("下载文件失败: objectKey={}, error={}", objectKey, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("下载文件失败: " + e.getMessage()));
        }
    }
}
