package com.onemoment.controller;

import com.onemoment.dto.ApiResponse;
import com.onemoment.dto.MomentDto;
import com.onemoment.dto.MomentRequest;
import com.onemoment.dto.OssUploadResponse;
import com.onemoment.model.Moment;
import com.onemoment.model.User;
import com.onemoment.service.DefaultUserService;
import com.onemoment.service.MomentService;
import com.onemoment.service.StorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.HashSet;

/**
 * 瞬间控制器
 */
@RestController
@RequestMapping("/moments")
@RequiredArgsConstructor
@Slf4j
public class MomentController {

    private final MomentService momentService;
    private final DefaultUserService defaultUserService;
    private final StorageService storageService;

    /**
     * 获取用户的瞬间列表
     */
    @PostMapping("/getMomentList")
    public ResponseEntity<Page<MomentDto>> getMoments(
            @RequestBody(required = false) Map<String, Object> request,
            @PageableDefault(size = 10) Pageable pageable) {
        User user = defaultUserService.getDefaultUser();
        log.info("获取用户瞬间列表: userId={}, page={}, size={}",
                user.getId(), pageable.getPageNumber(), pageable.getPageSize());

        Page<MomentDto> moments = momentService.getMomentsByUser(user, pageable);
        return ResponseEntity.ok(moments);
    }

    /**
     * 获取指定日期的瞬间
     */
    @PostMapping("/getMomentsByDate")
    public ResponseEntity<List<MomentDto>> getMomentsByDate(
            @RequestBody Map<String, Object> request) {
        User user = defaultUserService.getDefaultUser();
        LocalDate date = LocalDate.parse((String) request.get("date"));
        return ResponseEntity.ok(momentService.getMomentsByDate(user, date));
    }

    /**
     * 获取指定日期范围的瞬间
     */
    @PostMapping("/getMomentsByDateRange")
    public ResponseEntity<List<MomentDto>> getMomentsByDateRange(
            @RequestBody Map<String, Object> request) {
        User user = defaultUserService.getDefaultUser();
        LocalDate startDate = LocalDate.parse((String) request.get("startDate"));
        LocalDate endDate = LocalDate.parse((String) request.get("endDate"));
        return ResponseEntity.ok(momentService.getMomentsByDateRange(user, startDate, endDate));
    }

    /**
     * 获取指定标签的瞬间
     */
    @PostMapping("/getMomentsByTag")
    public ResponseEntity<Page<MomentDto>> getMomentsByTag(
            @RequestBody Map<String, Object> request,
            @PageableDefault(size = 10) Pageable pageable) {
        User user = defaultUserService.getDefaultUser();
        String tag = (String) request.get("tag");
        return ResponseEntity.ok(momentService.getMomentsByTag(user, tag, pageable));
    }

    /**
     * 搜索瞬间
     */
    @PostMapping("/searchMoments")
    public ResponseEntity<Page<MomentDto>> searchMoments(
            @RequestBody Map<String, Object> request,
            @PageableDefault(size = 10) Pageable pageable) {
        User user = defaultUserService.getDefaultUser();
        String keyword = (String) request.get("keyword");
        return ResponseEntity.ok(momentService.searchMoments(user, keyword, pageable));
    }

    /**
     * 获取随机瞬间
     */
    @PostMapping("/getRandomMoment")
    public ResponseEntity<MomentDto> getRandomMoment() {
        User user = defaultUserService.getDefaultUser();
        return ResponseEntity.ok(momentService.getRandomMoment(user));
    }

    /**
     * 获取指定ID的瞬间
     */
    @PostMapping("/getMomentById")
    public ResponseEntity<MomentDto> getMoment(
            @RequestBody Map<String, Object> request) {
        User user = defaultUserService.getDefaultUser();
        Long momentId = Long.valueOf(request.get("momentId").toString());
        return ResponseEntity.ok(momentService.getMoment(user, momentId));
    }

    /**
     * 创建瞬间
     */
    @PostMapping("/createMoment")
    public ResponseEntity<ApiResponse<MomentDto>> createMoment(
            @RequestBody MomentRequest request) {
        User user = defaultUserService.getDefaultUser();
        log.info("创建瞬间请求: userId={}, contentType={}, hasMediaUrl={}",
                user.getId(), request.getContentType(), (request.getMediaUrl() != null && !request.getMediaUrl().isEmpty()));

        try {
            MomentDto createdMoment = momentService.createMoment(user, request);
            log.info("瞬间创建成功: userId={}, momentId={}", user.getId(), createdMoment.getId());
            return ResponseEntity.ok(ApiResponse.success("瞬间创建成功", createdMoment));
        } catch (RuntimeException e) {
            log.error("创建瞬间失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建瞬间失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("创建瞬间失败: " + e.getMessage()));
        }
    }

    /**
     * 上传媒体文件
     */
    @PostMapping("/upload")
    public ResponseEntity<ApiResponse<OssUploadResponse>> upload(
            @RequestParam("file") MultipartFile file) {
        User user = defaultUserService.getDefaultUser();
        log.info("上传媒体文件请求: userId={}, fileName={}, fileSize={}, contentType={}",
                user.getId(), file.getOriginalFilename(), file.getSize(), file.getContentType());

        try {
            OssUploadResponse response = momentService.uploadMedia(user, file);
            log.info("媒体文件上传成功: userId={}, mediaUrl={}, objectKey={}",
                    user.getId(), response.getFileUrl(), response.getObjectKey());
            return ResponseEntity.ok(ApiResponse.success("媒体文件上传成功", response));
        } catch (Exception e) {
            log.error("上传媒体文件失败: userId={}, fileName={}, error={}",
                    user.getId(), file.getOriginalFilename(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("上传媒体文件失败: " + e.getMessage()));
        }
    }

    /**
     * 更新瞬间
     */
    @PostMapping("/updateMoment")
    public ResponseEntity<MomentDto> updateMoment(
            @RequestBody Map<String, Object> request) {
        User user = defaultUserService.getDefaultUser();
        Long momentId = Long.valueOf(request.get("momentId").toString());
        MomentRequest momentRequest = convertToMomentRequest(request);
        return ResponseEntity.ok(momentService.updateMoment(user, momentId, momentRequest));
    }

    /**
     * 删除瞬间
     */
    @PostMapping("/deleteMoment")
    public ResponseEntity<?> deleteMoment(
            @RequestBody Map<String, Object> request) {
        try {
            User user = defaultUserService.getDefaultUser();
            Long momentId = Long.valueOf(request.get("momentId").toString());
            log.info("删除瞬间: {}", momentId);

            // 获取瞬间信息，用于删除媒体文件
            Moment moment = momentService.getMomentById(momentId);
            if (moment != null && moment.getMediaObjectKey() != null && !moment.getMediaObjectKey().isEmpty()) {
                // 删除OSS中的媒体文件
                try {
                    storageService.deleteFile(moment.getMediaObjectKey());
                    log.info("删除瞬间媒体文件成功: {}", moment.getMediaObjectKey());
                } catch (Exception e) {
                    log.error("删除瞬间媒体文件失败: {}", moment.getMediaObjectKey(), e);
                    // 继续删除瞬间记录，即使媒体文件删除失败
                }
            }

            // 删除瞬间记录
            momentService.deleteMoment(user, momentId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "瞬间删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除瞬间失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除瞬间失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取媒体文件
     */
    @GetMapping("/media/{objectKey}")
    public ResponseEntity<?> getMedia(@PathVariable String objectKey) {
        try {
            log.info("获取媒体文件: {}", objectKey);

            // 对objectKey进行URL解码
            String decodedObjectKey = java.net.URLDecoder.decode(objectKey, "UTF-8");
            log.info("解码后的媒体文件路径: {}", decodedObjectKey);

            // 构建完整的文件路径：moment/uid/文件名
            // 目前使用固定的用户ID 5，实际应用中应该根据当前用户或请求参数获取
            String fullPath = "moment/5/" + decodedObjectKey;
            log.info("构建完整的文件路径: {}", fullPath);

            // 从OSS获取文件
            Map<String, Object> fileData = storageService.downloadFile(fullPath);

            if (fileData == null || !fileData.containsKey("content")) {
                log.warn("媒体文件不存在或为空: {}", fullPath);
                return ResponseEntity.notFound().build();
            }

            byte[] fileContent = (byte[]) fileData.get("content");
            if (fileContent == null || fileContent.length == 0) {
                log.warn("媒体文件内容为空: {}", fullPath);
                return ResponseEntity.notFound().build();
            }

            // 获取内容类型
            String contentType = (String) fileData.get("contentType");
            if (contentType == null || contentType.isEmpty()) {
                // 根据文件扩展名确定Content-Type
                contentType = "application/octet-stream"; // 默认
                if (decodedObjectKey.toLowerCase().endsWith(".jpg") || decodedObjectKey.toLowerCase().endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (decodedObjectKey.toLowerCase().endsWith(".png")) {
                    contentType = "image/png";
                } else if (decodedObjectKey.toLowerCase().endsWith(".gif")) {
                    contentType = "image/gif";
                } else if (decodedObjectKey.toLowerCase().endsWith(".mp4")) {
                    contentType = "video/mp4";
                } else if (decodedObjectKey.toLowerCase().endsWith(".mp3")) {
                    contentType = "audio/mpeg";
                }
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(fileContent);
        } catch (Exception e) {
            log.error("获取媒体文件失败: {}", objectKey, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("获取媒体文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载图片
     */
    @PostMapping("/downloadImage")
    public ResponseEntity<ApiResponse<String>> downloadImage(@RequestBody Map<String, String> request) {
        User user = defaultUserService.getDefaultUser();
        String objectKey = request.get("objectKey");
        log.info("下载图片请求: userId={}, objectKey={}", user.getId(), objectKey);

        try {
            // 生成预签名URL，有效期1小时
            String imageUrl = storageService.generatePresignedUrl(objectKey, 3600);
            log.info("生成图片下载URL成功: userId={}, objectKey={}, url={}",
                    user.getId(), objectKey, imageUrl);
            return ResponseEntity.ok(ApiResponse.success("获取图片URL成功", imageUrl));
        } catch (Exception e) {
            log.error("下载图片失败: userId={}, objectKey={}, error={}",
                    user.getId(), objectKey, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("下载图片失败: " + e.getMessage()));
        }
    }

    /**
     * 获取隐私协议
     */
    @PostMapping("/privacy-policy")
    public ResponseEntity<ApiResponse<String>> getPrivacyPolicy() {
        log.info("获取隐私协议请求");

        try {
            String privacyPolicy = """
                # 隐私政策

                ## 1. 信息收集
                我们收集您在使用One Moment应用时提供的信息，包括但不限于：
                - 您创建的瞬间内容（文字、图片等）
                - 位置信息（如果您选择分享）
                - 设备信息和使用数据

                ## 2. 信息使用
                我们使用收集的信息来：
                - 提供和改进我们的服务
                - 个性化您的体验
                - 确保应用安全

                ## 3. 信息分享
                我们不会向第三方出售、交易或转让您的个人信息，除非：
                - 获得您的明确同意
                - 法律要求
                - 保护我们的权利和安全

                ## 4. 数据安全
                我们采用适当的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。

                ## 5. 您的权利
                您有权：
                - 访问您的个人信息
                - 更正不准确的信息
                - 删除您的账户和数据
                - 限制信息处理

                ## 6. Cookie和跟踪技术
                我们可能使用Cookie和类似技术来改善用户体验和分析应用使用情况。

                ## 7. 儿童隐私
                我们的服务不面向13岁以下的儿童。我们不会故意收集儿童的个人信息。

                ## 8. 政策更新
                我们可能会不时更新此隐私政策。重大变更将通过应用内通知告知您。

                ## 9. 联系我们
                如果您对此隐私政策有任何疑问，请通过以下方式联系我们：
                - 邮箱：<EMAIL>
                - 地址：北京市朝阳区xxx路xxx号

                最后更新日期：2024年1月1日
                """;

            log.info("隐私协议获取成功");
            return ResponseEntity.ok(ApiResponse.success("获取隐私协议成功", privacyPolicy));
        } catch (Exception e) {
            log.error("获取隐私协议失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取隐私协议失败: " + e.getMessage()));
        }
    }

    /**
     * 将请求转换为MomentRequest对象
     */
    private MomentRequest convertToMomentRequest(Map<String, Object> request) {
        MomentRequest momentRequest = new MomentRequest();
        if (request.containsKey("contentType")) momentRequest.setContentType((String) request.get("contentType"));
        if (request.containsKey("textContent")) momentRequest.setTextContent((String) request.get("textContent"));
        if (request.containsKey("mediaUrl")) momentRequest.setMediaUrl((String) request.get("mediaUrl"));
        if (request.containsKey("locationName")) momentRequest.setLocationName((String) request.get("locationName"));
        if (request.containsKey("latitude")) momentRequest.setLatitude((Double) request.get("latitude"));
        if (request.containsKey("longitude")) momentRequest.setLongitude((Double) request.get("longitude"));
        if (request.containsKey("weather")) momentRequest.setWeather((String) request.get("weather"));
        if (request.containsKey("temperature")) momentRequest.setTemperature((Double) request.get("temperature"));
        if (request.containsKey("mood")) momentRequest.setMood((String) request.get("mood"));
        if (request.containsKey("tags")) momentRequest.setTags(new HashSet<>((List<String>) request.get("tags")));
        return momentRequest;
    }
}
