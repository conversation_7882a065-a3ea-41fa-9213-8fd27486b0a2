package com.onemoment.controller;

import com.onemoment.dto.ApiResponse;
import com.onemoment.dto.WeatherDto;
import com.onemoment.service.WeatherService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

/**
 * 天气控制器
 */
@RestController
@RequestMapping("/weather")
@RequiredArgsConstructor
@Slf4j
public class WeatherController {

    private final WeatherService weatherService;

    /**
     * 获取最新天气
     */
    @PostMapping("/getLatestWeather")
    public ResponseEntity<ApiResponse<WeatherDto>> getLatestWeather(@RequestBody Map<String, String> request) {
        String cityName = request.getOrDefault("cityName", "北京");
        log.info("获取最新天气请求: cityName={}", cityName);

        try {
            WeatherDto weather = weatherService.getLatestWeather(cityName);
            return ResponseEntity.ok(ApiResponse.success("获取天气成功", weather));
        } catch (Exception e) {
            log.error("获取天气失败: cityName={}, error={}", cityName, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取天气失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定日期的天气
     */
    @PostMapping("/getWeatherByDate")
    public ResponseEntity<ApiResponse<WeatherDto>> getWeatherByDate(@RequestBody Map<String, Object> request) {
        String cityName = (String) request.getOrDefault("cityName", "北京");
        String dateStr = (String) request.get("date");

        LocalDate date;
        try {
            date = LocalDate.parse(dateStr);
        } catch (Exception e) {
            date = LocalDate.now();
        }

        log.info("获取指定日期天气请求: cityName={}, date={}", cityName, date);

        try {
            Optional<WeatherDto> weatherOpt = weatherService.getWeatherByDate(cityName, date);

            if (weatherOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取指定日期天气成功", weatherOpt.get()));
            } else {
                // 如果没有找到指定日期的天气，返回当前天气数据
                WeatherDto currentWeather = weatherService.getLatestWeather(cityName);
                return ResponseEntity.ok(ApiResponse.success("未找到指定日期天气，返回当前天气", currentWeather));
            }
        } catch (Exception e) {
            log.error("获取指定日期天气失败: cityName={}, date={}, error={}", cityName, date, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取指定日期天气失败: " + e.getMessage()));
        }
    }

    /**
     * 更新天气（仅用于测试）
     */
    @PostMapping("/updateWeather")
    public ResponseEntity<ApiResponse<WeatherDto>> updateWeather(@RequestBody WeatherDto weatherDto) {
        log.info("更新天气请求: {}", weatherDto);

        try {
            WeatherDto updatedWeather = weatherService.updateWeather(weatherDto);
            return ResponseEntity.ok(ApiResponse.success("更新天气成功", updatedWeather));
        } catch (Exception e) {
            log.error("更新天气失败: error={}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("更新天气失败: " + e.getMessage()));
        }
    }
}
