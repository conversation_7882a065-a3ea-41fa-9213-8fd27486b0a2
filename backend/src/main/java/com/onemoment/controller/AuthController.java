package com.onemoment.controller;

import com.onemoment.dto.*;
import com.onemoment.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {

    private final AuthService authService;

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册响应
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<LoginResponse>> register(@Valid @RequestBody RegisterRequest request) {
        log.info("用户注册请求: phone={}", request.getPhone());

        try {
            LoginResponse response = authService.register(request);
            return ResponseEntity.ok(ApiResponse.success("注册成功", response));
        } catch (RuntimeException e) {
            log.error("用户注册失败: phone={}, error={}", request.getPhone(), e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("用户注册失败: phone={}, error={}", request.getPhone(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("注册失败: " + e.getMessage()));
        }
    }

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: loginType={}, phone={}", request.getLoginType(), request.getPhone());

        try {
            LoginResponse response = authService.login(request);
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));
        } catch (RuntimeException e) {
            log.error("用户登录失败: loginType={}, phone={}, error={}", 
                    request.getLoginType(), request.getPhone(), e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("用户登录失败: loginType={}, phone={}, error={}", 
                    request.getLoginType(), request.getPhone(), e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("登录失败: " + e.getMessage()));
        }
    }

    /**
     * 刷新令牌
     *
     * @param request 包含刷新令牌的请求
     * @return 新的令牌响应
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        log.info("刷新令牌请求");

        try {
            LoginResponse response = authService.refreshToken(refreshToken);
            return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", response));
        } catch (RuntimeException e) {
            log.error("刷新令牌失败: error={}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("刷新令牌失败: error={}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("刷新令牌失败: " + e.getMessage()));
        }
    }

    /**
     * 检查登录状态
     *
     * @return 登录状态
     */
    @PostMapping("/checkStatus")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkStatus() {
        // 这个接口主要用于前端检查用户是否需要登录
        // 由于我们只在AI对话时需要登录，所以这里返回一个简单的状态
        Map<String, Object> status = Map.of(
                "loginRequired", false,
                "message", "大部分功能无需登录，仅AI对话功能需要登录"
        );
        return ResponseEntity.ok(ApiResponse.success("状态检查成功", status));
    }

    /**
     * 检查AI功能登录状态
     *
     * @param request 请求头信息
     * @return 登录状态
     */
    @PostMapping("/checkAILoginStatus")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkAILoginStatus(
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        boolean isLoggedIn = false;
        String message = "AI对话功能需要登录";

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // 这里可以添加JWT验证逻辑
            // 暂时简单处理
            isLoggedIn = true;
            message = "已登录，可以使用AI对话功能";
        }

        Map<String, Object> status = Map.of(
                "isLoggedIn", isLoggedIn,
                "loginRequired", !isLoggedIn,
                "message", message
        );

        return ResponseEntity.ok(ApiResponse.success("AI登录状态检查成功", status));
    }

    /**
     * 初始化测试账号
     *
     * @return 初始化结果
     */
    @PostMapping("/initTestAccount")
    public ResponseEntity<ApiResponse<String>> initTestAccount() {
        log.info("初始化测试账号请求");

        try {
            authService.initTestAccount();
            return ResponseEntity.ok(ApiResponse.success("测试账号初始化成功", 
                    "测试账号: ***********, 密码: 123456"));
        } catch (Exception e) {
            log.error("初始化测试账号失败: error={}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("初始化测试账号失败: " + e.getMessage()));
        }
    }
}
