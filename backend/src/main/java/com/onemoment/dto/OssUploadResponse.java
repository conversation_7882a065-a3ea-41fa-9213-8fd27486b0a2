package com.onemoment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OSS上传响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssUploadResponse {
    
    /**
     * 文件URL
     */
    private String fileUrl;
    
    /**
     * 对象键
     */
    private String objectKey;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 内容类型
     */
    private String contentType;
}
