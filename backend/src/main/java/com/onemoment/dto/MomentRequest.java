package com.onemoment.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 瞬间请求DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MomentRequest {

    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    private String textContent;
    private String mediaUrl;
    private String mediaObjectKey;
    private String locationName;
    private Double latitude;
    private Double longitude;
    private String weather;
    private Double temperature;
    private String mood;
    private Set<String> tags;
}
