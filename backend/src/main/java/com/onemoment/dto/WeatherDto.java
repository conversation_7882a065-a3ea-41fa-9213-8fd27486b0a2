package com.onemoment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 天气DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeatherDto {
    private Long id;
    private String cityName;
    private String weatherCondition;
    private Double temperature;
    private Integer humidity;
    private Double windSpeed;
    private String windDirection;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
