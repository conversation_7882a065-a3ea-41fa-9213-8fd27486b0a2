package com.onemoment.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 登录请求DTO
 */
@Data
public class LoginRequest {

    /**
     * 登录类型：phone（手机号登录）、wechat（微信登录）
     */
    @NotBlank(message = "登录类型不能为空")
    private String loginType;

    /**
     * 手机号（手机号登录时必填）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 密码（手机号登录时必填）
     */
    private String password;

    /**
     * 微信授权码（微信登录时必填）
     */
    private String wechatCode;

    /**
     * 微信用户信息（微信登录时可选）
     */
    private String wechatUserInfo;
}
