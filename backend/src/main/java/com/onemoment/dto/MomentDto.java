package com.onemoment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 瞬间DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MomentDto {

    private Long id;
    private Long userId;
    private String username;
    private String contentType;
    private String textContent;
    private String mediaUrl;
    private String mediaObjectKey;
    private String thumbnailUrl;
    private LocalDateTime createdAt;
    private String locationName;
    private Double latitude;
    private Double longitude;
    private String weather;
    private Double temperature;
    private String mood;
    private Set<String> tags;
    private Map<String, Object> aiAnalysis;
    private String reviewStatus;
}
