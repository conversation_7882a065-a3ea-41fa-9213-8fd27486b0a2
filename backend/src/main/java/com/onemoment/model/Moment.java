package com.onemoment.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 瞬间实体类
 */
@Data
@Entity
@Table(name = "moments")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Moment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "content_type", nullable = false)
    private String contentType;

    @Column(name = "text_content")
    private String textContent;

    @Column(name = "media_url")
    private String mediaUrl;

    @Column(name = "media_object_key")
    private String mediaObjectKey;

    @Column(name = "thumbnail_url")
    private String thumbnailUrl;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "location_name")
    private String locationName;

    private Double latitude;

    private Double longitude;

    private String weather;

    private Double temperature;

    private String mood;

    @Column(name = "is_deleted", nullable = false)
    private boolean isDeleted;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "moment_tags",
            joinColumns = @JoinColumn(name = "moment_id"),
            inverseJoinColumns = @JoinColumn(name = "tag_id")
    )
    private Set<Tag> tags = new HashSet<>();

    @OneToMany(mappedBy = "moment", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<AIAnalysis> aiAnalyses = new HashSet<>();

    @OneToOne(mappedBy = "moment", cascade = CascadeType.ALL, orphanRemoval = true)
    private ContentReview contentReview;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
