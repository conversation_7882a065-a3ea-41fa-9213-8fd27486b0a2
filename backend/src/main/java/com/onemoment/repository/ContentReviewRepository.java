package com.onemoment.repository;

import com.onemoment.model.ContentReview;
import com.onemoment.model.Moment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 内容审核记录数据访问接口
 */
@Repository
public interface ContentReviewRepository extends JpaRepository<ContentReview, Long> {

    /**
     * 根据瞬间查找内容审核记录
     *
     * @param moment 瞬间
     * @return 内容审核记录
     */
    Optional<ContentReview> findByMoment(Moment moment);

    /**
     * 根据状态统计内容审核记录数量
     *
     * @param status 状态
     * @return 数量
     */
    long countByStatus(String status);
}
