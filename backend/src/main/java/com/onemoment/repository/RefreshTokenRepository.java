package com.onemoment.repository;

import com.onemoment.model.RefreshToken;
import com.onemoment.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 刷新令牌数据访问接口
 */
@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {

    /**
     * 根据令牌查找刷新令牌
     *
     * @param token 令牌
     * @return 刷新令牌
     */
    Optional<RefreshToken> findByToken(String token);

    /**
     * 根据用户删除刷新令牌
     *
     * @param user 用户
     */
    void deleteByUser(User user);
}
