package com.onemoment.repository;

import com.onemoment.model.AIAnalysis;
import com.onemoment.model.Moment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI分析结果数据访问接口
 */
@Repository
public interface AIAnalysisRepository extends JpaRepository<AIAnalysis, Long> {

    /**
     * 根据瞬间查找AI分析结果
     *
     * @param moment 瞬间
     * @return AI分析结果列表
     */
    List<AIAnalysis> findByMoment(Moment moment);

    /**
     * 根据瞬间和提供商查找AI分析结果
     *
     * @param moment   瞬间
     * @param provider 提供商
     * @return AI分析结果
     */
    Optional<AIAnalysis> findByMomentAndProvider(Moment moment, String provider);

    /**
     * 根据瞬间和提供商和模型查找AI分析结果
     *
     * @param moment   瞬间
     * @param provider 提供商
     * @param model    模型
     * @return AI分析结果
     */
    Optional<AIAnalysis> findByMomentAndProviderAndModel(Moment moment, String provider, String model);
}
