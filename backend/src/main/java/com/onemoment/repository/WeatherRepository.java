package com.onemoment.repository;

import com.onemoment.model.Weather;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 天气数据访问接口
 */
@Repository
public interface WeatherRepository extends JpaRepository<Weather, Long> {

    /**
     * 根据城市名称查找最新的天气信息
     *
     * @param cityName 城市名称
     * @return 天气信息
     */
    @Query("SELECT w FROM Weather w WHERE w.cityName = :cityName ORDER BY w.updatedAt DESC")
    Optional<Weather> findLatestByCityName(@Param("cityName") String cityName);

    /**
     * 查找指定日期范围内的天气信息
     *
     * @param cityName  城市名称
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 天气信息
     */
    Optional<Weather> findFirstByCityNameAndCreatedAtBetweenOrderByCreatedAtDesc(
            String cityName, LocalDateTime startDate, LocalDateTime endDate);
}
