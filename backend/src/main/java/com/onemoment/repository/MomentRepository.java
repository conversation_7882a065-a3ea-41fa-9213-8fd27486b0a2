package com.onemoment.repository;

import com.onemoment.model.Moment;
import com.onemoment.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 瞬间数据访问接口
 */
@Repository
public interface MomentRepository extends JpaRepository<Moment, Long> {

    /**
     * 查找用户的所有瞬间（分页）
     *
     * @param user     用户
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    Page<Moment> findByUserAndIsDeletedFalseOrderByCreatedAtDesc(User user, Pageable pageable);

    /**
     * 查找用户在指定日期范围内的瞬间
     *
     * @param user      用户
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 瞬间列表
     */
    List<Moment> findByUserAndIsDeletedFalseAndCreatedAtBetweenOrderByCreatedAtDesc(
            User user, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 查找用户在指定日期的瞬间
     *
     * @param user      用户
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 瞬间列表
     */
    @Query("SELECT m FROM Moment m WHERE m.user = :user AND m.isDeleted = false " +
            "AND FUNCTION('DATE', m.createdAt) = FUNCTION('DATE', :date) " +
            "ORDER BY m.createdAt DESC")
    List<Moment> findByUserAndDate(@Param("user") User user, @Param("date") LocalDateTime date);

    /**
     * 根据标签名查找用户的瞬间
     *
     * @param user     用户
     * @param tagName  标签名
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Query("SELECT m FROM Moment m JOIN m.tags t WHERE m.user = :user " +
            "AND m.isDeleted = false AND t.name = :tagName ORDER BY m.createdAt DESC")
    Page<Moment> findByUserAndTagName(
            @Param("user") User user, @Param("tagName") String tagName, Pageable pageable);

    /**
     * 搜索用户的瞬间
     *
     * @param user     用户
     * @param keyword  关键词
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Query("SELECT m FROM Moment m LEFT JOIN m.tags t WHERE m.user = :user " +
            "AND m.isDeleted = false AND (m.textContent LIKE %:keyword% OR t.name LIKE %:keyword%) " +
            "GROUP BY m.id ORDER BY m.createdAt DESC")
    Page<Moment> searchByUserAndKeyword(
            @Param("user") User user, @Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取用户的随机瞬间
     *
     * @param user 用户
     * @return 瞬间列表
     */
    @Query(value = "SELECT * FROM moments WHERE user_id = :userId AND is_deleted = false " +
            "ORDER BY RAND() LIMIT 1", nativeQuery = true)
    Moment findRandomByUser(@Param("userId") Long userId);

    /**
     * 获取待审核的瞬间
     *
     * @param pageable 分页参数
     * @return 瞬间分页结果
     */
    @Query("SELECT m FROM Moment m LEFT JOIN m.contentReview cr " +
            "WHERE m.isDeleted = false AND (cr IS NULL OR cr.status = 'PENDING') " +
            "ORDER BY m.createdAt DESC")
    Page<Moment> findPendingReview(Pageable pageable);

    /**
     * 统计待审核的瞬间数量
     *
     * @return 待审核的瞬间数量
     */
    @Query("SELECT COUNT(m) FROM Moment m LEFT JOIN m.contentReview cr " +
            "WHERE m.isDeleted = false AND (cr IS NULL OR cr.status = 'PENDING')")
    long countPendingReview();
}
