package com.onemoment.repository;

import com.onemoment.model.Tag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 标签数据访问接口
 */
@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {

    /**
     * 根据标签名查找标签
     *
     * @param name 标签名
     * @return 标签
     */
    Optional<Tag> findByName(String name);
}
