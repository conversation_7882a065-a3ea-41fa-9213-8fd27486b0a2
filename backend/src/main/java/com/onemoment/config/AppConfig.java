package com.onemoment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 应用配置类
 */
@Configuration
public class AppConfig {

    /**
     * 创建RestTemplate Bean
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 增加连接超时时间到60秒
        factory.setConnectTimeout(60000);
        // 增加读取超时时间到120秒，解决AI API调用超时问题
        factory.setReadTimeout(120000);
        return new RestTemplate(factory);
    }
}
