spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:rm-uf6wjk5pxjx5wa2pd.mysql.rds.aliyuncs.com}:${DB_PORT:3306}/${DB_NAME:one_moment_test}?useSSL=true&serverTimezone=Asia/Shanghai&characterEncoding=utf-8&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:one_moment_test}
    password: ${DB_PASSWORD:OneMoment2024Test!}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true

# 日志配置
logging:
  level:
    com.onemoment: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: INFO

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT}
    access-key-id: ${OSS_ACCESS_KEY_ID}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET}
    bucket-name: ${OSS_BUCKET_NAME:one-moment-test}
    url-prefix: ${OSS_URL_PREFIX}
    dir-prefix: ${OSS_DIR_PREFIX:uploads/}
  green:
    region-id: ${GREEN_REGION_ID:cn-shanghai}
    access-key-id: ${GREEN_ACCESS_KEY_ID}
    access-key-secret: ${GREEN_ACCESS_KEY_SECRET}

# AI服务配置
ai:
  service:
    default-provider: tongyi
    tongyi:
      api-key: ${TONGYI_API_KEY}
      api-url: ${TONGYI_API_URL:https://api.tongyi.aliyun.com/v1/}
    deepseek:
      api-key: ${DEEPSEEK_API_KEY}
      api-url: ${DEEPSEEK_API_URL:https://api.deepseek.com/v1/}
    kimi:
      api-key: ${KIMI_API_KEY}
      api-url: ${KIMI_API_URL:https://api.kimi.ai/v1/}
    minimax:
      api-key: ${MINIMAX_API_KEY}
      api-url: ${MINIMAX_API_URL:https://api.minimax.chat/v1/}
