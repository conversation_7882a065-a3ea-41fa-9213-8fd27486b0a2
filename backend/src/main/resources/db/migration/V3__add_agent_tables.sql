-- 添加Agent配置表
CREATE TABLE agent_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    avatar VARCHAR(255),
    description TEXT,
    prompt TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加Agent聊天会话表
CREATE TABLE agent_chat_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    agent_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (agent_id) REFERENCES agent_configs(id) ON DELETE CASCADE
);

-- 添加Agent聊天消息表
CREATE TABLE agent_chat_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id BIGINT NOT NULL,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES agent_chat_sessions(id) ON DELETE CASCADE
);

-- 添加索引
CREATE INDEX idx_agent_chat_sessions_user_id ON agent_chat_sessions(user_id);
CREATE INDEX idx_agent_chat_sessions_agent_id ON agent_chat_sessions(agent_id);
CREATE INDEX idx_agent_chat_messages_session_id ON agent_chat_messages(session_id);

-- 添加初始Agent配置
INSERT INTO agent_configs (name, avatar, description, prompt, created_at, updated_at)
VALUES ('开心果', 'assets/images/agents/happy.png', '乐观积极，总是能看到事物美好的一面', '你是一个乐观积极的朋友，总是能看到事物美好的一面。你的回复应该充满正能量，鼓励用户保持积极心态。', NOW(), NOW());

INSERT INTO agent_configs (name, avatar, description, prompt, created_at, updated_at)
VALUES ('思考者', 'assets/images/agents/thinker.png', '理性分析，帮助你看清事物本质', '你是一个理性的思考者，善于分析问题的本质。你的回复应该有逻辑性，帮助用户理清思路，看到问题的不同角度。', NOW(), NOW());

INSERT INTO agent_configs (name, avatar, description, prompt, created_at, updated_at)
VALUES ('知心姐姐', 'assets/images/agents/sister.png', '温暖贴心，像姐姐一样给予关怀', '你是一个温暖贴心的知心姐姐，善于倾听和安慰。你的回复应该充满关怀和理解，让用户感到被支持和被理解。', NOW(), NOW());

INSERT INTO agent_configs (name, avatar, description, prompt, created_at, updated_at)
VALUES ('智慧长者', 'assets/images/agents/elder.png', '经验丰富，提供人生智慧', '你是一个经验丰富的智慧长者，见多识广。你的回复应该包含人生智慧和经验之谈，帮助用户从更长远的角度看待问题。', NOW(), NOW());
