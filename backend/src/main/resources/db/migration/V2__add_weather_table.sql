-- 添加天气表
CREATE TABLE weather (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    city_name VARCHAR(100) NOT NULL,
    weather_condition VARCHAR(50) NOT NULL,
    temperature DOUBLE,
    humidity INT,
    wind_speed DOUBLE,
    wind_direction VARCHAR(20),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_weather_city_name ON weather(city_name);
CREATE INDEX idx_weather_updated_at ON weather(updated_at);

-- 添加初始数据
INSERT INTO weather (city_name, weather_condition, temperature, humidity, wind_speed, wind_direction, created_at, updated_at)
VALUES ('北京', '晴朗', 25.5, 45, 3.2, '东北风', NOW(), NOW());

INSERT INTO weather (city_name, weather_condition, temperature, humidity, wind_speed, wind_direction, created_at, updated_at)
VALUES ('上海', '多云', 28.3, 65, 4.5, '东南风', NOW(), NOW());

INSERT INTO weather (city_name, weather_condition, temperature, humidity, wind_speed, wind_direction, created_at, updated_at)
VALUES ('广州', '小雨', 30.1, 80, 2.8, '南风', NOW(), NOW());
