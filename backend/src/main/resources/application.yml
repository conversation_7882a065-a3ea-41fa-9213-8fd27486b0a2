spring:
  application:
    name: one-moment-backend
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: validate

server:
  port: 8080
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain,text/css,application/javascript

# 日志配置
logging:
  level:
    root: INFO
    com.onemoment: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: INFO

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
  packages-to-scan: com.onemoment.controller

# 应用自定义配置
app:
  security:
    jwt:
      secret-key: ${JWT_SECRET:OneMomentSecretKeyForJWTTokenGeneration2024!@#$%^&*()}
      token-validity: 86400000  # 24小时
      refresh-token-validity: 604800000  # 7天
  cors:
    allowed-origins: "*"
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    max-age: 3600
  content-safety:
    enabled: true
    provider: aliyun
  storage:
    provider: local
