spring:
  datasource:
    url: *****************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true

# 日志配置
logging:
  level:
    com.onemoment: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 存储配置
app:
  storage:
    provider: local
    local:
      upload-dir: uploads
      url-prefix: http://localhost:8080/api/files

# 阿里云OSS配置（已禁用）
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT:https://oss-cn-beijing.aliyuncs.com}
    access-key-id: ${OSS_ACCESS_KEY_ID:LTAI5tP6WnYTpUrNp1b1f2yX}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:******************************}
    bucket-name: ${OSS_BUCKET_NAME:one-moment-dev}
    url-prefix: ${OSS_URL_PREFIX:https://one-moment-dev.oss-cn-beijing.aliyuncs.com/}
    dir-prefix: ${OSS_DIR_PREFIX:uploads/}
  green:
    region-id: ${GREEN_REGION_ID:cn-shanghai}
    access-key-id: ${GREEN_ACCESS_KEY_ID:LTAI5tP6WnYTpUrNp1b1f2yX}
    access-key-secret: ${GREEN_ACCESS_KEY_SECRET:******************************}

# 高德地图配置
amap:
  key: ${AMAP_KEY:ad51cc15b3390cb2c31f92439cf8456f}
  weather:
    url: ${AMAP_WEATHER_URL:https://restapi.amap.com/v3/weather/weatherInfo}

# AI服务配置
ai:
  service:
    default-provider: tongyi
    tongyi:
      api-key: ${TONGYI_API_KEY:sk-539f8c4fa7b14c349702139562eb4701}
      api-url: ${TONGYI_API_URL:https://dashscope.aliyuncs.com/api/v1/}
    deepseek:
      api-key: ${DEEPSEEK_API_KEY:your_deepseek_api_key}
      api-url: ${DEEPSEEK_API_URL:https://api.deepseek.com/v1/}
    kimi:
      api-key: ${KIMI_API_KEY:your_kimi_api_key}
      api-url: ${KIMI_API_URL:https://api.kimi.ai/v1/}
    minimax:
      api-key: ${MINIMAX_API_KEY:your_minimax_api_key}
      api-url: ${MINIMAX_API_URL:https://api.minimax.chat/v1/}
