spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:one_moment_prod}?useSSL=true&serverTimezone=Asia/Shanghai&characterEncoding=utf-8&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true

# 日志配置
logging:
  level:
    root: WARN
    com.onemoment: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
  file:
    name: /var/log/one-moment/application.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30

# 存储配置
app:
  storage:
    provider: ${STORAGE_PROVIDER:local}
    local:
      upload-dir: ${UPLOAD_DIR:/app/uploads}
      url-prefix: ${URL_PREFIX:http://localhost:8080/api/files}

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT}
    access-key-id: ${OSS_ACCESS_KEY_ID}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET}
    bucket-name: ${OSS_BUCKET_NAME:one-moment-prod}
    url-prefix: ${OSS_URL_PREFIX}
    dir-prefix: ${OSS_DIR_PREFIX:uploads/}
  green:
    region-id: ${GREEN_REGION_ID:cn-shanghai}
    access-key-id: ${GREEN_ACCESS_KEY_ID}
    access-key-secret: ${GREEN_ACCESS_KEY_SECRET}

# 高德地图配置
amap:
  key: ${AMAP_KEY}
  weather:
    url: ${AMAP_WEATHER_URL:https://restapi.amap.com/v3/weather/weatherInfo}

# AI服务配置
ai:
  service:
    default-provider: tongyi
    tongyi:
      api-key: ${TONGYI_API_KEY}
      api-url: ${TONGYI_API_URL:https://api.tongyi.aliyun.com/v1/}
    deepseek:
      api-key: ${DEEPSEEK_API_KEY}
      api-url: ${DEEPSEEK_API_URL:https://api.deepseek.com/v1/}
    kimi:
      api-key: ${KIMI_API_KEY}
      api-url: ${KIMI_API_URL:https://api.kimi.ai/v1/}
    minimax:
      api-key: ${MINIMAX_API_KEY}
      api-url: ${MINIMAX_API_URL:https://api.minimax.chat/v1/}

# API文档配置
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLED:true}
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:true}
    path: /swagger-ui.html
    disable-swagger-default-url: true
    display-request-duration: true
    operations-sorter: method
