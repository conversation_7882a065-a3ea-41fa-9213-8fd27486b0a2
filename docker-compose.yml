version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: one-moment-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: one_moment
      MYSQL_USER: one_moment
      MYSQL_PASSWORD: one_moment
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 5s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: one-moment-backend
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      SPRING_DATASOURCE_URL: ******************************************************************************************************
      SPRING_DATASOURCE_USERNAME: one_moment
      SPRING_DATASOURCE_PASSWORD: one_moment
      SPRING_PROFILES_ACTIVE: dev
    ports:
      - "8080:8080"
    volumes:
      - backend-uploads:/app/uploads

  # Nginx 服务器（用于部署前端）
  nginx:
    image: nginx:alpine
    container_name: one-moment-nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./app/build/web:/usr/share/nginx/html
    depends_on:
      - backend

volumes:
  mysql-data:
  backend-uploads:
