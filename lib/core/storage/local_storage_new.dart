import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';

/// 本地存储服务
class LocalStorage {
  late final SharedPreferences _prefs;

  LocalStorage([SharedPreferences? prefs]) {
    if (prefs != null) {
      _prefs = prefs;
    } else {
      // This will be initialized later
      SharedPreferences.getInstance().then((value) => _prefs = value);
    }
  }

  /// 获取字符串
  Future<String?> getString(String key) async {
    try {
      return _prefs.getString(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get string: $e');
    }
  }

  /// 保存字符串
  Future<bool> setString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save string: $e');
    }
  }

  /// 获取布尔值
  Future<bool?> getBool(String key) async {
    try {
      return _prefs.getBool(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get bool: $e');
    }
  }

  /// 保存布尔值
  Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save bool: $e');
    }
  }

  /// 获取整数
  Future<int?> getInt(String key) async {
    try {
      return _prefs.getInt(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get int: $e');
    }
  }

  /// 保存整数
  Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save int: $e');
    }
  }

  /// 获取Map对象
  Future<Map<String, dynamic>?> getMap(String key) async {
    try {
      final jsonString = _prefs.getString(key);
      if (jsonString != null) {
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      throw CacheException(message: 'Failed to get map: $e');
    }
  }

  /// 保存Map对象
  Future<bool> setMap(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      throw CacheException(message: 'Failed to save map: $e');
    }
  }

  /// 删除键值
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      throw CacheException(message: 'Failed to remove key: $e');
    }
  }

  /// 清空所有数据
  Future<bool> clear() async {
    try {
      return await _prefs.clear();
    } catch (e) {
      throw CacheException(message: 'Failed to clear storage: $e');
    }
  }

  /// 检查键是否存在
  bool containsKey(String key) {
    try {
      return _prefs.containsKey(key);
    } catch (e) {
      throw CacheException(message: 'Failed to check key: $e');
    }
  }
}
