import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/weather_model.dart';

/// 天气服务
class WeatherService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();

  /// 构造函数
  WeatherService({ApiClient? apiClient}) {
    if (apiClient != null) {
      _apiClient = apiClient;
    } else {
      _apiClient = ApiClient();
    }
    _logger.i('初始化天气服务');
  }

  /// 获取最新天气
  Future<WeatherModel> getLatestWeather({String cityName = '北京'}) async {
    _logger.i('获取最新天气: $cityName');

    final response = await _apiClient.post(
      '/weather/getLatestWeather',
      data: {'cityName': cityName},
    );

    return WeatherModel.fromJson(response);
  }

  /// 获取指定日期的天气
  Future<WeatherModel> getWeatherByDate({
    required String cityName,
    required DateTime date,
  }) async {
    _logger.i('获取指定日期天气: $cityName, ${date.toIso8601String().split('T')[0]}');

    final response = await _apiClient.post(
      '/weather/getWeatherByDate',
      data: {
        'cityName': cityName,
        'date': date.toIso8601String().split('T')[0],
      },
    );

    return WeatherModel.fromJson(response);
  }
}
