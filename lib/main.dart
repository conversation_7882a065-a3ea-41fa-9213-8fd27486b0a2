import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/router/app_router.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/theme/app_theme.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
import 'package:one_moment_app/presentation/widgets/debug/debug_overlay.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化环境配置
  EnvironmentConfig().initialize(
    environment: Environment.development,
    debugMode: true,
  );
  
  // 初始化日志
  final logger = LoggerUtil();
  logger.i('应用启动');
  
  // 初始化本地存储
  final prefs = await SharedPreferences.getInstance();
  final localStorage = LocalStorage(prefs);
  
  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  runApp(OneMomentApp(localStorage: localStorage));
}

class OneMomentApp extends StatelessWidget {
  final LocalStorage localStorage;
  
  const OneMomentApp({
    super.key,
    required this.localStorage,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<LocalStorage>.value(value: localStorage),
        Provider<ApiClient>(
          create: (context) => ApiClient(localStorage: localStorage),
        ),
        ChangeNotifierProvider<MomentProvider>(
          create: (context) => MomentProvider(
            apiClient: Provider.of<ApiClient>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<WeatherProvider>(
          create: (context) => WeatherProvider(
            apiClient: Provider.of<ApiClient>(context, listen: false),
          ),
        ),
      ],
      child: Consumer<ApiClient>(
        builder: (context, apiClient, child) {
          return DebugOverlay(
            apiClient: apiClient,
            child: MaterialApp.router(
              title: 'One Moment',
              debugShowCheckedModeBanner: false,
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: ThemeMode.system,
              routerConfig: AppRouter.router,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('zh', 'CN'),
                Locale('en', 'US'),
              ],
              locale: const Locale('zh', 'CN'),
              builder: (context, child) {
                return MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaler: TextScaler.linear(1.0),
                  ),
                  child: child!,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
