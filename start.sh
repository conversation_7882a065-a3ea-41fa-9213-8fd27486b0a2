#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== One Moment 应用启动脚本 ===${NC}"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 检查 Flutter 是否安装
if ! command -v flutter &> /dev/null; then
    echo -e "${YELLOW}警告: Flutter 未安装，将跳过前端构建${NC}"
    BUILD_FRONTEND=false
else
    BUILD_FRONTEND=true
fi

# 检查 Maven 是否安装
if ! command -v mvn &> /dev/null; then
    echo -e "${YELLOW}警告: Maven 未安装，将使用 Docker 构建后端${NC}"
    BUILD_BACKEND=false
else
    BUILD_BACKEND=true
fi

# 构建前端
if [ "$BUILD_FRONTEND" = true ]; then
    echo -e "${GREEN}正在构建前端...${NC}"
    cd app
    flutter pub get
    flutter build web --release
    cd ..
    echo -e "${GREEN}前端构建完成${NC}"
else
    echo -e "${YELLOW}跳过前端构建${NC}"
fi

# 构建后端
if [ "$BUILD_BACKEND" = true ]; then
    echo -e "${GREEN}正在构建后端...${NC}"
    cd backend
    mvn clean package -DskipTests
    cd ..
    echo -e "${GREEN}后端构建完成${NC}"
else
    echo -e "${YELLOW}跳过后端构建，将使用 Docker 构建${NC}"
fi

# 启动 Docker Compose
echo -e "${GREEN}正在启动 Docker Compose...${NC}"
docker-compose up -d

# 检查服务是否启动成功
echo -e "${GREEN}正在检查服务状态...${NC}"
sleep 5
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}服务启动成功!${NC}"
    echo -e "${GREEN}前端访问地址: http://localhost${NC}"
    echo -e "${GREEN}后端API地址: http://localhost/api${NC}"
    echo -e "${GREEN}Swagger文档: http://localhost/api/swagger-ui/index.html${NC}"
else
    echo -e "${RED}服务启动失败，请检查日志${NC}"
    docker-compose logs
fi
