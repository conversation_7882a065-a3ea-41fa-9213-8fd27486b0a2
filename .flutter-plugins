# This is a generated file; do not edit or check into version control.
camera=/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
camera_android=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/
camera_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19+2/
camera_web=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
flutter_native_splash=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.6/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
geolocator=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/
geolocator_android=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
geolocator_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
geolocator_web=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/
geolocator_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
record=/Users/<USER>/.pub-cache/hosted/pub.dev/record-5.2.1/
record_android=/Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/
record_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/
record_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-0.7.2/
record_web=/Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.8/
record_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.6/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
