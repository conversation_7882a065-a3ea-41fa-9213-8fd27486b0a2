#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== One Moment 应用停止脚本 ===${NC}"

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 停止 Docker Compose
echo -e "${GREEN}正在停止 Docker Compose...${NC}"
docker-compose down

# 检查服务是否停止成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}服务已成功停止${NC}"
else
    echo -e "${RED}服务停止失败，请手动检查${NC}"
    exit 1
fi
