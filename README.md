# One Moment

**用最简单的方式，留住最细腻的幸福**

## 项目概述

One Moment 是一款帮助用户记录生活中美好瞬间的应用，通过 AI Native 的能力帮助用户提升幸福感知力和幸福指数。这是一个结合了「日常记录 + 情绪回顾 + 时间轴记忆」的概念，能帮助用户培养积极心态和增强幸福感。

## 技术架构

### 移动端
- Flutter 3.0+ 跨平台框架
- 原生模块：Android (Kotlin)，iOS (Swift) 用于视频处理
- 多环境配置管理

### 后端
- Spring Boot 3.2 + MySQL 8.0
- 阿里云 ACK 容器服务
- 阿里云 OSS 文件存储
- 多环境配置隔离

### AI 集成
- 国内 AI 模型：通义千问、DeepSeek、KIMI、MiniMax
- 内容安全审核：阿里云绿网

## 项目结构

```
one_moment/
├── app/     # Flutter 移动应用
│   ├── assets/   # 应用资源文件
│   ├── lib/      # Flutter 代码
│   └── pubspec.yaml  # Flutter 依赖配置
└── backend/ # Spring Boot 后端服务
    ├── src/      # 后端源代码
    ├── mvnw      # Maven 包装器
    └── pom.xml   # Maven 依赖配置
```

## 核心功能

1. **极简记录体验**
   - 灵活输入：文字、图片、图片+文字、短视频、音频
   - 智能时间戳：自动记录时间/地点/天气
   - AI Native：与大模型交流，获取鼓励和安慰

2. **回顾系统设计**
   - 每周主题回顾
   - Week of Week 对比
   - 月度总结与情绪曲线
   - 随机回忆功能

3. **视觉呈现创新**
   - 时间胶囊界面
   - 季节主题色自动变化

4. **后台运营功能**
   - 用户管理
   - 内容审核
   - 数据看板
   - 远程配置

## 项目状态

### 已完成功能
- 项目基础架构设计
- 多环境配置管理
- 基础UI框架
- 后端API实现
- 本地存储服务
- 前端与后端连接
- 模拟数据库服务
- 时间胶囊功能
- 月历查看功能
- 记录瞬间功能（支持文字、图片、视频）
- 阿里云OSS集成
- 通义千问API集成
- 发现页面（类似即刻）
- GoRouter路由管理

### 待完成功能
- 内容安全审核
- 后台管理系统

## 安全措施
- 内容安全审核 API 集成
- 敏感词过滤系统
- 心理危机关键词检测
- AI 生成内容免责声明

## 环境配置
- 开发环境：用于开发和测试
- 测试环境：用于 QA 和验收测试
- 生产环境：用于最终用户使用

## 开发指南

### 前端开发
```bash
cd app
flutter pub get
open -a Simulator
flutter run -d ios      # 在iOS模拟器上运行
flutter run -d android  # 在Android模拟器上运行
flutter run -d chrome   # 在Chrome浏览器上运行
```

### 后端开发
```bash
cd backend
mvn clean install -DskipTests
mvn spring-boot:run
```

### 数据库初始化
项目使用MySQL数据库，需要先创建数据库并初始化表结构：

```bash
# 登录MySQL
mysql -u root -p

# 执行初始化脚本
mysql -u root -p < backend/src/main/resources/schema.sql
```

或者直接在MySQL客户端中执行`schema.sql`文件中的SQL语句。

### API文档
后端API文档可通过Swagger UI访问：http://localhost:8080/api/swagger-ui/index.html

### 存储配置
默认情况下，应用使用阿里云OSS存储服务，需要在`application.yml`中配置OSS相关参数。

```yaml
aliyun:
  oss:
    access-key-id: 你的AccessKeyId
    access-key-secret: 你的AccessKeySecret
    endpoint: oss-cn-beijing.aliyuncs.com
    bucket: your-bucket-name
    region: cn-beijing
```

## 部署指南

### 前端打包
```bash
cd app
flutter build apk --release  # Android
flutter build ios --release  # iOS
flutter build web --release  # Web
```

### 后端打包
```bash
cd backend
mvn clean package -DskipTests
```

### 后端部署
```bash
# 运行JAR包
java -jar backend/target/one-moment-backend-0.0.1-SNAPSHOT.jar

# 或者使用Docker（需要先创建Dockerfile）
cd backend
docker build -t one-moment-backend .
docker run -p 8080:8080 one-moment-backend
```

### 阿里云部署
应用可以部署在阿里云ACK容器服务上，具体步骤请参考阿里云文档。

## 最近更新

### 2023-05-18
- 优化了阿里云OSS存储服务，改进了文件目录结构为userId/年月格式
- 添加了统一的API响应格式，提升了前后端交互的稳定性
- 实现了AI模块与通义千问API的完整集成，支持内容分析和聊天功能
- 集成了高德地图天气API，替换了模拟天气数据
- 添加了OssController和AIController，规范了API接口设计
- 优化了文件上传功能，增加了成功/失败状态返回
- 完善了错误处理机制，提高了系统稳定性

### 2023-05-17
- 修复了GoRouter上下文问题，解决了导航错误
- 实现了记录瞬间功能，支持文字、图片和视频
- 集成了通义千问API，提供智能对话功能
- 实现了时间胶囊功能，可按日期查看历史记录
- 添加了阿里云OSS集成，支持媒体文件云存储
- 优化了UI界面，提升用户体验
- 添加了发现页面，类似即刻的社区功能

### 已知问题
- 部分图片资源可能无法正确加载，需要手动添加
- 通义千问API需要配置正确的密钥才能使用
- 阿里云OSS需要配置正确的访问凭证才能使用
- 后端服务需要正确配置数据库连接才能使用
- 高德地图API需要配置正确的密钥才能获取天气信息

### 解决方案
1. 确保在`app/assets/images/agents/`目录下有所需的图片资源
2. 在`app/assets/config/tongyi_config.json`中配置正确的通义千问API密钥
3. 在`app/assets/config/oss_config.json`中配置正确的阿里云OSS访问凭证
4. 确保MySQL服务正在运行，并且用户名和密码正确
5. 在后端的`application.yml`中配置正确的高德地图API密钥

### 配置说明

#### 阿里云OSS配置
在后端的`application.yml`中配置：
```yaml
aliyun:
  oss:
    access-key-id: 你的AccessKeyId
    access-key-secret: 你的AccessKeySecret
    endpoint: oss-cn-beijing.aliyuncs.com
    bucket: your-bucket-name
    region: cn-beijing
```

#### 高德地图API配置
在后端的`application.yml`中配置：
```yaml
amap:
  key: 你的高德地图API密钥
  weather:
    url: https://restapi.amap.com/v3/weather/weatherInfo
```

#### 通义千问API配置
在`app/assets/config/tongyi_config.json`中配置：
```json
{
  "apiKey": "你的通义千问API密钥",
  "apiSecret": "你的通义千问API密钥密码",
  "apiEndpoint": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
}
```
