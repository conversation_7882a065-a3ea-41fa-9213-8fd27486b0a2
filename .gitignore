# 通用
.DS_Store
.idea/
*.iml
*.iws
*.ipr
.vscode/
*.swp
*.swo
*.log
logs/
tmp/
temp/

# 后端
backend/target/
backend/build/
backend/out/
backend/.gradle/
backend/uploads/
backend/.settings/
backend/.classpath
backend/.project
backend/.factorypath
backend/bin/
backend/logs/
backend/*.log
backend/*.gz

# 前端
app/.dart_tool/
app/.flutter-plugins
app/.flutter-plugins-dependencies
app/.packages
app/build/
app/.pub/
app/.pub-cache/
app/ios/Pods/
app/ios/Flutter/App.framework
app/ios/Flutter/Flutter.framework
app/ios/Flutter/Generated.xcconfig
app/ios/Flutter/app.flx
app/ios/Flutter/app.zip
app/ios/Flutter/flutter_assets/
app/ios/Flutter/flutter_export_environment.sh
app/ios/ServiceDefinitions.json
app/ios/Runner/GeneratedPluginRegistrant.*
app/android/.gradle/
app/android/captures/
app/android/gradlew
app/android/gradlew.bat
app/android/local.properties
app/android/app/debug
app/android/app/profile
app/android/app/release
app/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
app/android/app/src/main/kotlin/io/flutter/plugins/GeneratedPluginRegistrant.kt
app/android/app/src/debug/AndroidManifest.xml
app/android/app/src/profile/AndroidManifest.xml
app/android/app/src/release/AndroidManifest.xml
app/android/app/src/debug/res/
app/android/app/src/profile/res/
app/android/app/src/release/res/
app/android/app/src/debug/assets/
app/android/app/src/profile/assets/
app/android/app/src/release/assets/
app/android/app/src/debug/java/
app/android/app/src/profile/java/
app/android/app/src/release/java/
app/android/app/src/debug/kotlin/
app/android/app/src/profile/kotlin/
app/android/app/src/release/kotlin/
app/android/app/src/debug/res/
app/android/app/src/profile/res/
app/android/app/src/release/res/
app/android/app/src/debug/assets/
app/android/app/src/profile/assets/
app/android/app/src/release/assets/
app/android/app/src/debug/java/
app/android/app/src/profile/java/
app/android/app/src/release/java/
app/android/app/src/debug/kotlin/
app/android/app/src/profile/kotlin/
app/android/app/src/release/kotlin/

# Docker
.docker/
docker-compose.override.yml

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
