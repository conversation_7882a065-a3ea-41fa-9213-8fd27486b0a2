import 'package:flutter/material.dart';

/// 应用主题配置
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();
  
  // 春季主题
  static ThemeData springTheme() {
    return ThemeData(
      primaryColor: const Color(0xFFF8BBD0), // 浅粉色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFF8BBD0),
        brightness: Brightness.light,
      ),
      fontFamily: 'PingFang',
      useMaterial3: true,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFF8BBD0),
        foregroundColor: Colors.black87,
        elevation: 0,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: Color(0xFFF8BBD0),
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF8BBD0),
          foregroundColor: Colors.black87,
        ),
      ),
    );
  }
  
  // 夏季主题
  static ThemeData summerTheme() {
    return ThemeData(
      primaryColor: const Color(0xFF81D4FA), // 浅蓝色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF81D4FA),
        brightness: Brightness.light,
      ),
      fontFamily: 'PingFang',
      useMaterial3: true,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF81D4FA),
        foregroundColor: Colors.black87,
        elevation: 0,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: Color(0xFF81D4FA),
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF81D4FA),
          foregroundColor: Colors.black87,
        ),
      ),
    );
  }
  
  // 秋季主题
  static ThemeData autumnTheme() {
    return ThemeData(
      primaryColor: const Color(0xFFFFCC80), // 浅橙色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFFFCC80),
        brightness: Brightness.light,
      ),
      fontFamily: 'PingFang',
      useMaterial3: true,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFFFCC80),
        foregroundColor: Colors.black87,
        elevation: 0,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: Color(0xFFFFCC80),
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFCC80),
          foregroundColor: Colors.black87,
        ),
      ),
    );
  }
  
  // 冬季主题
  static ThemeData winterTheme() {
    return ThemeData(
      primaryColor: const Color(0xFFB3E5FC), // 冷蓝色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFB3E5FC),
        brightness: Brightness.light,
      ),
      fontFamily: 'PingFang',
      useMaterial3: true,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFB3E5FC),
        foregroundColor: Colors.black87,
        elevation: 0,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: Color(0xFFB3E5FC),
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFB3E5FC),
          foregroundColor: Colors.black87,
        ),
      ),
    );
  }
  
  /// 根据当前月份获取季节主题
  static ThemeData getSeasonTheme() {
    final month = DateTime.now().month;
    
    // 3-5月为春季，6-8月为夏季，9-11月为秋季，12-2月为冬季
    if (month >= 3 && month <= 5) {
      return springTheme();
    } else if (month >= 6 && month <= 8) {
      return summerTheme();
    } else if (month >= 9 && month <= 11) {
      return autumnTheme();
    } else {
      return winterTheme();
    }
  }
}
