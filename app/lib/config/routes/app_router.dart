import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/presentation/pages/home_page.dart';
import 'package:one_moment_app/presentation/pages/record_page.dart';
import 'package:one_moment_app/presentation/pages/review_page.dart';
import 'package:one_moment_app/presentation/pages/settings_page.dart';
import 'package:one_moment_app/presentation/pages/ai_chat_page.dart';

/// 应用路由配置
class AppRouter {
  // 私有构造函数，防止实例化
  AppRouter._();
  
  /// 创建路由器
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: AppConstants.homeRoute,
      routes: [
        GoRoute(
          path: AppConstants.homeRoute,
          builder: (context, state) => const HomePage(),
        ),
        GoRoute(
          path: AppConstants.recordRoute,
          builder: (context, state) => const RecordPage(),
        ),
        GoRoute(
          path: AppConstants.reviewRoute,
          builder: (context, state) => const ReviewPage(),
        ),
        GoRoute(
          path: AppConstants.settingsRoute,
          builder: (context, state) => const SettingsPage(),
        ),
        GoRoute(
          path: AppConstants.aiChatRoute,
          builder: (context, state) => const AIChatPage(),
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(
          title: const Text('页面未找到'),
        ),
        body: Center(
          child: Text('无法找到路径: ${state.uri.path}'),
        ),
      ),
    );
  }
}
