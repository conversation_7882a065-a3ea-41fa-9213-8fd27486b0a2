import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:path_provider/path_provider.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// 图片缓存服务
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  final LoggerUtil _logger = LoggerUtil();
  final Map<String, String> _urlCache = {}; // objectKey -> cachedUrl
  final Map<String, DateTime> _cacheTime = {}; // objectKey -> cacheTime
  
  // 缓存有效期：1小时
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// 检查缓存是否有效
  bool _isCacheValid(String objectKey) {
    if (!_urlCache.containsKey(objectKey) || !_cacheTime.containsKey(objectKey)) {
      return false;
    }
    
    final cacheTime = _cacheTime[objectKey]!;
    final now = DateTime.now();
    return now.difference(cacheTime) < _cacheExpiry;
  }

  /// 获取缓存的URL
  String? getCachedUrl(String objectKey) {
    if (_isCacheValid(objectKey)) {
      _logger.d('使用缓存的图片URL: $objectKey');
      return _urlCache[objectKey];
    }
    
    // 清理过期缓存
    _urlCache.remove(objectKey);
    _cacheTime.remove(objectKey);
    return null;
  }

  /// 缓存URL
  void cacheUrl(String objectKey, String url) {
    _urlCache[objectKey] = url;
    _cacheTime[objectKey] = DateTime.now();
    _logger.d('缓存图片URL: $objectKey -> $url');
  }

  /// 清理所有缓存
  void clearCache() {
    _urlCache.clear();
    _cacheTime.clear();
    _logger.i('清理所有图片URL缓存');
  }

  /// 清理过期缓存
  void cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTime.entries) {
      if (now.difference(entry.value) >= _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _urlCache.remove(key);
      _cacheTime.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.i('清理${expiredKeys.length}个过期的图片URL缓存');
    }
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    cleanExpiredCache(); // 先清理过期缓存
    
    return {
      'totalCached': _urlCache.length,
      'cacheKeys': _urlCache.keys.toList(),
    };
  }
}
