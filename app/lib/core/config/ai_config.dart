import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// AI配置管理
class AIConfigManager {
  static const String _configKey = 'ai_model_config';
  static const String _defaultConfigPath = 'assets/config/ai_default_config.json';
  final LoggerUtil _logger = LoggerUtil();
  
  /// 获取AI模型配置
  Future<AIModelConfig> getModelConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configKey);
      
      if (configJson != null) {
        _logger.i('从本地存储加载AI配置');
        return AIModelConfig.fromJson(json.decode(configJson));
      }
      
      // 如果本地没有配置，加载默认配置
      _logger.i('加载默认AI配置');
      return _loadDefaultConfig();
    } catch (e) {
      _logger.e('获取AI配置失败', e);
      // 返回一个基本配置
      return AIModelConfig(
        modelName: 'qwen-max',
        apiKey: '',
        provider: '通义千问',
      );
    }
  }
  
  /// 保存AI模型配置
  Future<bool> saveModelConfig(AIModelConfig config) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = json.encode(config.toJson());
      
      final result = await prefs.setString(_configKey, configJson);
      _logger.i('保存AI配置${result ? '成功' : '失败'}: ${config.provider}');
      
      return result;
    } catch (e) {
      _logger.e('保存AI配置失败', e);
      return false;
    }
  }
  
  /// 加载默认配置
  Future<AIModelConfig> _loadDefaultConfig() async {
    try {
      final configString = await rootBundle.loadString(_defaultConfigPath);
      final configJson = json.decode(configString);
      
      return AIModelConfig.fromJson(configJson);
    } catch (e) {
      _logger.e('加载默认AI配置失败', e);
      // 返回一个基本配置
      return AIModelConfig(
        modelName: 'qwen-max',
        apiKey: '',
        provider: '通义千问',
      );
    }
  }
  
  /// 获取所有支持的AI模型配置
  Future<List<AIModelConfig>> getAllModelConfigs() async {
    try {
      final configString = await rootBundle.loadString('assets/config/ai_models.json');
      final List<dynamic> configsJson = json.decode(configString);
      
      return configsJson
          .map((config) => AIModelConfig.fromJson(config))
          .toList();
    } catch (e) {
      _logger.e('获取所有AI模型配置失败', e);
      // 返回基本配置列表
      return [
        AIModelConfig(
          modelName: 'qwen-max',
        apiKey: '',
          provider: '通义千问',
        ),
        AIModelConfig(
          modelName: 'deepseek-chat',
          provider: 'DeepSeek',
        ),
        AIModelConfig(
          modelName: 'kimi-v1',
          provider: 'Kimi',
        ),
        AIModelConfig(
          modelName: 'minimax-abab5.5',
          provider: 'MiniMax',
        ),
      ];
    }
  }
}
