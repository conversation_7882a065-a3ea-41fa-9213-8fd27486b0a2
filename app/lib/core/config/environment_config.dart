import 'dart:io';
import 'package:flutter/foundation.dart';

/// 环境类型
enum Environment {
  development,
  testing,
  production,
}

/// 环境配置
class EnvironmentConfig {
  static final EnvironmentConfig _instance = EnvironmentConfig._internal();

  factory EnvironmentConfig() {
    return _instance;
  }

  EnvironmentConfig._internal();

  // 当前环境
  Environment _environment = Environment.development;

  // 是否启用调试模式
  bool _debugMode = kDebugMode;

  // API基础URL
  final Map<Environment, String> _apiBaseUrls = {
    Environment.development: 'http://localhost:8080/api',
    Environment.testing: 'https://api-test.onemoment.com/api',
    Environment.production: 'https://api.onemoment.com/api',
  };

  // 模拟器特定配置 - 根据平台返回不同的URL
  // iOS模拟器使用localhost，Android模拟器使用********（指向宿主机的特殊IP）
  final Map<Environment, Map<String, String>> _platformApiBaseUrls = {
    Environment.development: {
      'ios': 'http://localhost:8080/api',
      'android': 'http://********:8080/api',
      'web': 'http://localhost:8080/api',
    },
    Environment.testing: {
      'ios': 'https://api-test.onemoment.com/api',
      'android': 'https://api-test.onemoment.com/api',
    },
    Environment.production: {
      'ios': 'https://api.onemoment.com/api',
      'android': 'https://api.onemoment.com/api',
    },
  };

  /// 初始化环境配置
  void initialize({
    Environment environment = Environment.development,
    bool debugMode = kDebugMode,
    Map<Environment, String>? apiBaseUrls,
    Map<Environment, Map<String, String>>? platformApiBaseUrls,
  }) {
    _environment = environment;
    _debugMode = debugMode;

    if (apiBaseUrls != null) {
      _apiBaseUrls.addAll(apiBaseUrls);
    }

    if (platformApiBaseUrls != null) {
      platformApiBaseUrls.forEach((env, urls) {
        if (_platformApiBaseUrls.containsKey(env)) {
          _platformApiBaseUrls[env]!.addAll(urls);
        } else {
          _platformApiBaseUrls[env] = urls;
        }
      });
    }
  }

  /// 获取当前环境
  Environment get environment => _environment;

  /// 获取是否为开发环境
  bool get isDevelopment => _environment == Environment.development;

  /// 获取是否为测试环境
  bool get isTesting => _environment == Environment.testing;

  /// 获取是否为生产环境
  bool get isProduction => _environment == Environment.production;

  /// 获取是否启用调试模式
  bool get isDebugMode => _debugMode;

  /// 设置是否启用调试模式
  set isDebugMode(bool value) {
    _debugMode = value;
  }

  /// 获取当前平台
  String get platform {
    if (kIsWeb) {
      return 'web';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else {
      return 'unknown';
    }
  }

  /// 获取API基础URL
  String get apiBaseUrl {
    if (kIsWeb) {
      return _apiBaseUrls[_environment] ?? '';
    } else {
      // 根据平台返回不同的URL
      final currentPlatform = platform;
      final platformUrls = _platformApiBaseUrls[_environment];
      if (platformUrls != null && platformUrls.containsKey(currentPlatform)) {
        return platformUrls[currentPlatform]!;
      }
      return _apiBaseUrls[_environment] ?? '';
    }
  }

  /// 设置API基础URL
  void setApiBaseUrl(String url, {Environment? environment, String? platformName}) {
    final env = environment ?? _environment;
    final plat = platformName ?? platform;

    // 更新通用URL
    _apiBaseUrls[env] = url;

    // 更新平台特定URL
    if (_platformApiBaseUrls.containsKey(env)) {
      _platformApiBaseUrls[env]![plat] = url;
    } else {
      _platformApiBaseUrls[env] = {plat: url};
    }
  }

  /// 获取环境名称
  String get environmentName {
    switch (_environment) {
      case Environment.development:
        return 'Development';
      case Environment.testing:
        return 'Testing';
      case Environment.production:
        return 'Production';
    }
  }

  /// 获取环境配置信息
  Map<String, dynamic> getConfig() {
    return {
      'environment': environmentName,
      'debugMode': _debugMode,
      'apiBaseUrl': apiBaseUrl,
    };
  }
}
