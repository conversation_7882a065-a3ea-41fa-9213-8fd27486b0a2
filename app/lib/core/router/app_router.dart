import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
import 'package:one_moment_app/presentation/pages/agent_list_page.dart';

import 'package:one_moment_app/presentation/pages/home_page.dart';
import 'package:one_moment_app/presentation/pages/moment_create_page.dart';
import 'package:one_moment_app/presentation/pages/moment_edit_page.dart';
import 'package:one_moment_app/presentation/pages/time_capsule_calendar_page.dart';

/// 应用路由配置
class AppRouter {
  /// 创建路由器
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    routes: [
      // 首页
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),

      // 时间胶囊日历页面
      GoRoute(
        path: '/time-capsule',
        name: 'time-capsule',
        builder: (context, state) => const TimeCapsuleCalendarPage(),
      ),

      // 创建瞬间页面
      GoRoute(
        path: '/moment-create',
        name: 'moment-create',
        builder: (context, state) => const MomentCreatePage(),
      ),

      // 编辑瞬间页面
      GoRoute(
        path: '/moment-edit',
        name: 'moment-edit',
        builder: (context, state) {
          final moment = state.extra as MomentModel;
          return MomentEditPage(moment: moment);
        },
      ),



      // AI助手列表页面
      GoRoute(
        path: '/agents',
        name: 'agents',
        builder: (context, state) => const AgentListPage(),
      ),

      // AI助手聊天页面
      GoRoute(
        path: '/agent-chat',
        name: 'agent-chat',
        builder: (context, state) {
          final Map<String, dynamic> params = state.extra as Map<String, dynamic>? ?? {};
          final int? sessionId = params['sessionId'];
          final int? agentId = params['agentId'];
          return AgentChatPage(
            sessionId: sessionId,
            agentId: agentId,
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      ),
      body: Center(
        child: Text('无法找到路径: ${state.uri.path}'),
      ),
    ),
  );
}
