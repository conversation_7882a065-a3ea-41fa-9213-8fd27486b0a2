/// 服务器异常
class ServerException implements Exception {
  final String message;
  final int? statusCode;
  
  ServerException({required this.message, this.statusCode});
  
  @override
  String toString() => 'ServerException: $message (Status Code: $statusCode)';
}

/// 缓存异常
class CacheException implements Exception {
  final String message;
  
  CacheException({required this.message});
  
  @override
  String toString() => 'CacheException: $message';
}

/// 网络异常
class NetworkException implements Exception {
  final String message;
  
  NetworkException({required this.message});
  
  @override
  String toString() => 'NetworkException: $message';
}

/// 认证异常
class AuthException implements Exception {
  final String message;
  
  AuthException({required this.message});
  
  @override
  String toString() => 'AuthException: $message';
}

/// 权限异常
class PermissionException implements Exception {
  final String message;
  
  PermissionException({required this.message});
  
  @override
  String toString() => 'PermissionException: $message';
}

/// 内容安全异常
class ContentSafetyException implements Exception {
  final String message;
  final String? violationType;
  
  ContentSafetyException({required this.message, this.violationType});
  
  @override
  String toString() => 'ContentSafetyException: $message (Type: $violationType)';
}

/// AI模型异常
class AIModelException implements Exception {
  final String message;
  final String? modelName;
  
  AIModelException({required this.message, this.modelName});
  
  @override
  String toString() => 'AIModelException: $message (Model: $modelName)';
}
