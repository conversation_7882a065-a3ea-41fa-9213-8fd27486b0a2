/// 应用常量
class AppConstants {
  // App信息
  static const String appName = 'One Moment';
  static const String appSlogan = '用最简单的方式，留住最细腻的幸福';

  // API相关
  static const String baseUrl = 'http://localhost:8080/api';

  // 存储相关
  static const String momentsCollection = 'moments';
  static const String userPrefs = 'user_preferences';

  // 安全相关
  static const int maxContentLength = 1000; // 内容最大长度
  static const int maxMediaSize = 50 * 1024 * 1024; // 媒体最大大小 (50MB)

  // AI相关
  static const List<String> supportedAIModels = [
    '通义千问',
    'DeepSeek',
    'Kimi',
    'MiniMax'
  ];

  // 媒体相关
  static const int maxVideoLength = 10; // 最大视频长度（秒）
  static const int maxAudioLength = 10; // 最大音频长度（秒）

  // 路由相关
  static const String homeRoute = '/';
  static const String recordRoute = '/record';
  static const String reviewRoute = '/review';
  static const String settingsRoute = '/settings';
  static const String aiChatRoute = '/ai-chat';
  static const String agentListRoute = '/agent-list';
  static const String agentChatRoute = '/agent-chat';
  static const String timeCapsuleRoute = '/time-capsule';
  static const String discoveryRoute = '/discovery';

  // 主题相关
  static const List<String> seasons = ['春', '夏', '秋', '冬'];
}
