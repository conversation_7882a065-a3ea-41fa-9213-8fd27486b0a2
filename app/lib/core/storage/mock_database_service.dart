import 'package:one_moment_app/core/storage/database_service.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:uuid/uuid.dart';

/// 模拟数据库服务
class MockDatabaseService extends DatabaseService {
  // 单例实例
  static MockDatabaseService? _instance;

  // 模拟数据
  final List<MomentModel> _moments = [];
  final List<ChatSession> _chatSessions = [];

  // 私有构造函数
  MockDatabaseService._() : super();

  /// 获取实例
  static Future<MockDatabaseService> get instance async {
    _instance ??= MockDatabaseService._();
    await _instance!._initMockData();
    return _instance!;
  }

  /// 初始化模拟数据
  Future<void> _initMockData() async {
    if (_moments.isEmpty) {
      // 添加一些模拟瞬间
      final uuid = Uuid();

      // 今天的瞬间
      final today = DateTime.now();
      _moments.add(MomentModel(
        id: uuid.v4(),
        userId: 'mock-user-id',
        username: '测试用户',
        contentType: 'text',
        textContent: '今天是个好日子，阳光明媚，心情舒畅。',
        createdAt: today,
        locationName: '北京市海淀区',
        weather: '晴朗',
        temperature: 25.0,
        latitude: 39.9042,
        longitude: 116.4074,
        mood: MoodType.happy,
      ));

      // 昨天的瞬间
      final yesterday = today.subtract(const Duration(days: 1));
      _moments.add(MomentModel(
        id: uuid.v4(),
        userId: 'mock-user-id',
        username: '测试用户',
        contentType: 'text',
        textContent: '昨天下雨了，在家看了一部好电影，很放松。',
        createdAt: yesterday,
        locationName: '北京市海淀区',
        weather: '雨',
        temperature: 18.0,
        latitude: 39.9042,
        longitude: 116.4074,
        mood: MoodType.calm,
      ));

      // 一周前的瞬间
      final lastWeek = today.subtract(const Duration(days: 7));
      _moments.add(MomentModel(
        id: uuid.v4(),
        userId: 'mock-user-id',
        username: '测试用户',
        contentType: 'text',
        textContent: '一周前去了趟公园，拍了些照片，感觉很美好。',
        createdAt: lastWeek,
        locationName: '北京市海淀区公园',
        weather: '多云',
        temperature: 22.0,
        latitude: 39.9042,
        longitude: 116.4074,
        mood: MoodType.excited,
      ));

      // 一个月前的瞬间
      final lastMonth = DateTime(today.year, today.month - 1, today.day);
      _moments.add(MomentModel(
        id: uuid.v4(),
        userId: 'mock-user-id',
        username: '测试用户',
        contentType: 'text',
        textContent: '一个月前参加了朋友的生日聚会，很开心。',
        createdAt: lastMonth,
        locationName: '北京市朝阳区',
        weather: '晴朗',
        temperature: 26.0,
        latitude: 39.9042,
        longitude: 116.4074,
        mood: MoodType.happy,
      ));
    }

    if (_chatSessions.isEmpty) {
      // 添加一些模拟聊天会话
      final uuid = Uuid();
      final now = DateTime.now();

      _chatSessions.add(ChatSession(
        id: uuid.v4(),
        title: '关于旅行的对话',
        messages: [
          ChatMessage(
            id: uuid.v4(),
            role: MessageRole.user,
            content: '你好，我想去旅行，有什么推荐的地方吗？',
            timestamp: now.subtract(const Duration(minutes: 30)),
          ),
          ChatMessage(
            id: uuid.v4(),
            role: MessageRole.assistant,
            content: '你好！根据当前季节，我推荐你可以考虑以下几个地方：\n1. 云南大理和丽江，气候宜人，风景优美\n2. 浙江杭州，西湖景色迷人\n3. 四川九寨沟，自然风光绝佳\n你更喜欢哪种类型的旅行呢？自然风光、人文历史还是美食体验？',
            timestamp: now.subtract(const Duration(minutes: 29)),
          ),
        ],
        createdAt: now.subtract(const Duration(minutes: 30)),
        updatedAt: now.subtract(const Duration(minutes: 29)),
        modelConfig: AIModelConfig(
          modelName: '通义千问',
          provider: 'aliyun',
          apiKey: 'mock-api-key',
          apiEndpoint: 'https://api.example.com/v1/chat',
        ),
      ));
    }
  }

  @override
  Future<List<MomentModel>> getAllMoments() async {
    return List.from(_moments);
  }

  @override
  Future<List<MomentModel>> getMomentsByDate(DateTime date) async {
    return _moments.where((moment) {
      return moment.createdAt.year == date.year &&
          moment.createdAt.month == date.month &&
          moment.createdAt.day == date.day;
    }).toList();
  }

  @override
  Future<void> insertMoment(MomentModel moment) async {
    final existingIndex = _moments.indexWhere((m) => m.id == moment.id);
    if (existingIndex >= 0) {
      _moments[existingIndex] = moment;
    } else {
      _moments.add(moment);
    }
  }

  @override
  Future<void> deleteMoment(String id) async {
    _moments.removeWhere((moment) => moment.id == id);
  }

  @override
  Future<List<ChatSession>> getAllChatSessions() async {
    return List.from(_chatSessions);
  }

  @override
  Future<void> insertChatSession(ChatSession session) async {
    final existingIndex = _chatSessions.indexWhere((s) => s.id == session.id);
    if (existingIndex >= 0) {
      _chatSessions[existingIndex] = session;
    } else {
      _chatSessions.add(session);
    }
  }

  @override
  Future<void> deleteChatSession(String id) async {
    _chatSessions.removeWhere((session) => session.id == id);
  }
}
