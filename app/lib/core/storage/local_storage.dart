import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';

/// 本地存储服务
class LocalStorage {
  late final SharedPreferences _prefs;

  LocalStorage([SharedPreferences? prefs]) {
    if (prefs != null) {
      _prefs = prefs;
    } else {
      // This will be initialized later
      SharedPreferences.getInstance().then((value) => _prefs = value);
    }
  }

  /// 获取字符串
  Future<String?> getString(String key) async {
    try {
      return _prefs.getString(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get string: $e');
    }
  }

  /// 保存字符串
  Future<bool> setString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save string: $e');
    }
  }

  /// 获取布尔值
  Future<bool?> getBool(String key) async {
    try {
      return _prefs.getBool(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get bool: $e');
    }
  }

  /// 保存布尔值
  Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save bool: $e');
    }
  }

  /// 获取整数
  Future<int?> getInt(String key) async {
    try {
      return _prefs.getInt(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get int: $e');
    }
  }

  /// 保存整数
  Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save int: $e');
    }
  }

  /// 获取双精度浮点数
  Future<double?> getDouble(String key) async {
    try {
      return _prefs.getDouble(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get double: $e');
    }
  }

  /// 保存双精度浮点数
  Future<bool> setDouble(String key, double value) async {
    try {
      return await _prefs.setDouble(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save double: $e');
    }
  }

  /// 获取字符串列表
  Future<List<String>?> getStringList(String key) async {
    try {
      return _prefs.getStringList(key);
    } catch (e) {
      throw CacheException(message: 'Failed to get string list: $e');
    }
  }

  /// 保存字符串列表
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      return await _prefs.setStringList(key, value);
    } catch (e) {
      throw CacheException(message: 'Failed to save string list: $e');
    }
  }

  /// 获取JSON对象
  Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = _prefs.getString(key);
      if (jsonString == null) return null;
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException(message: 'Failed to get JSON: $e');
    }
  }

  /// 保存JSON对象
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      throw CacheException(message: 'Failed to save JSON: $e');
    }
  }

  /// 检查键是否存在
  Future<bool> containsKey(String key) async {
    try {
      return _prefs.containsKey(key);
    } catch (e) {
      throw CacheException(message: 'Failed to check key: $e');
    }
  }

  /// 删除键
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      throw CacheException(message: 'Failed to remove key: $e');
    }
  }

  /// 清除所有数据
  Future<bool> clear() async {
    try {
      return await _prefs.clear();
    } catch (e) {
      throw CacheException(message: 'Failed to clear storage: $e');
    }
  }
}
