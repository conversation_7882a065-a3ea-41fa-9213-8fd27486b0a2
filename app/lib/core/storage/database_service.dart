import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/storage/mock_database_service.dart';

/// 数据库服务
class DatabaseService {
  static const String _databaseName = 'one_moment.db';
  static const int _databaseVersion = 1;

  // 表名
  static const String _momentsTable = 'moments';
  static const String _chatSessionsTable = 'chat_sessions';
  static const String _chatMessagesTable = 'chat_messages';

  // 单例实例
  static DatabaseService? _instance;
  static Database? _database;

  // 构造函数
  DatabaseService();

  /// 获取实例
  static Future<DatabaseService> get instance async {
    // 使用模拟数据库服务
    return MockDatabaseService.instance;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    try {
      final databasePath = await getDatabasesPath();
      final path = join(databasePath, _databaseName);

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      throw CacheException(message: 'Failed to initialize database: $e');
    }
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    // 创建瞬间表
    await db.execute('''
      CREATE TABLE $_momentsTable (
        id TEXT PRIMARY KEY,
        content TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        location TEXT,
        weather TEXT,
        mediaType TEXT NOT NULL,
        mediaPath TEXT,
        tags TEXT,
        mood TEXT NOT NULL,
        isPrivate INTEGER NOT NULL
      )
    ''');

    // 创建聊天会话表
    await db.execute('''
      CREATE TABLE $_chatSessionsTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        modelName TEXT NOT NULL,
        provider TEXT NOT NULL,
        apiKey TEXT NOT NULL,
        apiEndpoint TEXT
      )
    ''');

    // 创建聊天消息表
    await db.execute('''
      CREATE TABLE $_chatMessagesTable (
        id TEXT PRIMARY KEY,
        sessionId TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (sessionId) REFERENCES $_chatSessionsTable (id) ON DELETE CASCADE
      )
    ''');
  }

  /// 升级数据库
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // 未来版本升级逻辑
  }

  /// 关闭数据库
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// 插入瞬间
  Future<void> insertMoment(MomentModel moment) async {
    try {
      await _database!.insert(
        _momentsTable,
        {
          'id': moment.id,
          'userId': moment.userId,
          'username': moment.username,
          'contentType': moment.contentType,
          'textContent': moment.textContent,
          'createdAt': moment.createdAt.toIso8601String(),
          'locationName': moment.locationName,
          'weather': moment.weather,
          'temperature': moment.temperature,
          'latitude': moment.latitude,
          'longitude': moment.longitude,
          'mediaUrl': moment.mediaUrl,
          'mediaObjectKey': moment.mediaObjectKey,
          'thumbnailUrl': moment.thumbnailUrl,
          'mood': moment.mood.toString().split('.').last,
          'isPrivate': moment.isPrivate ? 1 : 0,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw CacheException(message: 'Failed to insert moment: $e');
    }
  }

  /// 获取所有瞬间
  Future<List<MomentModel>> getAllMoments() async {
    try {
      final List<Map<String, dynamic>> maps = await _database!.query(_momentsTable);

      return List.generate(maps.length, (i) {
        return MomentModel(
          id: maps[i]['id'] as String,
          userId: maps[i]['userId'] as String? ?? '',
          username: maps[i]['username'] as String? ?? '',
          contentType: maps[i]['contentType'] as String? ?? 'none',
          textContent: maps[i]['textContent'] as String? ?? maps[i]['content'] as String? ?? '',
          createdAt: DateTime.parse(maps[i]['createdAt'] as String),
          locationName: maps[i]['locationName'] as String? ?? maps[i]['location'] as String?,
          weather: maps[i]['weather'] as String?,
          temperature: maps[i]['temperature'] != null ? double.tryParse(maps[i]['temperature'].toString()) : null,
          latitude: maps[i]['latitude'] != null ? double.tryParse(maps[i]['latitude'].toString()) : null,
          longitude: maps[i]['longitude'] != null ? double.tryParse(maps[i]['longitude'].toString()) : null,
          mediaUrl: maps[i]['mediaUrl'] as String? ?? maps[i]['mediaPath'] as String?,
          mediaObjectKey: maps[i]['mediaObjectKey'] as String?,
          thumbnailUrl: maps[i]['thumbnailUrl'] as String?,
          mood: MoodType.values.firstWhere(
            (e) => e.toString() == 'MoodType.${maps[i]['mood']}',
            orElse: () => MoodType.neutral,
          ),
          isPrivate: (maps[i]['isPrivate'] as int) == 1,
        );
      });
    } catch (e) {
      throw CacheException(message: 'Failed to get moments: $e');
    }
  }

  /// 获取指定日期的瞬间
  Future<List<MomentModel>> getMomentsByDate(DateTime date) async {
    try {
      final startDate = DateTime(date.year, date.month, date.day);
      final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final List<Map<String, dynamic>> maps = await _database!.query(
        _momentsTable,
        where: 'createdAt BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      );

      return List.generate(maps.length, (i) {
        return MomentModel(
          id: maps[i]['id'] as String,
          userId: maps[i]['userId'] as String? ?? '',
          username: maps[i]['username'] as String? ?? '',
          contentType: maps[i]['contentType'] as String? ?? 'none',
          textContent: maps[i]['textContent'] as String? ?? maps[i]['content'] as String? ?? '',
          createdAt: DateTime.parse(maps[i]['createdAt'] as String),
          locationName: maps[i]['locationName'] as String? ?? maps[i]['location'] as String?,
          weather: maps[i]['weather'] as String?,
          temperature: maps[i]['temperature'] != null ? double.tryParse(maps[i]['temperature'].toString()) : null,
          latitude: maps[i]['latitude'] != null ? double.tryParse(maps[i]['latitude'].toString()) : null,
          longitude: maps[i]['longitude'] != null ? double.tryParse(maps[i]['longitude'].toString()) : null,
          mediaUrl: maps[i]['mediaUrl'] as String? ?? maps[i]['mediaPath'] as String?,
          mediaObjectKey: maps[i]['mediaObjectKey'] as String?,
          thumbnailUrl: maps[i]['thumbnailUrl'] as String?,
          mood: MoodType.values.firstWhere(
            (e) => e.toString() == 'MoodType.${maps[i]['mood']}',
            orElse: () => MoodType.neutral,
          ),
          isPrivate: (maps[i]['isPrivate'] as int) == 1,
        );
      });
    } catch (e) {
      throw CacheException(message: 'Failed to get moments by date: $e');
    }
  }

  /// 删除瞬间
  Future<void> deleteMoment(String id) async {
    try {
      await _database!.delete(
        _momentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw CacheException(message: 'Failed to delete moment: $e');
    }
  }

  /// 插入聊天会话
  Future<void> insertChatSession(ChatSession session) async {
    try {
      await _database!.transaction((txn) async {
        // 插入会话
        await txn.insert(
          _chatSessionsTable,
          {
            'id': session.id,
            'title': session.title,
            'createdAt': session.createdAt.toIso8601String(),
            'updatedAt': session.updatedAt.toIso8601String(),
            'modelName': session.modelConfig.modelName,
            'provider': session.modelConfig.provider,
            'apiKey': session.modelConfig.apiKey,
            'apiEndpoint': session.modelConfig.apiEndpoint,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // 插入消息
        for (final message in session.messages) {
          await txn.insert(
            _chatMessagesTable,
            {
              'id': message.id,
              'sessionId': session.id,
              'role': message.role.toString().split('.').last,
              'content': message.content,
              'timestamp': message.timestamp.toIso8601String(),
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
    } catch (e) {
      throw CacheException(message: 'Failed to insert chat session: $e');
    }
  }

  /// 获取所有聊天会话
  Future<List<ChatSession>> getAllChatSessions() async {
    try {
      final List<Map<String, dynamic>> sessionMaps = await _database!.query(_chatSessionsTable);

      final List<ChatSession> sessions = [];

      for (final sessionMap in sessionMaps) {
        final sessionId = sessionMap['id'] as String;

        // 获取会话的所有消息
        final List<Map<String, dynamic>> messageMaps = await _database!.query(
          _chatMessagesTable,
          where: 'sessionId = ?',
          whereArgs: [sessionId],
          orderBy: 'timestamp ASC',
        );

        final List<ChatMessage> messages = messageMaps.map((messageMap) {
          return ChatMessage(
            id: messageMap['id'] as String,
            role: MessageRole.values.firstWhere(
              (e) => e.toString() == 'MessageRole.${messageMap['role']}',
              orElse: () => MessageRole.user,
            ),
            content: messageMap['content'] as String,
            timestamp: DateTime.parse(messageMap['timestamp'] as String),
          );
        }).toList();

        sessions.add(ChatSession(
          id: sessionId,
          title: sessionMap['title'] as String,
          messages: messages,
          createdAt: DateTime.parse(sessionMap['createdAt'] as String),
          updatedAt: DateTime.parse(sessionMap['updatedAt'] as String),
          modelConfig: AIModelConfig(
            modelName: sessionMap['modelName'] as String,
            provider: sessionMap['provider'] as String,
            apiKey: sessionMap['apiKey'] as String,
            apiEndpoint: sessionMap['apiEndpoint'] as String?,
          ),
        ));
      }

      return sessions;
    } catch (e) {
      throw CacheException(message: 'Failed to get chat sessions: $e');
    }
  }

  /// 删除聊天会话
  Future<void> deleteChatSession(String id) async {
    try {
      await _database!.transaction((txn) async {
        // 删除会话的所有消息
        await txn.delete(
          _chatMessagesTable,
          where: 'sessionId = ?',
          whereArgs: [id],
        );

        // 删除会话
        await txn.delete(
          _chatSessionsTable,
          where: 'id = ?',
          whereArgs: [id],
        );
      });
    } catch (e) {
      throw CacheException(message: 'Failed to delete chat session: $e');
    }
  }
}
