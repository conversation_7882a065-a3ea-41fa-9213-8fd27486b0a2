import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/core/network/debug_interceptor.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// API客户端
class ApiClient {
  static const String _authTokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  final Dio _dio;
  final LocalStorage _localStorage;
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();

  ApiClient({required LocalStorage localStorage})
      : _localStorage = localStorage,
        _dio = Dio(
          BaseOptions(
            baseUrl: EnvironmentConfig().apiBaseUrl,
            connectTimeout: const Duration(seconds: 15),
            receiveTimeout: const Duration(seconds: 60),
            contentType: Headers.jsonContentType,
            responseType: ResponseType.json,
          ),
        ) {
    _logger.i('初始化API客户端，baseUrl: ${_dio.options.baseUrl}');

    // 添加拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: _onRequest,
        onResponse: _onResponse,
        onError: _onError,
      ),
    );

    // 添加调试拦截器
    if (_config.isDebugMode) {
      _dio.interceptors.add(DebugInterceptor(enableDetailedLogs: true));
    }
  }

  /// 请求拦截器
  void _onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 添加通用头部
    options.headers['Accept'] = 'application/json';
    options.headers['Content-Type'] = 'application/json';

    // 打印请求信息
    _logger.d('请求: ${options.method} ${options.path}');
    _logger.d('请求头: ${options.headers}');
    if (options.data != null) {
      _logger.d('请求体: ${options.data}');
    }

    handler.next(options);
  }

  /// 响应拦截器
  void _onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  /// 错误拦截器
  void _onError(DioException error, ErrorInterceptorHandler handler) async {
    _logger.e('API错误: ${error.type}', error, error.stackTrace);

    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      _logger.e('网络连接超时: ${error.requestOptions.path}');
    } else if (error.type == DioExceptionType.connectionError) {
      _logger.e('网络连接错误: ${error.requestOptions.path}');
    }

    handler.next(error);
  }

  /// 登录（已废弃，请使用 authenticateUser）
  @Deprecated('请使用 authenticateUser 方法')
  Future<Map<String, dynamic>> login(String username, String password) async {
    final result = await authenticateUser(username, password);
    return {'success': result};
  }

  /// 注册
  Future<Map<String, dynamic>> register(String username, String password, String email, {String? nickname, String? phone}) async {
    try {
      // 创建注册请求
      final request = {
        'username': username,
        'password': password,
        'email': email,
      };

      // 添加可选字段
      if (nickname != null) {
        request['nickname'] = nickname;
      } else {
        request['nickname'] = username;
      }

      if (phone != null) {
        request['phone'] = phone;
      }

      _logger.i('发送注册请求到: /auth/register');
      _logger.d('注册请求数据: $request');

      final response = await _dio.post(
        '/auth/register',
        data: request,
      );

      _logger.d('注册响应: $response');

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        _logger.d('注册响应数据: $data');

        // 保存令牌
        if (data.containsKey('accessToken')) {
          final token = data['accessToken'] as String;
          await _localStorage.setString(_authTokenKey, token);
          _logger.i('注册成功，保存令牌: ${token.substring(0, min(10, token.length))}...');

          // 保存刷新令牌
          if (data.containsKey('refreshToken')) {
            final refreshToken = data['refreshToken'] as String;
            await _localStorage.setString(_refreshTokenKey, refreshToken);
            _logger.i('保存刷新令牌');
          }

          // 如果有用户信息，也保存起来
          if (data.containsKey('user')) {
            final userJson = jsonEncode(data['user']);
            await _localStorage.setString('user_info', userJson);
            _logger.i('保存用户信息');
          }
        }

        return data;
      } else {
        throw Exception('注册失败: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('注册失败', e);
      throw Exception('注册失败: $e');
    }
  }

  /// 登出（已废弃，请使用 signOut）
  @Deprecated('请使用 signOut 方法')
  Future<void> logout() async {
    await signOut();
  }

  /// GET请求 (已废弃，请使用post方法)
  @Deprecated('请使用post方法')
  Future<dynamic> get(String path, {Map<String, dynamic>? queryParameters}) async {
    _logger.w('使用已废弃的get方法，请改用post方法');
    return post(path, data: queryParameters);
  }

  /// POST请求
  Future<dynamic> post(String path, {dynamic data}) async {
    try {
      final response = await _dio.post(path, data: data);
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// PUT请求 (已废弃，请使用post方法)
  @Deprecated('请使用post方法')
  Future<dynamic> put(String path, {dynamic data}) async {
    _logger.w('使用已废弃的put方法，请改用post方法');
    return post(path, data: data);
  }

  /// DELETE请求 (已废弃，请使用post方法)
  @Deprecated('请使用post方法')
  Future<dynamic> delete(String path) async {
    _logger.w('使用已废弃的delete方法，请改用post方法');
    return post(path, data: {'id': path.split('/').last});
  }

  /// 上传文件
  Future<String> uploadFile(File file, String type) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
        'type': type,
      });

      final response = await _dio.post('/moments/upload', data: formData);

      if (response.statusCode == 200) {
        return response.data as String;
      } else {
        throw Exception('上传文件失败: ${response.statusCode}');
      }
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 处理错误
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.sendTimeout) {
        _logger.e('网络连接超时', error, error.stackTrace);
        return Exception('网络连接超时，请检查网络');
      } else if (error.type == DioExceptionType.connectionError) {
        _logger.e('网络连接错误', error, error.stackTrace);
        return Exception('网络连接错误，请检查网络');
      } else if (error.response != null) {
        final statusCode = error.response!.statusCode;
        final data = error.response!.data;

        _logger.e('API响应错误: $statusCode', error, error.stackTrace);

        if (data is Map<String, dynamic> && data.containsKey('message')) {
          final message = data['message'] as String;
          _logger.e('错误信息: $message');
          return Exception(message);
        } else {
          return Exception('请求失败: $statusCode');
        }
      } else {
        _logger.e('未知的Dio错误', error, error.stackTrace);
      }
    }

    _logger.e('未知错误', error);
    return Exception('未知错误: $error');
  }

  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      _logger.i('测试API连接: ${_dio.options.baseUrl}');

      // 尝试发送一个简单的请求
      final response = await _dio.post('/health/check',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );

      _logger.i('API连接测试成功: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      _logger.e('API连接测试失败', e);

      // 尝试使用不同的路径
      try {
        _logger.i('尝试备用路径: ${_dio.options.baseUrl}/moments/getMomentList');
        final response = await _dio.post('/moments/getMomentList',
          options: Options(
            sendTimeout: const Duration(seconds: 5),
            receiveTimeout: const Duration(seconds: 5),
          ),
        );

        _logger.i('备用路径测试成功: ${response.statusCode}');
        return response.statusCode == 200;
      } catch (e2) {
        _logger.e('备用路径测试失败', e2);

        // 尝试不带路径的基础URL
        try {
          final baseUrl = _dio.options.baseUrl;
          final baseUrlWithoutPath = baseUrl.substring(0, baseUrl.lastIndexOf('/api') + 4);
          _logger.i('尝试基础URL: $baseUrlWithoutPath');

          final testDio = Dio(
            BaseOptions(
              baseUrl: baseUrlWithoutPath,
              connectTimeout: const Duration(seconds: 5),
              receiveTimeout: const Duration(seconds: 5),
            ),
          );

          final response = await testDio.post('/health/check');
          _logger.i('基础URL测试成功: ${response.statusCode}');
          return response.statusCode == 200;
        } catch (e3) {
          _logger.e('所有连接测试都失败', e3);
          return false;
        }
      }
    }
  }

  /// 更新API基础URL
  void updateBaseUrl(String baseUrl) {
    _logger.i('更新API基础URL: $baseUrl');
    _dio.options.baseUrl = baseUrl;
  }

  /// 获取当前API基础URL
  String get baseUrl => _dio.options.baseUrl;

  /// 获取Dio实例
  Dio get dio => _dio;

  /// 用户认证
  /// 注意：已移除认证功能，始终返回成功
  Future<bool> authenticateUser(String username, String password) async {
    _logger.i('认证功能已禁用，直接返回成功');
    return true;
  }

  /// 注销
  /// 注意：已移除认证功能，此方法不再执行任何操作
  Future<void> signOut() async {
    _logger.i('注销功能已禁用');
  }

  /// 检查是否已登录
  /// 注意：已移除认证功能，始终返回已登录状态
  bool isLoggedIn() {
    _logger.i('登录检查功能已禁用，始终返回已登录状态');
    return true;
  }
}
