import 'package:one_moment_app/data/models/media_type.dart';import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// 调试拦截器
/// 用于记录网络请求和响应的详细信息
class DebugInterceptor extends Interceptor {
  final LoggerUtil _logger = LoggerUtil();
  final bool _enableDetailedLogs;
  
  DebugInterceptor({bool enableDetailedLogs = kDebugMode}) 
      : _enableDetailedLogs = enableDetailedLogs;
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final method = options.method;
    final url = '${options.baseUrl}${options.path}';
    
    // 记录请求信息
    final requestInfo = {
      'url': url,
      'method': method,
      'headers': options.headers,
      'queryParameters': options.queryParameters,
    };
    
    // 记录请求体（如果存在）
    if (options.data != null) {
      try {
        if (options.data is FormData) {
          final formData = options.data as FormData;
          requestInfo['data'] = 'FormData: ${formData.fields}';
          if (formData.files.isNotEmpty) {
            requestInfo['files'] = formData.files.map((file) => 
                '${file.key}: ${file.value.filename} (${file.value.contentType})').toList();
          }
        } else if (options.data is Map || options.data is List) {
          requestInfo['data'] = options.data;
        } else if (options.data is String) {
          requestInfo['data'] = options.data;
        } else {
          requestInfo['data'] = options.data.toString();
        }
      } catch (e) {
        requestInfo['data'] = 'Error parsing request data: $e';
      }
    }
    
    _logger.network(method, url, data: _enableDetailedLogs ? requestInfo : null);
    
    // 添加请求开始时间，用于计算请求耗时
    options.extra['startTime'] = DateTime.now().millisecondsSinceEpoch;
    
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final method = response.requestOptions.method;
    final url = '${response.requestOptions.baseUrl}${response.requestOptions.path}';
    final statusCode = response.statusCode;
    
    // 计算请求耗时
    final startTime = response.requestOptions.extra['startTime'] as int?;
    final endTime = DateTime.now().millisecondsSinceEpoch;
    final duration = startTime != null ? endTime - startTime : null;
    
    // 记录响应信息
    final responseInfo = {
      'statusCode': statusCode,
      'headers': response.headers.map,
      if (duration != null) 'duration': '$duration ms',
    };
    
    // 记录响应体（如果存在）
    if (response.data != null) {
      try {
        if (response.data is Map || response.data is List) {
          responseInfo['data'] = response.data;
        } else if (response.data is String) {
          // 尝试解析JSON字符串
          try {
            responseInfo['data'] = json.decode(response.data as String);
          } catch (e) {
            responseInfo['data'] = response.data;
          }
        } else {
          responseInfo['data'] = response.data.toString();
        }
      } catch (e) {
        responseInfo['data'] = 'Error parsing response data: $e';
      }
    }
    
    _logger.network(method, url, response: _enableDetailedLogs ? responseInfo : {
      'statusCode': statusCode,
      if (duration != null) 'duration': '$duration ms',
    });
    
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final method = err.requestOptions.method;
    final url = '${err.requestOptions.baseUrl}${err.requestOptions.path}';
    
    // 计算请求耗时
    final startTime = err.requestOptions.extra['startTime'] as int?;
    final endTime = DateTime.now().millisecondsSinceEpoch;
    final duration = startTime != null ? endTime - startTime : null;
    
    // 记录错误信息
    final errorInfo = {
      'type': err.type.toString(),
      'message': err.message,
      if (err.response != null) 'statusCode': err.response?.statusCode,
      if (duration != null) 'duration': '$duration ms',
    };
    
    // 记录响应体（如果存在）
    if (err.response?.data != null) {
      try {
        if (err.response!.data is Map || err.response!.data is List) {
          errorInfo['responseData'] = err.response!.data;
        } else if (err.response!.data is String) {
          // 尝试解析JSON字符串
          try {
            errorInfo['responseData'] = json.decode(err.response!.data as String);
          } catch (e) {
            errorInfo['responseData'] = err.response!.data;
          }
        } else {
          errorInfo['responseData'] = err.response!.data.toString();
        }
      } catch (e) {
        errorInfo['responseData'] = 'Error parsing response data: $e';
      }
    }
    
    _logger.network(method, url, error: errorInfo);
    _logger.e('网络请求错误: $method $url', err, err.stackTrace);
    
    handler.next(err);
  }
}
