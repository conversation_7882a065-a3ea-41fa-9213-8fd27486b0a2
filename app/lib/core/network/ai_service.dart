import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:one_moment_app/core/errors/exceptions.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/security_service.dart';

/// AI服务
class AIService {
  final http.Client _client;
  final SecurityService _securityService;
  
  AIService({
    http.Client? client,
    SecurityService? securityService,
  })  : _client = client ?? http.Client(),
        _securityService = securityService ?? SecurityService();
  
  /// 发送消息到通义千问API
  Future<String> sendMessageToTongyi({
    required List<ChatMessage> messages,
    required String apiKey,
    String? apiEndpoint,
  }) async {
    try {
      // 检查消息内容是否包含敏感词
      for (final message in messages) {
        if (_securityService.containsSensitiveWords(message.content)) {
          final sensitiveWords = _securityService.getSensitiveWords(message.content);
          throw ContentSafetyException(
            message: '消息包含敏感内容',
            violationType: '敏感词: ${sensitiveWords.join(', ')}',
          );
        }
      }
      
      final endpoint = apiEndpoint ?? 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
      
      // 转换消息格式为通义千问API格式
      final List<Map<String, String>> formattedMessages = messages.map((message) {
        return {
          'role': message.role == MessageRole.user
              ? 'user'
              : message.role == MessageRole.assistant
                  ? 'assistant'
                  : 'system',
          'content': message.content,
        };
      }).toList();
      
      final response = await _client.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'model': 'qwen-max',
          'input': {
            'messages': formattedMessages,
          },
          'parameters': {
            'temperature': 0.7,
            'top_p': 0.9,
            'result_format': 'text',
          },
        }),
      );
      
      if (response.statusCode != 200) {
        throw AIModelException(
          message: '通义千问API请求失败: ${response.statusCode}',
          modelName: 'qwen-max',
        );
      }
      
      final responseData = json.decode(response.body);
      return responseData['output']['text'] as String;
    } catch (e) {
      if (e is ContentSafetyException || e is AIModelException) {
        rethrow;
      }
      throw AIModelException(
        message: '通义千问API请求异常: $e',
        modelName: 'qwen-max',
      );
    }
  }
  
  /// 发送消息到DeepSeek API
  Future<String> sendMessageToDeepSeek({
    required List<ChatMessage> messages,
    required String apiKey,
    String? apiEndpoint,
  }) async {
    try {
      // 检查消息内容是否包含敏感词
      for (final message in messages) {
        if (_securityService.containsSensitiveWords(message.content)) {
          final sensitiveWords = _securityService.getSensitiveWords(message.content);
          throw ContentSafetyException(
            message: '消息包含敏感内容',
            violationType: '敏感词: ${sensitiveWords.join(', ')}',
          );
        }
      }
      
      final endpoint = apiEndpoint ?? 'https://api.deepseek.com/v1/chat/completions';
      
      // 转换消息格式为DeepSeek API格式
      final List<Map<String, String>> formattedMessages = messages.map((message) {
        return {
          'role': message.role == MessageRole.user
              ? 'user'
              : message.role == MessageRole.assistant
                  ? 'assistant'
                  : 'system',
          'content': message.content,
        };
      }).toList();
      
      final response = await _client.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'model': 'deepseek-chat',
          'messages': formattedMessages,
          'temperature': 0.7,
          'top_p': 0.9,
          'max_tokens': 2000,
        }),
      );
      
      if (response.statusCode != 200) {
        throw AIModelException(
          message: 'DeepSeek API请求失败: ${response.statusCode}',
          modelName: 'deepseek-chat',
        );
      }
      
      final responseData = json.decode(response.body);
      return responseData['choices'][0]['message']['content'] as String;
    } catch (e) {
      if (e is ContentSafetyException || e is AIModelException) {
        rethrow;
      }
      throw AIModelException(
        message: 'DeepSeek API请求异常: $e',
        modelName: 'deepseek-chat',
      );
    }
  }
  
  /// 发送消息到Kimi API
  Future<String> sendMessageToKimi({
    required List<ChatMessage> messages,
    required String apiKey,
    String? apiEndpoint,
  }) async {
    try {
      // 检查消息内容是否包含敏感词
      for (final message in messages) {
        if (_securityService.containsSensitiveWords(message.content)) {
          final sensitiveWords = _securityService.getSensitiveWords(message.content);
          throw ContentSafetyException(
            message: '消息包含敏感内容',
            violationType: '敏感词: ${sensitiveWords.join(', ')}',
          );
        }
      }
      
      final endpoint = apiEndpoint ?? 'https://api.moonshot.cn/v1/chat/completions';
      
      // 转换消息格式为Kimi API格式
      final List<Map<String, String>> formattedMessages = messages.map((message) {
        return {
          'role': message.role == MessageRole.user
              ? 'user'
              : message.role == MessageRole.assistant
                  ? 'assistant'
                  : 'system',
          'content': message.content,
        };
      }).toList();
      
      final response = await _client.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'model': 'moonshot-v1-8k',
          'messages': formattedMessages,
          'temperature': 0.7,
          'top_p': 0.9,
          'max_tokens': 2000,
        }),
      );
      
      if (response.statusCode != 200) {
        throw AIModelException(
          message: 'Kimi API请求失败: ${response.statusCode}',
          modelName: 'moonshot-v1-8k',
        );
      }
      
      final responseData = json.decode(response.body);
      return responseData['choices'][0]['message']['content'] as String;
    } catch (e) {
      if (e is ContentSafetyException || e is AIModelException) {
        rethrow;
      }
      throw AIModelException(
        message: 'Kimi API请求异常: $e',
        modelName: 'moonshot-v1-8k',
      );
    }
  }
  
  /// 发送消息到MiniMax API
  Future<String> sendMessageToMiniMax({
    required List<ChatMessage> messages,
    required String apiKey,
    String? apiEndpoint,
  }) async {
    try {
      // 检查消息内容是否包含敏感词
      for (final message in messages) {
        if (_securityService.containsSensitiveWords(message.content)) {
          final sensitiveWords = _securityService.getSensitiveWords(message.content);
          throw ContentSafetyException(
            message: '消息包含敏感内容',
            violationType: '敏感词: ${sensitiveWords.join(', ')}',
          );
        }
      }
      
      final endpoint = apiEndpoint ?? 'https://api.minimax.chat/v1/text/chatcompletion';
      
      // 转换消息格式为MiniMax API格式
      final List<Map<String, String>> formattedMessages = messages.map((message) {
        return {
          'sender_type': message.role == MessageRole.user
              ? 'USER'
              : message.role == MessageRole.assistant
                  ? 'BOT'
                  : 'SYSTEM',
          'text': message.content,
        };
      }).toList();
      
      final response = await _client.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'model': 'abab5.5-chat',
          'messages': formattedMessages,
          'temperature': 0.7,
          'top_p': 0.9,
          'tokens_to_generate': 2000,
        }),
      );
      
      if (response.statusCode != 200) {
        throw AIModelException(
          message: 'MiniMax API请求失败: ${response.statusCode}',
          modelName: 'abab5.5-chat',
        );
      }
      
      final responseData = json.decode(response.body);
      return responseData['reply'] as String;
    } catch (e) {
      if (e is ContentSafetyException || e is AIModelException) {
        rethrow;
      }
      throw AIModelException(
        message: 'MiniMax API请求异常: $e',
        modelName: 'abab5.5-chat',
      );
    }
  }
  
  /// 根据提供商发送消息
  Future<String> sendMessage({
    required List<ChatMessage> messages,
    required AIModelConfig modelConfig,
  }) async {
    switch (modelConfig.provider.toLowerCase()) {
      case 'tongyi':
      case '通义千问':
        return sendMessageToTongyi(
          messages: messages,
          apiKey: modelConfig.apiKey,
          apiEndpoint: modelConfig.apiEndpoint,
        );
      case 'deepseek':
        return sendMessageToDeepSeek(
          messages: messages,
          apiKey: modelConfig.apiKey,
          apiEndpoint: modelConfig.apiEndpoint,
        );
      case 'kimi':
        return sendMessageToKimi(
          messages: messages,
          apiKey: modelConfig.apiKey,
          apiEndpoint: modelConfig.apiEndpoint,
        );
      case 'minimax':
        return sendMessageToMiniMax(
          messages: messages,
          apiKey: modelConfig.apiKey,
          apiEndpoint: modelConfig.apiEndpoint,
        );
      default:
        throw AIModelException(
          message: '不支持的AI提供商: ${modelConfig.provider}',
          modelName: modelConfig.modelName,
        );
    }
  }
}
