import 'package:one_moment_app/data/models/media_type.dart';import 'package:geolocator/geolocator.dart';
import 'package:weather/weather.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';

/// 位置和天气服务
class LocationWeatherService {
  final WeatherFactory _weatherFactory;
  
  LocationWeatherService({String? weatherApiKey})
      : _weatherFactory = WeatherFactory(weatherApiKey ?? 'YOUR_OPENWEATHERMAP_API_KEY');
  
  /// 获取当前位置
  Future<Position> getCurrentLocation() async {
    try {
      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationServiceDisabledException();
      }
      
      // 检查位置权限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw PermissionException(message: '位置权限被拒绝');
        }
      }
      
      if (permission == LocationPermission.deniedForever) {
        throw PermissionException(message: '位置权限被永久拒绝，请在设置中启用');
      }
      
      // 获取位置
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } on LocationServiceDisabledException {
      throw PermissionException(message: '位置服务未启用');
    } catch (e) {
      throw PermissionException(message: '无法获取位置: $e');
    }
  }
  
  /// 获取地址描述
  Future<String> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // 这里应该使用地理编码服务，如Google Maps Geocoding API
      // 由于API限制，这里简化为返回坐标
      return '位置: $latitude, $longitude';
    } catch (e) {
      throw NetworkException(message: '无法获取地址: $e');
    }
  }
  
  /// 获取当前天气
  Future<Weather> getCurrentWeather() async {
    try {
      // 获取当前位置
      final position = await getCurrentLocation();
      
      // 获取天气
      return await _weatherFactory.currentWeatherByLocation(
        position.latitude,
        position.longitude,
      );
    } catch (e) {
      if (e is PermissionException) {
        rethrow;
      }
      throw NetworkException(message: '无法获取天气: $e');
    }
  }
  
  /// 获取天气描述
  String getWeatherDescription(Weather weather) {
    return '${weather.weatherMain ?? '未知'}, ${weather.temperature?.celsius?.toStringAsFixed(1) ?? '未知'}°C';
  }
  
  /// 获取位置和天气信息
  Future<Map<String, String>> getLocationAndWeather() async {
    try {
      // 获取当前位置
      final position = await getCurrentLocation();
      
      // 获取地址
      final address = await getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );
      
      // 获取天气
      final weather = await _weatherFactory.currentWeatherByLocation(
        position.latitude,
        position.longitude,
      );
      
      return {
        'location': address,
        'weather': getWeatherDescription(weather),
      };
    } catch (e) {
      if (e is PermissionException || e is NetworkException) {
        rethrow;
      }
      throw NetworkException(message: '无法获取位置和天气: $e');
    }
  }
}
