import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// 日志工具类
class LoggerUtil {
  static final LoggerUtil _instance = LoggerUtil._internal();
  late Logger _logger;

  // 是否启用详细日志
  bool _verbose = false;

  // 日志缓存，用于在应用内显示
  final List<LogEntry> _logBuffer = [];
  static const int _maxBufferSize = 1000;

  factory LoggerUtil() {
    return _instance;
  }

  LoggerUtil._internal() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.verbose : Level.info,
    );
  }

  /// 设置是否启用详细日志
  void setVerbose(bool verbose) {
    _verbose = verbose;
  }

  /// 获取是否启用详细日志
  bool get isVerbose => _verbose;

  /// 获取日志缓存
  List<LogEntry> get logBuffer => List.unmodifiable(_logBuffer);

  /// 清除日志缓存
  void clearLogBuffer() {
    _logBuffer.clear();
  }

  /// 记录调试日志
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message);
    _addToBuffer('DEBUG', message, error, stackTrace);
  }

  /// 记录信息日志
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message);
    _addToBuffer('INFO', message, error, stackTrace);
  }

  /// 记录警告日志
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message);
    _addToBuffer('WARN', message, error, stackTrace);
  }

  /// 记录错误日志
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message);
    _addToBuffer('ERROR', message, error, stackTrace);
  }

  /// 记录网络请求日志
  void network(String method, String url, {dynamic data, dynamic response, dynamic error}) {
    if (!_verbose && !kDebugMode) return;

    final message = 'NETWORK $method $url';
    final details = {
      if (data != null) 'Request': data,
      if (response != null) 'Response': response,
      if (error != null) 'Error': error,
    };

    _logger.i('$message: ${details.toString()}');
    _addToBuffer('NETWORK', '$method $url', details, null);
  }

  /// 添加日志到缓存
  void _addToBuffer(String level, String message, dynamic error, StackTrace? stackTrace) {
    final timestamp = DateTime.now();

    _logBuffer.add(LogEntry(
      level: level,
      message: message,
      error: error,
      stackTrace: stackTrace,
      timestamp: timestamp,
    ));

    // 限制缓存大小
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }
  }
}

/// 日志条目
class LogEntry {
  final String level;
  final String message;
  final dynamic error;
  final StackTrace? stackTrace;
  final DateTime timestamp;

  LogEntry({
    required this.level,
    required this.message,
    this.error,
    this.stackTrace,
    required this.timestamp,
  });

  @override
  String toString() {
    final timeStr = '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
    return '[$timeStr] $level: $message${error != null ? '\nError: $error' : ''}';
  }
}
