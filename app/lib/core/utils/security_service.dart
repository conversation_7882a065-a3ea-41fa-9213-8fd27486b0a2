import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:one_moment_app/core/errors/exceptions.dart';

/// 安全服务
class SecurityService {
  final FlutterSecureStorage _secureStorage;
  static const String _keyPrefix = 'one_moment_';
  static const String _encryptionKeyName = '${_keyPrefix}encryption_key';
  
  // 敏感词列表
  static const List<String> _sensitiveWords = [
    '自杀',
    '抑郁',
    '焦虑',
    '绝望',
    '死亡',
    // 可以根据需要添加更多敏感词
  ];
  
  SecurityService({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ?? const FlutterSecureStorage();
  
  /// 获取或生成加密密钥
  Future<String> _getOrCreateEncryptionKey() async {
    try {
      String? key = await _secureStorage.read(key: _encryptionKeyName);
      
      if (key == null) {
        // 生成新密钥
        final newKey = encrypt.Key.fromSecureRandom(32).base64;
        await _secureStorage.write(key: _encryptionKeyName, value: newKey);
        return newKey;
      }
      
      return key;
    } catch (e) {
      throw AuthException(message: 'Failed to get or create encryption key: $e');
    }
  }
  
  /// 加密数据
  Future<String> encryptData(String data) async {
    try {
      final key = await _getOrCreateEncryptionKey();
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(key)),
      );
      final iv = encrypt.IV.fromLength(16);
      
      final encrypted = encrypter.encrypt(data, iv: iv);
      return '${encrypted.base64}:${iv.base64}';
    } catch (e) {
      throw AuthException(message: 'Failed to encrypt data: $e');
    }
  }
  
  /// 解密数据
  Future<String> decryptData(String encryptedData) async {
    try {
      final parts = encryptedData.split(':');
      if (parts.length != 2) {
        throw AuthException(message: 'Invalid encrypted data format');
      }
      
      final encryptedText = parts[0];
      final ivText = parts[1];
      
      final key = await _getOrCreateEncryptionKey();
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(key)),
      );
      final iv = encrypt.IV.fromBase64(ivText);
      
      return encrypter.decrypt64(encryptedText, iv: iv);
    } catch (e) {
      throw AuthException(message: 'Failed to decrypt data: $e');
    }
  }
  
  /// 安全存储数据
  Future<void> secureWrite(String key, String value) async {
    try {
      await _secureStorage.write(key: '$_keyPrefix$key', value: value);
    } catch (e) {
      throw AuthException(message: 'Failed to securely write data: $e');
    }
  }
  
  /// 安全读取数据
  Future<String?> secureRead(String key) async {
    try {
      return await _secureStorage.read(key: '$_keyPrefix$key');
    } catch (e) {
      throw AuthException(message: 'Failed to securely read data: $e');
    }
  }
  
  /// 安全删除数据
  Future<void> secureDelete(String key) async {
    try {
      await _secureStorage.delete(key: '$_keyPrefix$key');
    } catch (e) {
      throw AuthException(message: 'Failed to securely delete data: $e');
    }
  }
  
  /// 检查内容是否包含敏感词
  bool containsSensitiveWords(String content) {
    final lowerContent = content.toLowerCase();
    
    for (final word in _sensitiveWords) {
      if (lowerContent.contains(word.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  }
  
  /// 获取内容中的敏感词
  List<String> getSensitiveWords(String content) {
    final lowerContent = content.toLowerCase();
    final foundWords = <String>[];
    
    for (final word in _sensitiveWords) {
      if (lowerContent.contains(word.toLowerCase())) {
        foundWords.add(word);
      }
    }
    
    return foundWords;
  }
  
  /// 过滤敏感词（用*替换）
  String filterSensitiveWords(String content) {
    String filteredContent = content;
    
    for (final word in _sensitiveWords) {
      final replacement = '*' * word.length;
      filteredContent = filteredContent.replaceAll(word, replacement);
    }
    
    return filteredContent;
  }
}
