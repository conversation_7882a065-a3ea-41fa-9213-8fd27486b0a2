import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:camera/camera.dart';
import 'package:record/record.dart';
import 'package:video_player/video_player.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';

/// 媒体服务
class MediaService {
  final ImagePicker _imagePicker;
  final Record _audioRecorder;
  CameraController? _cameraController;
  
  MediaService({
    ImagePicker? imagePicker,
    Record? audioRecorder,
  })  : _imagePicker = imagePicker ?? ImagePicker(),
        _audioRecorder = audioRecorder ?? Record();
  
  /// 从相册选择图片
  Future<File?> pickImage({
    ImageSource source = ImageSource.gallery,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int imageQuality = 85,
  }) async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );
      
      if (pickedFile == null) return null;
      
      // 复制到应用目录
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${const Uuid().v4()}${path.extension(pickedFile.path)}';
      final savedImage = File('${appDir.path}/images/$fileName');
      
      // 确保目录存在
      await Directory('${appDir.path}/images').create(recursive: true);
      
      // 复制文件
      await File(pickedFile.path).copy(savedImage.path);
      
      return savedImage;
    } catch (e) {
      throw PermissionException(message: '无法选择图片: $e');
    }
  }
  
  /// 从相册选择视频
  Future<File?> pickVideo({
    ImageSource source = ImageSource.gallery,
    Duration maxDuration = const Duration(seconds: 10),
  }) async {
    try {
      final pickedFile = await _imagePicker.pickVideo(
        source: source,
        maxDuration: maxDuration,
      );
      
      if (pickedFile == null) return null;
      
      // 复制到应用目录
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${const Uuid().v4()}${path.extension(pickedFile.path)}';
      final savedVideo = File('${appDir.path}/videos/$fileName');
      
      // 确保目录存在
      await Directory('${appDir.path}/videos').create(recursive: true);
      
      // 复制文件
      await File(pickedFile.path).copy(savedVideo.path);
      
      return savedVideo;
    } catch (e) {
      throw PermissionException(message: '无法选择视频: $e');
    }
  }
  
  /// 初始化相机
  Future<void> initializeCamera({
    required CameraDescription camera,
    required ResolutionPreset resolution,
  }) async {
    try {
      _cameraController = CameraController(
        camera,
        resolution,
        enableAudio: true,
      );
      
      await _cameraController!.initialize();
    } catch (e) {
      throw PermissionException(message: '无法初始化相机: $e');
    }
  }
  
  /// 释放相机资源
  Future<void> disposeCamera() async {
    if (_cameraController != null) {
      await _cameraController!.dispose();
      _cameraController = null;
    }
  }
  
  /// 拍照
  Future<File?> takePhoto() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      throw PermissionException(message: '相机未初始化');
    }
    
    try {
      final XFile photo = await _cameraController!.takePicture();
      
      // 复制到应用目录
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${const Uuid().v4()}${path.extension(photo.path)}';
      final savedPhoto = File('${appDir.path}/images/$fileName');
      
      // 确保目录存在
      await Directory('${appDir.path}/images').create(recursive: true);
      
      // 复制文件
      await File(photo.path).copy(savedPhoto.path);
      
      return savedPhoto;
    } catch (e) {
      throw PermissionException(message: '无法拍照: $e');
    }
  }
  
  /// 录制视频
  Future<File?> recordVideo({Duration maxDuration = const Duration(seconds: 10)}) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      throw PermissionException(message: '相机未初始化');
    }
    
    try {
      await _cameraController!.startVideoRecording();
      
      // 设置最大录制时间
      await Future.delayed(maxDuration);
      
      final XFile video = await _cameraController!.stopVideoRecording();
      
      // 复制到应用目录
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${const Uuid().v4()}${path.extension(video.path)}';
      final savedVideo = File('${appDir.path}/videos/$fileName');
      
      // 确保目录存在
      await Directory('${appDir.path}/videos').create(recursive: true);
      
      // 复制文件
      await File(video.path).copy(savedVideo.path);
      
      return savedVideo;
    } catch (e) {
      throw PermissionException(message: '无法录制视频: $e');
    }
  }
  
  /// 开始录音
  Future<void> startAudioRecording() async {
    try {
      // 检查麦克风权限
      if (await _audioRecorder.hasPermission()) {
        // 准备录音目录
        final appDir = await getApplicationDocumentsDirectory();
        final audioDir = Directory('${appDir.path}/audios');
        if (!await audioDir.exists()) {
          await audioDir.create(recursive: true);
        }
        
        final fileName = '${const Uuid().v4()}.m4a';
        final filePath = '${audioDir.path}/$fileName';
        
        // 开始录音
        await _audioRecorder.start(
          path: filePath,
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          samplingRate: 44100,
        );
      } else {
        throw PermissionException(message: '没有麦克风权限');
      }
    } catch (e) {
      throw PermissionException(message: '无法开始录音: $e');
    }
  }
  
  /// 停止录音
  Future<File?> stopAudioRecording() async {
    try {
      final path = await _audioRecorder.stop();
      
      if (path == null) return null;
      
      return File(path);
    } catch (e) {
      throw PermissionException(message: '无法停止录音: $e');
    }
  }
  
  /// 获取可用的相机列表
  Future<List<CameraDescription>> getAvailableCameras() async {
    try {
      return await availableCameras();
    } catch (e) {
      throw PermissionException(message: '无法获取相机列表: $e');
    }
  }
  
  /// 创建视频播放器控制器
  VideoPlayerController createVideoPlayerController(File videoFile) {
    return VideoPlayerController.file(videoFile);
  }
  
  /// 创建网络视频播放器控制器
  VideoPlayerController createNetworkVideoPlayerController(String url) {
    return VideoPlayerController.networkUrl(Uri.parse(url));
  }
}
