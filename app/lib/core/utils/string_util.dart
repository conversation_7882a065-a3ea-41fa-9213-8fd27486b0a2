import 'package:one_moment_app/data/models/media_type.dart';/// 字符串工具类
class StringUtil {
  /// 判断字符串是否为空
  static bool isEmpty(String? str) {
    return str == null || str.trim().isEmpty;
  }
  
  /// 判断字符串是否不为空
  static bool isNotEmpty(String? str) {
    return !isEmpty(str);
  }
  
  /// 截断字符串
  static String truncate(String str, int maxLength, {String suffix = '...'}) {
    if (str.length <= maxLength) {
      return str;
    }
    return '${str.substring(0, maxLength)}$suffix';
  }
  
  /// 获取字符串的第一个字符
  static String getFirstChar(String str) {
    if (isEmpty(str)) {
      return '';
    }
    return str[0];
  }
  
  /// 获取字符串的最后一个字符
  static String getLastChar(String str) {
    if (isEmpty(str)) {
      return '';
    }
    return str[str.length - 1];
  }
  
  /// 将字符串首字母大写
  static String capitalize(String str) {
    if (isEmpty(str)) {
      return '';
    }
    return str[0].toUpperCase() + str.substring(1);
  }
  
  /// 将字符串转换为驼峰命名
  static String toCamelCase(String str) {
    if (isEmpty(str)) {
      return '';
    }
    
    final words = str.split(RegExp(r'[_\s-]+'));
    final result = StringBuffer(words[0].toLowerCase());
    
    for (var i = 1; i < words.length; i++) {
      result.write(capitalize(words[i]));
    }
    
    return result.toString();
  }
  
  /// 将字符串转换为帕斯卡命名
  static String toPascalCase(String str) {
    if (isEmpty(str)) {
      return '';
    }
    
    final words = str.split(RegExp(r'[_\s-]+'));
    final result = StringBuffer();
    
    for (final word in words) {
      result.write(capitalize(word));
    }
    
    return result.toString();
  }
  
  /// 将字符串转换为下划线命名
  static String toSnakeCase(String str) {
    if (isEmpty(str)) {
      return '';
    }
    
    final result = StringBuffer();
    
    for (var i = 0; i < str.length; i++) {
      final char = str[i];
      
      if (char == ' ' || char == '-' || char == '_') {
        result.write('_');
      } else if (i > 0 && char.toUpperCase() == char) {
        result.write('_${char.toLowerCase()}');
      } else {
        result.write(char.toLowerCase());
      }
    }
    
    return result.toString();
  }
  
  /// 将字符串转换为短横线命名
  static String toKebabCase(String str) {
    if (isEmpty(str)) {
      return '';
    }
    
    final result = StringBuffer();
    
    for (var i = 0; i < str.length; i++) {
      final char = str[i];
      
      if (char == ' ' || char == '-' || char == '_') {
        result.write('-');
      } else if (i > 0 && char.toUpperCase() == char) {
        result.write('-${char.toLowerCase()}');
      } else {
        result.write(char.toLowerCase());
      }
    }
    
    return result.toString();
  }
  
  /// 获取字符串的字节长度
  static int getByteLength(String str) {
    return utf8.encode(str).length;
  }
  
  /// 检查字符串是否是有效的电子邮件地址
  static bool isValidEmail(String email) {
    final emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegExp.hasMatch(email);
  }
  
  /// 检查字符串是否是有效的手机号码（中国大陆）
  static bool isValidPhoneNumber(String phoneNumber) {
    final phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegExp.hasMatch(phoneNumber);
  }
  
  /// 检查字符串是否是有效的URL
  static bool isValidUrl(String url) {
    final urlRegExp = RegExp(
      r'^(http|https)://[a-zA-Z0-9]+([\-\.]{1}[a-zA-Z0-9]+)*\.[a-zA-Z]{2,5}(:[0-9]{1,5})?(\/.*)?$',
    );
    return urlRegExp.hasMatch(url);
  }
}

/// UTF-8编码
class utf8 {
  /// 将字符串编码为UTF-8字节
  static List<int> encode(String str) {
    final List<int> bytes = [];
    
    for (var i = 0; i < str.length; i++) {
      final codeUnit = str.codeUnitAt(i);
      
      if (codeUnit < 0x80) {
        // 1字节
        bytes.add(codeUnit);
      } else if (codeUnit < 0x800) {
        // 2字节
        bytes.add(0xC0 | (codeUnit >> 6));
        bytes.add(0x80 | (codeUnit & 0x3F));
      } else if (codeUnit < 0x10000) {
        // 3字节
        bytes.add(0xE0 | (codeUnit >> 12));
        bytes.add(0x80 | ((codeUnit >> 6) & 0x3F));
        bytes.add(0x80 | (codeUnit & 0x3F));
      } else {
        // 4字节
        bytes.add(0xF0 | (codeUnit >> 18));
        bytes.add(0x80 | ((codeUnit >> 12) & 0x3F));
        bytes.add(0x80 | ((codeUnit >> 6) & 0x3F));
        bytes.add(0x80 | (codeUnit & 0x3F));
      }
    }
    
    return bytes;
  }
}
