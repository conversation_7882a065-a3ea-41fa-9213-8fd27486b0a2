import 'package:one_moment_app/data/models/media_type.dart';import 'package:intl/intl.dart';

/// 日期工具类
class DateUtil {
  /// 格式化日期（年月日）
  static String formatDate(DateTime date) {
    return DateFormat('yyyy年MM月dd日').format(date);
  }
  
  /// 格式化日期（年月日 时分）
  static String formatDateTime(DateTime date) {
    return DateFormat('yyyy年MM月dd日 HH:mm').format(date);
  }
  
  /// 格式化日期（时分）
  static String formatTime(DateTime date) {
    return DateFormat('HH:mm').format(date);
  }
  
  /// 格式化日期（星期几）
  static String formatWeekday(DateTime date) {
    final List<String> weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekdays[date.weekday - 1];
  }
  
  /// 获取相对时间描述（例如：刚刚、5分钟前、1小时前等）
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else {
      return '${(difference.inDays / 365).floor()}年前';
    }
  }
  
  /// 获取今天的日期（只包含年月日）
  static DateTime today() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
  
  /// 获取昨天的日期
  static DateTime yesterday() {
    final today = DateTime.now();
    return today.subtract(const Duration(days: 1));
  }
  
  /// 获取明天的日期
  static DateTime tomorrow() {
    final today = DateTime.now();
    return today.add(const Duration(days: 1));
  }
  
  /// 获取一周前的日期
  static DateTime oneWeekAgo() {
    final today = DateTime.now();
    return today.subtract(const Duration(days: 7));
  }
  
  /// 获取一个月前的日期
  static DateTime oneMonthAgo() {
    final today = DateTime.now();
    return DateTime(today.year, today.month - 1, today.day);
  }
  
  /// 获取一年前的日期
  static DateTime oneYearAgo() {
    final today = DateTime.now();
    return DateTime(today.year - 1, today.month, today.day);
  }
  
  /// 判断两个日期是否是同一天
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }
  
  /// 判断日期是否是今天
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return isSameDay(date, now);
  }
  
  /// 判断日期是否是昨天
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }
  
  /// 获取当前季节
  static String getCurrentSeason() {
    final now = DateTime.now();
    final month = now.month;
    
    if (month >= 3 && month <= 5) {
      return '春';
    } else if (month >= 6 && month <= 8) {
      return '夏';
    } else if (month >= 9 && month <= 11) {
      return '秋';
    } else {
      return '冬';
    }
  }
}
