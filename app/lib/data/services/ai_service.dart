import 'package:one_moment_app/data/models/media_type.dart';import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/network/ai_service.dart' as core_ai_service;
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/config/ai_config.dart';import 'package:one_moment_app/data/models/ai_config_model.dart';

/// AI服务
class AIService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  final core_ai_service.AIService _coreAIService = core_ai_service.AIService();
  final AIConfigManager _configManager = AIConfigManager();
  AIService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化AI服务');
  }

  /// 发送消息到AI服务
  Future<String> sendMessage(List<ChatMessage> messages) async {
    try {
      // 使用后端API发送消息
      final lastMessage = messages.lastWhere(
        (msg) => msg.role == MessageRole.user,
        orElse: () => throw Exception('没有找到用户消息'),
      );

      // 构建请求参数，包含完整的消息历史
      final requestData = {
        'message': lastMessage.content,
        'history': messages.where((msg) => msg.role != MessageRole.user || msg != lastMessage)
            .map((msg) => {
                  'role': msg.role.toString().split('.').last.toLowerCase(),
                  'content': msg.content,
                })
            .toList(),
      };

      _logger.i('发送AI聊天请求: $requestData');

      final response = await _apiClient.post(
        '/ai/chat',
        data: requestData,
      );

      _logger.i('AI聊天响应: $response');

      if (response is Map<String, dynamic>) {
        // 处理标准API响应格式
        if (response.containsKey('data')) {
          if (response['data'] is String) {
            return response['data'] as String;
          } else if (response['data'] is Map<String, dynamic> &&
                    response['data'].containsKey('message')) {
            return response['data']['message'] as String;
          }
        } else if (response.containsKey('message') && response['message'] is String) {
          return response['message'] as String;
        }
      }

      // 如果无法解析响应，直接返回字符串表示
      return response.toString();
    } catch (e) {
      _logger.e('发送消息失败', e);
      throw Exception('发送消息失败: $e');
    }
  }

  /// 获取当前AI模型配置
  Future<AIConfigModel> getCurrentModel() async {
    try {
      final response = await _apiClient.post('/ai/getCurrentModel');
      return AIConfigModel.fromJson(response as Map<String, dynamic>);
    } catch (e) {
      _logger.e('获取当前AI模型失败', e);

      // 如果API调用失败，使用本地配置
      final localConfig = await _configManager.getModelConfig();
      return AIConfigModel(
        id: '1',
        name: localConfig.modelName,
        provider: localConfig.provider,
        description: '默认AI模型',
        features: ['文本分析', '情感识别'],
      );
    }
  }

  /// 获取可用的AI模型列表
  Future<List<AIConfigModel>> getAvailableModels() async {
    try {
      final response = await _apiClient.post('/ai/getAvailableModels');

      if (response is! List<dynamic>) {
        _logger.e('获取可用AI模型响应格式错误: 预期List，实际${response.runtimeType}');
        throw Exception('获取可用AI模型响应格式错误');
      }

      return response
          .map((item) => AIConfigModel.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.e('获取可用AI模型失败', e);

      // 如果API调用失败，返回默认模型列表
      return [
        AIConfigModel(
          id: '1',
          name: 'Tongyi Qianwen',
          provider: '通义千问',
          description: '阿里云提供的大语言模型',
          features: ['文本分析', '情感识别'],
        ),
        AIConfigModel(
          id: '2',
          name: 'DeepSeek',
          provider: 'DeepSeek',
          description: 'DeepSeek提供的大语言模型',
          features: ['文本分析', '代码生成'],
        ),
        AIConfigModel(
          id: '3',
          name: 'Kimi',
          provider: 'Kimi',
          description: 'Kimi提供的大语言模型',
          features: ['文本分析', '知识问答'],
        ),
        AIConfigModel(
          id: '4',
          name: 'MiniMax',
          provider: 'MiniMax',
          description: 'MiniMax提供的大语言模型',
          features: ['文本分析', '多轮对话'],
        ),
      ];
    }
  }

  /// 设置当前AI模型
  Future<bool> setCurrentModel(String modelId) async {
    try {
      await _apiClient.post('/ai/setCurrentModel', data: {'modelId': modelId});
      return true;
    } catch (e) {
      _logger.e('设置当前AI模型失败', e);

      // 如果API调用失败，使用本地配置
      try {
        // 获取所有模型
        final models = await getAvailableModels();

        // 查找指定ID的模型
        final model = models.firstWhere((m) => m.id == modelId);

        // 保存到本地
        final success = await _configManager.saveModelConfig(AIModelConfig(apiKey: '', 
          modelName: model.name,
          provider: model.provider,
        ));

        return success;
      } catch (e) {
        _logger.e('保存本地AI模型配置失败', e);
        return false;
      }
    }
  }

  /// 分析瞬间内容
  Future<String> analyzeMomentContent(String content) async {
    try {
      _logger.i('发送瞬间内容分析请求: $content');

      final response = await _apiClient.post('/ai/analyzeMoment', data: {'content': content});
      _logger.i('分析瞬间内容响应: $response');

      if (response is Map<String, dynamic>) {
        // 处理标准API响应格式
        if (response.containsKey('data')) {
          if (response['data'] is String) {
            return response['data'] as String;
          } else if (response['data'] is Map<String, dynamic>) {
            if (response['data'].containsKey('analysis')) {
              return response['data']['analysis'] as String;
            } else if (response['data'].containsKey('message')) {
              return response['data']['message'] as String;
            }
          }
        } else if (response.containsKey('analysis')) {
          return response['analysis'] as String;
        } else if (response.containsKey('message') && response['message'] is String) {
          return response['message'] as String;
        }
      }

      // 如果无法解析响应，直接返回字符串表示
      return response.toString();
    } catch (e) {
      _logger.e('分析瞬间内容失败', e);
      throw Exception('分析瞬间内容失败: $e');
    }
  }
}
