import 'dart:io';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// 瞬间服务
class MomentService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();

  MomentService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化瞬间服务');
  }

  /// 获取所有瞬间
  Future<List<MomentModel>> getAllMoments({int page = 0, int size = 10}) async {
    _logger.i('获取所有瞬间: page=$page, size=$size');
    try {
      final response = await _apiClient.post(
        '/moments/getMomentList',
        data: {
          'page': page,
          'size': size,
          'sort': 'createdAt,desc',
        },
      );

      _logger.d('获取所有瞬间响应: $response');

      if (response is! Map<String, dynamic>) {
        _logger.e('获取瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('获取瞬间响应格式错误');
      }

      if (!response.containsKey('content')) {
        _logger.e('获取瞬间响应缺少content字段: $response');

        // 尝试处理不同的响应格式
        if (response.containsKey('data') && response['data'] is List) {
          _logger.i('使用替代响应格式: data字段');
          final content = response['data'] as List<dynamic>;
          return _processContentList(content);
        }

        // 如果响应本身就是一个列表
        if (response.containsKey('_embedded') &&
            response['_embedded'] is Map &&
            (response['_embedded'] as Map).containsKey('moments')) {
          _logger.i('使用替代响应格式: _embedded.moments字段');
          final content = response['_embedded']['moments'] as List<dynamic>;
          return _processContentList(content);
        }

        // 如果响应本身就是一个列表
        if (response is List) {
          _logger.i('响应本身是一个列表');
          return _processContentList(response);
        }

        throw Exception('获取瞬间响应格式错误: 缺少content字段');
      }

      final content = response['content'];
      return _processContentList(content);
    } catch (e) {
      _logger.e('获取瞬间失败', e);

      // 返回空列表，不抛出异常
      _logger.w('返回空列表');
      return [];
    }
  }

  /// 处理内容列表
  List<MomentModel> _processContentList(dynamic content) {
    if (content is! List<dynamic>) {
      _logger.e('内容格式错误: 预期List，实际${content.runtimeType}');
      throw Exception('内容格式错误: 不是List');
    }

    final List<MomentModel> moments = [];
    for (var item in content) {
      try {
        if (item is Map<String, dynamic>) {
          moments.add(_mapResponseToMomentModel(item));
        } else {
          _logger.w('跳过无效的瞬间数据: $item');
        }
      } catch (e) {
        _logger.e('解析瞬间数据失败', e);
      }
    }

    _logger.i('成功处理${moments.length}个瞬间');
    return moments;
  }

  /// 获取指定日期的瞬间
  Future<List<MomentModel>> getMomentsByDate(DateTime date) async {
    try {
      _logger.i('获取指定日期的瞬间: ${date.toString()}');

      final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      final response = await _apiClient.post('/moments/getMomentsByDate', data: {'date': dateStr});

      _logger.d('获取指定日期的瞬间响应: $response');

      // 处理不同的响应格式
      if (response is Map<String, dynamic>) {
        if (response.containsKey('data') && response['data'] is List) {
          _logger.i('使用data字段中的列表');
          final data = response['data'] as List<dynamic>;
          return data.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
        }
      } else if (response is List<dynamic>) {
        _logger.i('响应直接是列表');
        return response.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
      }

      _logger.e('获取指定日期的瞬间响应格式错误: $response');
      return []; // 返回空列表而不是抛出异常
    } catch (e) {
      _logger.e('获取指定日期的瞬间失败: $e');
      return []; // 返回空列表而不是抛出异常
    }
  }

  /// 获取指定日期范围的瞬间
  Future<List<MomentModel>> getMomentsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      _logger.i('获取日期范围内的瞬间: ${startDate.toString()} - ${endDate.toString()}');

      final startDateStr = '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')}';
      final endDateStr = '${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}';

      final response = await _apiClient.post('/moments/getMomentsByDateRange', data: {
        'startDate': startDateStr,
        'endDate': endDateStr,
      });

      _logger.d('获取日期范围内的瞬间响应: $response');

      // 处理不同的响应格式
      if (response is Map<String, dynamic>) {
        if (response.containsKey('data') && response['data'] is List) {
          _logger.i('使用data字段中的列表');
          final data = response['data'] as List<dynamic>;
          return data.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
        } else if (response.containsKey('content') && response['content'] is List) {
          _logger.i('使用content字段中的列表（分页响应）');
          final data = response['content'] as List<dynamic>;
          return data.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
        } else {
          _logger.e('获取日期范围内的瞬间响应格式错误: 无法找到data或content字段');
          return [];
        }
      } else if (response is List<dynamic>) {
        _logger.i('响应直接是列表');
        return response.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
      } else {
        _logger.e('获取日期范围内的瞬间响应格式错误: 预期List或Map，实际${response.runtimeType}');
        return [];
      }
    } catch (e) {
      _logger.e('获取日期范围内的瞬间失败: $e');
      return [];
    }
  }

  /// 获取随机瞬间
  Future<MomentModel> getRandomMoment() async {
    try {
      final response = await _apiClient.post('/moments/getRandomMoment');
      return _mapResponseToMomentModel(response as Map<String, dynamic>);
    } catch (e) {
      throw Exception('获取随机瞬间失败: $e');
    }
  }

  /// 获取指定ID的瞬间
  Future<MomentModel> getMomentById(String id) async {
    _logger.i('获取瞬间详情: id=$id');
    try {
      // 确保ID是数字格式
      final numericId = int.tryParse(id) ?? id;
      final response = await _apiClient.post('/moments/getMomentById', data: {'momentId': numericId});

      _logger.d('获取瞬间详情响应: $response');

      if (response is! Map<String, dynamic>) {
        _logger.e('获取瞬间详情响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('获取瞬间详情响应格式错误');
      }

      return _mapResponseToMomentModel(response);
    } catch (e) {
      _logger.e('获取瞬间详情失败', e);
      throw Exception('获取瞬间详情失败: $e');
    }
  }

  /// 创建瞬间
  Future<MomentModel> createMoment(MomentModel moment, {File? mediaFile}) async {
    _logger.i('创建瞬间: ${moment.content}, 媒体类型: ${moment.mediaType}');
    try {
      // 上传媒体文件
      String? mediaUrl;
      if (mediaFile != null) {
        _logger.i('上传媒体文件: ${mediaFile.path}');
        try {
          // 创建表单数据
          final formData = FormData.fromMap({
            'file': await MultipartFile.fromFile(
              mediaFile.path,
              filename: mediaFile.path.split('/').last,
            ),
          });

          // 上传文件
          final response = await _apiClient.post(
            '/moments/upload',
            data: formData,
          );

          // 处理响应
          mediaUrl = response as String;
          _logger.i('媒体文件上传成功: $mediaUrl');
        } catch (e) {
          _logger.e('媒体文件上传失败', e);
          throw Exception('媒体文件上传失败: $e');
        }
      }

      // 创建瞬间请求
      final contentType = moment.mediaType == MediaType.none
          ? 'TEXT'
          : moment.mediaType.toString().split('.').last.toUpperCase();

      _logger.d('准备创建瞬间请求，内容类型: $contentType');

      final request = {
        'contentType': contentType,
        'textContent': moment.content,
        'mediaUrl': mediaUrl,
        'locationName': moment.location,
        'latitude': 0.0, // 暂不支持
        'longitude': 0.0, // 暂不支持
        'weather': moment.weather,
        'temperature': 0.0, // 暂不支持
        'mood': moment.mood.toString().split('.').last.toUpperCase(),
        'isPrivate': moment.isPrivate,
        'tags': [], // 暂不支持标签
      };

      _logger.d('发送创建瞬间请求: $request');

      final response = await _apiClient.post('/moments/createMoment', data: request);

      _logger.d('创建瞬间响应: $response');

      if (response is! Map<String, dynamic>) {
        _logger.e('创建瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('创建瞬间响应格式错误');
      }

      // 检查是否是错误响应
      if (response.containsKey('success') && response['success'] == false) {
        final errorMessage = response['message'] ?? '创建瞬间失败';
        _logger.e('创建瞬间失败: $errorMessage');
        throw Exception(errorMessage);
      }

      // 处理成功响应，提取data字段
      final momentData = response.containsKey('data') ? response['data'] : response;
      final result = _mapResponseToMomentModel(momentData);
      _logger.i('瞬间创建成功: ${result.id}');
      return result;
    } catch (e) {
      _logger.e('创建瞬间失败', e);
      throw Exception('创建瞬间失败: $e');
    }
  }

  /// 更新瞬间
  Future<MomentModel> updateMoment(
    String id,
    String content, {
    String? location,
    String? weather,
    MoodType? mood,
    bool? isPrivate,
  }) async {
    _logger.i('更新瞬间: id=$id');
    try {
      // 确保ID是数字格式
      final numericId = int.tryParse(id) ?? id;

      final request = {
        'textContent': content,
        'locationName': location,
        'weather': weather,
        'mood': mood?.toString().split('.').last.toUpperCase(),
        'isPrivate': isPrivate,
      };

      _logger.d('更新瞬间请求: $request');

      // 添加momentId到请求中
      request['momentId'] = numericId;
      final response = await _apiClient.post('/moments/updateMoment', data: request);

      _logger.d('更新瞬间响应: $response');

      if (response is! Map<String, dynamic>) {
        _logger.e('更新瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('更新瞬间响应格式错误');
      }

      return _mapResponseToMomentModel(response);
    } catch (e) {
      _logger.e('更新瞬间失败', e);
      throw Exception('更新瞬间失败: $e');
    }
  }

  /// 更新瞬间（使用模型）
  @Deprecated('请使用 updateMoment(String id, String content, ...) 方法')
  Future<MomentModel> updateMomentModel(MomentModel moment) async {
    try {
      return updateMoment(
        moment.id,
        moment.content,
        location: moment.location,
        weather: moment.weather,
        mood: moment.mood,
        isPrivate: moment.isPrivate,
      );
    } catch (e) {
      throw Exception('更新瞬间失败: $e');
    }
  }

  /// 删除瞬间
  Future<void> deleteMoment(String id) async {
    _logger.i('删除瞬间: id=$id');
    try {
      // 确保ID是数字格式
      final numericId = int.tryParse(id) ?? id;
      await _apiClient.post('/moments/deleteMoment', data: {'momentId': numericId});
      _logger.i('删除瞬间成功: id=$id');
    } catch (e) {
      _logger.e('删除瞬间失败', e);
      throw Exception('删除瞬间失败: $e');
    }
  }

  /// 将API响应映射为瞬间模型
  MomentModel _mapResponseToMomentModel(Map<String, dynamic> response) {
    _logger.d('映射瞬间响应: $response');

    try {
      return MomentModel(
        id: response['id']?.toString() ?? '',
        userId: response['userId']?.toString() ?? '',
        username: response['username']?.toString() ?? '',
        contentType: response['contentType']?.toString() ?? 'none',
        textContent: response['textContent']?.toString() ?? '',
        createdAt: response['createdAt'] != null
            ? DateTime.parse(response['createdAt'].toString())
            : DateTime.now(),
        locationName: response['locationName']?.toString(),
        weather: response['weather']?.toString(),
        temperature: response['temperature'] != null
            ? double.tryParse(response['temperature'].toString())
            : null,
        latitude: response['latitude'] != null
            ? double.tryParse(response['latitude'].toString())
            : null,
        longitude: response['longitude'] != null
            ? double.tryParse(response['longitude'].toString())
            : null,
        mediaUrl: response['mediaUrl']?.toString(),
        mediaObjectKey: response['mediaObjectKey']?.toString(),
        thumbnailUrl: response['thumbnailUrl']?.toString(),
        mood: _mapMoodType(response['mood']?.toString()),
        isPrivate: response['isPrivate'] as bool? ?? false,
      );
    } catch (e) {
      _logger.e('映射瞬间响应失败', e);
      _logger.e('响应数据: $response');

      // 尝试使用不同的字段名
      return MomentModel(
        id: (response['id'] ?? '').toString(),
        userId: response['userId']?.toString() ?? '',
        username: response['username']?.toString() ?? '',
        contentType: response['contentType']?.toString() ?? 'none',
        textContent: response['textContent'] ?? response['content'] ?? '',
        createdAt: response['createdAt'] != null
            ? DateTime.parse(response['createdAt'].toString())
            : DateTime.now(),
        locationName: response['locationName'] ?? response['location'],
        weather: response['weather'],
        temperature: response['temperature'] != null
            ? double.tryParse(response['temperature'].toString())
            : null,
        latitude: response['latitude'] != null
            ? double.tryParse(response['latitude'].toString())
            : null,
        longitude: response['longitude'] != null
            ? double.tryParse(response['longitude'].toString())
            : null,
        mediaUrl: response['mediaUrl'] ?? response['thumbnailUrl'],
        mediaObjectKey: response['mediaObjectKey'],
        thumbnailUrl: response['thumbnailUrl'],
        mood: _mapMoodType(response['mood']?.toString()),
        isPrivate: response['isPrivate'] ?? false,
      );
    }
  }

  /// 映射媒体类型
  MediaType _mapMediaType(String? contentType) {
    if (contentType == null) return MediaType.none;

    switch (contentType.toUpperCase()) {
      case 'IMAGE':
        return MediaType.image;
      case 'VIDEO':
        return MediaType.video;
      case 'AUDIO':
        return MediaType.audio;
      default:
        return MediaType.none;
    }
  }

  /// 映射心情类型
  MoodType _mapMoodType(String? mood) {
    if (mood == null) return MoodType.neutral;

    switch (mood.toLowerCase()) {
      case 'happy':
        return MoodType.happy;
      case 'calm':
        return MoodType.calm;
      case 'sad':
        return MoodType.sad;
      case 'excited':
        return MoodType.excited;
      case 'anxious':
        return MoodType.anxious;
      default:
        return MoodType.neutral;
    }
  }
}
