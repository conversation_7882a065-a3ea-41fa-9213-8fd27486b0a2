import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// 阿里云OSS服务
class OssService {
  final ApiClient _apiClient;
  final Dio _dio = Dio();

  /// OSS配置
  Map<String, dynamic>? _ossConfig;

  /// 构造函数
  OssService({required ApiClient apiClient}) : _apiClient = apiClient;

  /// 获取OSS配置
  Future<Map<String, dynamic>> _getOssConfig() async {
    if (_ossConfig != null) {
      return _ossConfig!;
    }

    try {
      final response = await _apiClient.post('/api/oss/config', data: {});
      _ossConfig = response['data'];
      return _ossConfig!;
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('获取OSS配置失败: $e');
      throw Exception('获取OSS配置失败');
    }
  }

  /// 上传文件到OSS
  Future<String> uploadFile({
    required File file,
    required String objectKey,
  }) async {
    try {
      // 获取OSS配置
      final config = await _getOssConfig();

      // 构建表单数据
      final formData = FormData.fromMap({
        'OSSAccessKeyId': config['accessKeyId'],
        'policy': config['policy'],
        'signature': config['signature'],
        'key': objectKey,
        'success_action_status': '200',
        'file': await MultipartFile.fromFile(file.path),
      });

      // 上传文件
      final response = await _dio.post(
        config['host'],
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      // 检查响应状态
      if (response.statusCode == 200) {
        // 返回文件URL
        return '${config['host']}/$objectKey';
      } else {
        throw Exception('上传文件失败: ${response.statusCode}');
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('上传文件到OSS失败: $e');
      throw Exception('上传文件到OSS失败');
    }
  }

  /// 下载文件
  Future<File> downloadFile({
    required String url,
    required String savePath,
  }) async {
    try {
      // 下载文件
      final response = await _dio.download(
        url,
        savePath,
        options: Options(
          responseType: ResponseType.bytes,
        ),
      );

      // 检查响应状态
      if (response.statusCode == 200) {
        return File(savePath);
      } else {
        throw Exception('下载文件失败: ${response.statusCode}');
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('从OSS下载文件失败: $e');
      throw Exception('从OSS下载文件失败');
    }
  }

  /// 获取文件URL
  String getFileUrl(String objectKey) {
    if (_ossConfig == null) {
      throw Exception('OSS配置未初始化');
    }

    return '${_ossConfig!['host']}/$objectKey';
  }
}
