import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';

/// 通义千问服务
/// 通过后端API调用通义千问服务
class TongyiService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();

  /// 构造函数
  TongyiService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化通义千问服务');
  }

  /// 发送聊天消息
  /// 通过后端API调用通义千问服务
  Future<String> sendMessage({
    required List<AgentChatMessageModel> messages,
    required String modelName,
  }) async {
    try {
      _logger.i('发送消息到通义千问: 消息数量=${messages.length}, 模型=$modelName');

      // 构建请求参数
      final requestBody = {
        'model': modelName,
        'messages': messages.map((message) => {
          'role': message.role.toString().split('.').last.toLowerCase(),
          'content': message.content,
        }).toList(),
      };

      _logger.d('发送请求到后端AI服务: $requestBody');

      // 通过后端API发送请求
      final response = await _apiClient.post(
        '/ai/chat',
        data: {
          'message': messages.last.content,
          'model': modelName,
        },
      );

      _logger.d('收到后端AI服务响应: $response');

      // 解析响应
      if (response is Map<String, dynamic>) {
        _logger.d('解析AI响应: $response');

        // 检查是否有数据字段
        if (response.containsKey('data') && response['data'] is Map<String, dynamic>) {
          final data = response['data'] as Map<String, dynamic>;
          if (data.containsKey('message') && data['message'] != null) {
            _logger.d('从data.message获取响应: ${data['message']}');
            return data['message'] as String;
          }
        }

        // 检查是否直接包含消息字段
        if (response.containsKey('message') && response['message'] != null) {
          _logger.d('从message字段获取响应: ${response['message']}');
          return response['message'] as String;
        }

        // 检查是否有错误信息
        if (response.containsKey('error') && response['error'] != null) {
          _logger.e('服务器返回错误: ${response['error']}');
          return '抱歉，服务器返回了一个错误: ${response['error']}';
        }

        // 尝试将整个响应转换为字符串
        _logger.d('尝试使用整个响应作为消息');
        return response.toString();
      }

      _logger.e('解析AI响应失败: $response');
      return '抱歉，我无法理解服务器的响应。';
    } catch (e) {
      _logger.e('发送消息到通义千问失败: $e');
      // 返回错误消息
      return '抱歉，我遇到了一些问题，无法回应您的消息。错误信息: $e';
    }
  }

  /// 分析内容
  Future<Map<String, dynamic>> analyzeContent(String content) async {
    try {
      _logger.i('分析内容: ${content.length}字符');

      final response = await _apiClient.post(
        '/ai/analyzeMoment',
        data: {
          'content': content,
        },
      );

      _logger.d('收到内容分析响应: $response');

      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response['data'] is Map<String, dynamic>) {
        return response['data'] as Map<String, dynamic>;
      }

      _logger.e('解析内容分析响应失败: $response');
      return {'error': '无法解析服务器响应'};
    } catch (e) {
      _logger.e('内容分析失败: $e');
      return {'error': '内容分析失败: $e'};
    }
  }

  /// 获取当前AI模型信息
  Future<Map<String, dynamic>> getCurrentModel() async {
    try {
      _logger.i('获取当前AI模型信息');

      final response = await _apiClient.post('/ai/getCurrentModel');

      _logger.d('收到当前AI模型信息响应: $response');

      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response['data'] is Map<String, dynamic>) {
        return response['data'] as Map<String, dynamic>;
      }

      _logger.e('解析当前AI模型信息响应失败: $response');
      return {'name': '通义千问', 'provider': '阿里云'};
    } catch (e) {
      _logger.e('获取当前AI模型信息失败: $e');
      return {'name': '通义千问', 'provider': '阿里云'};
    }
  }

  /// 获取支持的模型列表
  Future<List<Map<String, dynamic>>> getAvailableModels() async {
    try {
      _logger.i('获取可用AI模型列表');

      final response = await _apiClient.post('/ai/getAvailableModels');

      _logger.d('收到可用AI模型列表响应: $response');

      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response['data'] is List) {
        return (response['data'] as List).cast<Map<String, dynamic>>();
      }

      _logger.e('解析可用AI模型列表响应失败: $response');
      return [{'name': '通义千问', 'provider': '阿里云'}];
    } catch (e) {
      _logger.e('获取可用AI模型列表失败: $e');
      return [{'name': '通义千问', 'provider': '阿里云'}];
    }
  }

  /// 与AI聊天
  Future<String> chat(int userId, String message) async {
    try {
      _logger.i('发送聊天消息: userId=$userId, message=${message.length}字符');

      final response = await _apiClient.post(
        '/ai/chat',
        data: {
          'userId': userId,
          'message': message,
        },
      );

      _logger.d('收到聊天响应: $response');

      // 解析响应
      if (response is Map<String, dynamic>) {
        // 检查是否有数据字段
        if (response.containsKey('data') && response['data'] != null) {
          return response['data'].toString();
        }

        // 检查是否直接包含消息字段
        if (response.containsKey('message') && response['message'] != null) {
          return response['message'] as String;
        }

        // 检查是否有错误信息
        if (response.containsKey('error') && response['error'] != null) {
          _logger.e('服务器返回错误: ${response['error']}');
          return '抱歉，服务器返回了一个错误: ${response['error']}';
        }

        // 尝试将整个响应转换为字符串
        return response.toString();
      }

      if (response is String) {
        return response;
      }

      _logger.e('解析聊天响应失败: $response');
      return '抱歉，我无法理解服务器的响应。';
    } catch (e) {
      _logger.e('聊天失败: $e');
      return '抱歉，我遇到了一些问题，无法回应您的消息。';
    }
  }
}
