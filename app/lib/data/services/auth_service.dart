import 'package:one_moment_app/core/storage/local_storage.dart';import 'package:one_moment_app/data/models/media_type.dart';import 'package:dio/dio.dart';import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/auth_model.dart';

/// 认证服务
class AuthService {
  final ApiClient _apiClient;
  final LocalStorage _localStorage;
  final LoggerUtil _logger = LoggerUtil();

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_info';

  AuthService({
    required ApiClient apiClient,
    required LocalStorage localStorage,
  })  : _apiClient = apiClient,
        _localStorage = localStorage {
    _logger.i('初始化认证服务');
  }

  /// 用户注册
  Future<LoginResponse> register(RegisterRequest request) async {
    try {
      _logger.i('用户注册: phone=${request.phone}');

      final response = await _apiClient.post(
        '/auth/register',
        data: request.toJson(),
      );

      if (response['success'] == true && response['data'] != null) {
        final loginResponse = LoginResponse.fromJson(response['data']);
        await _saveAuthData(loginResponse);
        _logger.i('用户注册成功');
        return loginResponse;
      } else {
        throw Exception(response['message'] ?? '注册失败');
      }
    } catch (e) {
      _logger.e('用户注册失败', e);
      rethrow;
    }
  }

  /// 用户登录
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      _logger.i('用户登录: loginType=${request.loginType}, phone=${request.phone}');

      final response = await _apiClient.post(
        '/auth/login',
        data: request.toJson(),
      );

      if (response['success'] == true && response['data'] != null) {
        final loginResponse = LoginResponse.fromJson(response['data']);
        await _saveAuthData(loginResponse);
        _logger.i('用户登录成功');
        return loginResponse;
      } else {
        throw Exception(response['message'] ?? '登录失败');
      }
    } catch (e) {
      _logger.e('用户登录失败', e);
      rethrow;
    }
  }

  /// 刷新令牌
  Future<LoginResponse> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        throw Exception('刷新令牌不存在');
      }

      _logger.i('刷新令牌');

      final response = await _apiClient.post(
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
      );

      if (response['success'] == true && response['data'] != null) {
        final loginResponse = LoginResponse.fromJson(response['data']);
        await _saveAuthData(loginResponse);
        _logger.i('令牌刷新成功');
        return loginResponse;
      } else {
        throw Exception(response['message'] ?? '刷新令牌失败');
      }
    } catch (e) {
      _logger.e('刷新令牌失败', e);
      await logout(); // 刷新失败时清除本地数据
      rethrow;
    }
  }

  /// 检查登录状态
  Future<Map<String, dynamic>> checkStatus() async {
    try {
      final response = await _apiClient.post('/auth/checkStatus');
      return response['data'] ?? {};
    } catch (e) {
      _logger.e('检查登录状态失败', e);
      return {'loginRequired': false, 'message': '检查登录状态失败'};
    }
  }

  /// 检查AI功能登录状态
  Future<Map<String, dynamic>> checkAILoginStatus() async {
    try {
      final accessToken = await getAccessToken();
      final headers = accessToken != null
          ? {'Authorization': 'Bearer $accessToken'}
          : <String, String>{};

      final response = await _apiClient.post(
        '/auth/checkAILoginStatus',
        headers: headers,
      );
      return response['data'] ?? {};
    } catch (e) {
      _logger.e('检查AI登录状态失败', e);
      return {'isLoggedIn': false, 'loginRequired': true, 'message': 'AI对话功能需要登录'};
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      _logger.i('用户登出');
      await _clearAuthData();
    } catch (e) {
      _logger.e('用户登出失败', e);
    }
  }

  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    return await _localStorage.getString(_accessTokenKey);
  }

  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    return await _localStorage.getString(_refreshTokenKey);
  }

  /// 获取用户信息
  Future<UserModel?> getUserInfo() async {
    final userJson = await _localStorage.getString(_userKey);
    if (userJson != null) {
      try {
        final userMap = Map<String, dynamic>.from(
          await _localStorage.getMap(_userKey) ?? {},
        );
        return UserModel.fromJson(userMap);
      } catch (e) {
        _logger.e('解析用户信息失败', e);
        return null;
      }
    }
    return null;
  }

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    return accessToken != null && accessToken.isNotEmpty;
  }

  /// 保存认证数据
  Future<void> _saveAuthData(LoginResponse loginResponse) async {
    await _localStorage.setString(_accessTokenKey, loginResponse.accessToken);
    await _localStorage.setString(_refreshTokenKey, loginResponse.refreshToken);
    await _localStorage.setMap(_userKey, loginResponse.user.toJson());
  }

  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await _localStorage.remove(_accessTokenKey);
    await _localStorage.remove(_refreshTokenKey);
    await _localStorage.remove(_userKey);
  }

  /// 初始化测试账号
  Future<String> initTestAccount() async {
    try {
      _logger.i('初始化测试账号');

      final response = await _apiClient.post('/auth/initTestAccount');

      if (response['success'] == true) {
        return response['data'] ?? '测试账号初始化成功';
      } else {
        throw Exception(response['message'] ?? '初始化测试账号失败');
      }
    } catch (e) {
      _logger.e('初始化测试账号失败', e);
      rethrow;
    }
  }
}
