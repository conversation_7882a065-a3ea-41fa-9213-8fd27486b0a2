import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';

/// Agent服务
class AgentService {
  late final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();

  AgentService({ApiClient? apiClient}) {
    if (apiClient != null) {
      _apiClient = apiClient;
    } else {
      // For testing, we'll just use a mock implementation
      _apiClient = _createMockApiClient();
    }
    _logger.i('初始化Agent服务');
  }

  /// 创建模拟API客户端
  ApiClient _createMockApiClient() {
    return ApiClient(
      localStorage: LocalStorage(
        SharedPreferences.getInstance() as SharedPreferences
      )
    );
  }

  /// 获取所有Agent配置
  Future<List<AgentConfigModel>> getAllAgentConfigs() async {
    try {
      _logger.i('获取所有Agent配置');

      final response = await _apiClient.post('/agent/getAllAgentConfigs');

      final List<dynamic> data = response['data'];
      return data.map((json) => AgentConfigModel.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('获取Agent配置失败', e);
      throw ServerException(
        message: '获取Agent配置失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      _logger.e('获取Agent配置失败', e);
      throw ServerException(message: '获取Agent配置失败: $e');
    }
  }

  /// 获取用户的所有聊天会话
  Future<List<AgentChatSessionModel>> getUserChatSessions(int userId) async {
    try {
      _logger.i('获取用户聊天会话: userId=$userId');

      final response = await _apiClient.post(
        '/agent/getUserChatSessions',
        data: {'userId': userId},
      );

      final List<dynamic> data = response['data'];
      return data.map((json) => AgentChatSessionModel.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('获取用户聊天会话失败', e);
      throw ServerException(
        message: '获取聊天会话失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      _logger.e('获取用户聊天会话失败', e);
      throw ServerException(message: '获取聊天会话失败: $e');
    }
  }

  /// 获取聊天会话详情
  Future<AgentChatSessionModel> getChatSessionDetail(int sessionId) async {
    try {
      _logger.i('获取聊天会话详情: sessionId=$sessionId');

      final response = await _apiClient.post(
        '/agent/getChatSessionDetail',
        data: {'sessionId': sessionId},
      );

      return AgentChatSessionModel.fromJson(response['data']);
    } on DioException catch (e) {
      _logger.e('获取聊天会话详情失败', e);
      throw ServerException(
        message: '获取聊天会话详情失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      _logger.e('获取聊天会话详情失败', e);
      throw ServerException(message: '获取聊天会话详情失败: $e');
    }
  }

  /// 创建聊天会话
  Future<AgentChatSessionModel> createChatSession({
    required int userId,
    required int agentId,
    required String title,
  }) async {
    try {
      _logger.i('创建聊天会话: userId=$userId, agentId=$agentId, title=$title');

      final response = await _apiClient.post(
        '/agent/createChatSession',
        data: {
          'userId': userId,
          'agentId': agentId,
          'title': title,
        },
      );

      return AgentChatSessionModel.fromJson(response['data']);
    } on DioException catch (e) {
      _logger.e('创建聊天会话失败', e);
      throw ServerException(
        message: '创建聊天会话失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      _logger.e('创建聊天会话失败', e);
      throw ServerException(message: '创建聊天会话失败: $e');
    }
  }

  /// 发送聊天消息
  Future<AgentChatMessageModel> sendChatMessage({
    required int sessionId,
    required String content,
  }) async {
    try {
      _logger.i('发送聊天消息: sessionId=$sessionId, content=$content');

      final response = await _apiClient.post(
        '/agent/sendChatMessage',
        data: {
          'sessionId': sessionId,
          'content': content,
        },
      );

      return AgentChatMessageModel.fromJson(response['data']);
    } on DioException catch (e) {
      _logger.e('发送聊天消息失败', e);
      throw ServerException(
        message: '发送聊天消息失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      _logger.e('发送聊天消息失败', e);
      throw ServerException(message: '发送聊天消息失败: $e');
    }
  }

  /// 模拟获取Agent配置
  List<AgentConfigModel> getMockAgentConfigs() {
    _logger.i('获取模拟Agent配置');

    return [
      AgentConfigModel(
        id: 1,
        name: '心灵导师',
        avatar: 'assets/images/agents/mentor.png',
        description: '富有智慧的心灵导师，帮助你解决生活中的困惑',
        prompt: '你是一个富有智慧的心灵导师，善于倾听和给予建议。你的回答应该温暖、有智慧，并且能够帮助用户解决生活中的困惑。',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      AgentConfigModel(
        id: 2,
        name: '情绪疗愈师',
        avatar: 'assets/images/agents/therapist.png',
        description: '专注于情绪疗愈，帮助你缓解压力和焦虑',
        prompt: '你是一个专注于情绪疗愈的心理咨询师，善于帮助用户缓解压力和焦虑。你的回答应该充满理解和支持，并提供实用的情绪管理技巧。',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      AgentConfigModel(
        id: 3,
        name: '创意伙伴',
        avatar: 'assets/images/agents/creative.png',
        description: '激发你的创造力，帮助你找到新的灵感',
        prompt: '你是一个充满创意的伙伴，善于激发用户的创造力和想象力。你的回答应该充满灵感和新颖的想法，帮助用户突破思维限制。',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      AgentConfigModel(
        id: 4,
        name: '职场顾问',
        avatar: 'assets/images/agents/career.png',
        description: '提供职业发展建议，帮助你规划职业道路',
        prompt: '你是一个经验丰富的职场顾问，善于提供职业发展建议。你的回答应该专业、实用，并且能够帮助用户规划职业道路和解决工作中的问题。',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 模拟获取用户聊天会话
  List<AgentChatSessionModel> getMockUserChatSessions(int userId) {
    _logger.i('获取模拟用户聊天会话: userId=$userId');

    final agents = getMockAgentConfigs();

    return [
      AgentChatSessionModel(
        id: 1,
        userId: userId,
        agentId: 1,
        title: '关于生活的困惑',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
        agent: agents[0],
      ),
      AgentChatSessionModel(
        id: 2,
        userId: userId,
        agentId: 2,
        title: '如何缓解压力',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        agent: agents[1],
      ),
      AgentChatSessionModel(
        id: 3,
        userId: userId,
        agentId: 3,
        title: '寻找创作灵感',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        agent: agents[2],
      ),
    ];
  }

  /// 模拟获取聊天会话详情
  AgentChatSessionModel getMockChatSessionDetail(int sessionId) {
    _logger.i('获取模拟聊天会话详情: sessionId=$sessionId');

    final agents = getMockAgentConfigs();
    final agent = agents[(sessionId - 1) % agents.length];

    return AgentChatSessionModel(
      id: sessionId,
      userId: 1,
      agentId: agent.id,
      title: '模拟会话 $sessionId',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
      agent: agent,
      messages: _getMockChatMessages(sessionId),
    );
  }

  /// 模拟获取聊天消息
  List<AgentChatMessageModel> _getMockChatMessages(int sessionId) {
    return [
      AgentChatMessageModel(
        id: 1,
        sessionId: sessionId,
        role: MessageRole.system,
        content: '欢迎使用One Moment的AI助手功能，我将尽力帮助你解决问题。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      AgentChatMessageModel(
        id: 2,
        sessionId: sessionId,
        role: MessageRole.user,
        content: '你好，我最近感到有些迷茫，不知道该如何规划未来。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 25)),
      ),
      AgentChatMessageModel(
        id: 3,
        sessionId: sessionId,
        role: MessageRole.assistant,
        content: '感谢你的分享。迷茫是很正常的感受，每个人在人生的不同阶段都可能经历这种感觉。让我们一起来探讨一下，你现在最关心的是什么方面的规划呢？是学业、职业还是个人发展？',
        createdAt: DateTime.now().subtract(const Duration(minutes: 24)),
      ),
      AgentChatMessageModel(
        id: 4,
        sessionId: sessionId,
        role: MessageRole.user,
        content: '主要是职业方面的，我不确定现在的工作是否适合我长期发展。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
      ),
      AgentChatMessageModel(
        id: 5,
        sessionId: sessionId,
        role: MessageRole.assistant,
        content: '理解你的顾虑。关于职业发展，我建议可以从以下几个方面思考：\n\n1. 回顾一下目前工作中你最享受的部分和最不喜欢的部分\n2. 思考你的长期职业目标是什么\n3. 评估现在的工作是否能帮助你积累有价值的经验和技能\n4. 考虑你的个人价值观和工作环境是否匹配\n\n你能分享一下你现在工作中最喜欢和最不喜欢的方面吗？这样我们可以更深入地讨论。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 18)),
      ),
    ];
  }
}
