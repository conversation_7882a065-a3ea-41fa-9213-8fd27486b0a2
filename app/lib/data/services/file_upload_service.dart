import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'package:dio/dio.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// 文件上传响应
class FileUploadResponse {
  final String fileUrl;
  final String objectKey;

  FileUploadResponse({required this.fileUrl, required this.objectKey});

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      fileUrl: json['fileUrl'] as String,
      objectKey: json['objectKey'] as String,
    );
  }
}

/// 文件上传服务
class FileUploadService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();

  /// 构造函数
  FileUploadService({required ApiClient apiClient}) : _apiClient = apiClient;

  /// 上传文件到后端
  Future<FileUploadResponse> uploadFile(File file) async {
    _logger.i('开始上传文件: ${file.path}');

    try {
      // 创建表单数据
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
      });

      // 发送请求
      final response = await _apiClient.dio.post(
        '/moments/upload',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      // 检查响应
      if (response.statusCode == 200) {
        // 处理新的API响应格式
        final responseData = response.data;
        _logger.i('文件上传成功: $responseData');

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data') &&
            responseData.containsKey('success')) {
          // 新的API响应格式
          if (responseData['success'] == true) {
            final data = responseData['data'];
            if (data is Map<String, dynamic>) {
              return FileUploadResponse.fromJson(data);
            } else {
              // 旧格式，只返回URL
              return FileUploadResponse(
                fileUrl: data.toString(),
                objectKey: '',
              );
            }
          } else {
            throw Exception('上传文件失败: ${responseData['message']}');
          }
        } else {
          // 旧的API响应格式或其他格式
          return FileUploadResponse(
            fileUrl: responseData.toString(),
            objectKey: '',
          );
        }
      } else {
        throw Exception('上传文件失败: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('上传文件失败: $e');
      throw Exception('上传文件失败: $e');
    }
  }
}
