import 'package:one_moment_app/data/models/media_type.dart';/// 天气模型
class WeatherModel {
  final String cityName;
  final String weatherCondition;
  final double temperature;
  final int? humidity;
  final double? windSpeed;
  final String? windDirection;
  final DateTime updatedAt;

  WeatherModel({
    required this.cityName,
    required this.weatherCondition,
    required this.temperature,
    this.humidity,
    this.windSpeed,
    this.windDirection,
    required this.updatedAt,
  });

  /// 从JSON创建天气模型
  factory WeatherModel.fromJson(Map<String, dynamic> json) {
    return WeatherModel(
      cityName: json['cityName'] as String,
      weatherCondition: json['weatherCondition'] as String,
      temperature: json['temperature'] as double,
      humidity: json['humidity'] as int?,
      windSpeed: json['windSpeed'] as double?,
      windDirection: json['windDirection'] as String?,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'cityName': cityName,
      'weatherCondition': weatherCondition,
      'temperature': temperature,
      'humidity': humidity,
      'windSpeed': windSpeed,
      'windDirection': windDirection,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 获取天气图标
  String getWeatherIcon() {
    switch (weatherCondition) {
      case '晴朗':
        return 'assets/images/weather/sunny.png';
      case '多云':
        return 'assets/images/weather/cloudy.png';
      case '阴天':
        return 'assets/images/weather/overcast.png';
      case '小雨':
        return 'assets/images/weather/light_rain.png';
      case '中雨':
        return 'assets/images/weather/moderate_rain.png';
      case '大雨':
        return 'assets/images/weather/heavy_rain.png';
      case '雷阵雨':
        return 'assets/images/weather/thunderstorm.png';
      case '小雪':
        return 'assets/images/weather/light_snow.png';
      case '中雪':
        return 'assets/images/weather/moderate_snow.png';
      case '大雪':
        return 'assets/images/weather/heavy_snow.png';
      case '雾':
        return 'assets/images/weather/fog.png';
      case '霾':
        return 'assets/images/weather/haze.png';
      case '沙尘暴':
        return 'assets/images/weather/sandstorm.png';
      default:
        return 'assets/images/weather/sunny.png';
    }
  }

  /// 获取天气背景图
  String getWeatherBackground() {
    switch (weatherCondition) {
      case '晴朗':
        return 'assets/images/weather_bg/sunny_bg.jpg';
      case '多云':
        return 'assets/images/weather_bg/cloudy_bg.jpg';
      case '阴天':
        return 'assets/images/weather_bg/overcast_bg.jpg';
      case '小雨':
      case '中雨':
      case '大雨':
      case '雷阵雨':
        return 'assets/images/weather_bg/rain_bg.jpg';
      case '小雪':
      case '中雪':
      case '大雪':
        return 'assets/images/weather_bg/snow_bg.jpg';
      case '雾':
      case '霾':
      case '沙尘暴':
        return 'assets/images/weather_bg/fog_bg.jpg';
      default:
        return 'assets/images/weather_bg/default_bg.jpg';
    }
  }
}
