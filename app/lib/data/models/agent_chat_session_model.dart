import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';

/// Agent聊天会话模型
class AgentChatSessionModel {
  final int id;
  final int userId;
  final int agentId;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AgentConfigModel? agent;
  final List<AgentChatMessageModel>? messages;

  AgentChatSessionModel({
    required this.id,
    required this.userId,
    required this.agentId,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.agent,
    this.messages,
  });

  /// 从JSON创建Agent聊天会话模型
  factory AgentChatSessionModel.fromJson(Map<String, dynamic> json) {
    return AgentChatSessionModel(
      id: json['id'] as int,
      userId: json['userId'] as int,
      agentId: json['agentId'] as int,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      agent: json['agent'] != null
          ? AgentConfigModel.fromJson(json['agent'] as Map<String, dynamic>)
          : null,
      messages: json['messages'] != null
          ? (json['messages'] as List)
              .map((e) => AgentChatMessageModel.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'agentId': agentId,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'agent': agent?.toJson(),
      'messages': messages?.map((e) => e.toJson()).toList(),
    };
  }
}
