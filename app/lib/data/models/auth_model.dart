import 'package:one_moment_app/data/models/media_type.dart';/// 登录请求模型
class LoginRequest {
  final String loginType; // 'phone' 或 'wechat'
  final String? phone;
  final String? password;
  final String? wechatCode;
  final String? wechatUserInfo;

  LoginRequest({
    required this.loginType,
    this.phone,
    this.password,
    this.wechatCode,
    this.wechatUserInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'loginType': loginType,
      if (phone != null) 'phone': phone,
      if (password != null) 'password': password,
      if (wechatCode != null) 'wechatCode': wechatCode,
      if (wechatUserInfo != null) 'wechatUserInfo': wechatUserInfo,
    };
  }
}

/// 注册请求模型
class RegisterRequest {
  final String phone;
  final String password;
  final String? nickname;
  final String? email;
  final String? verificationCode;

  RegisterRequest({
    required this.phone,
    required this.password,
    this.nickname,
    this.email,
    this.verificationCode,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'phone': phone,
      'password': password,
      if (nickname != null) 'nickname': nickname,
      if (email != null) 'email': email,
      if (verificationCode != null) 'verificationCode': verificationCode,
    };
  }
}

/// 用户模型
class UserModel {
  final int id;
  final String username;
  final String? nickname;
  final String? email;
  final String? phone;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final List<String> roles;

  UserModel({
    required this.id,
    required this.username,
    this.nickname,
    this.email,
    this.phone,
    this.avatarUrl,
    required this.createdAt,
    this.lastLoginAt,
    this.roles = const [],
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      username: json['username'] as String,
      nickname: json['nickname'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      roles: (json['roles'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'email': email,
      'phone': phone,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'roles': roles,
    };
  }
}

/// 登录响应模型
class LoginResponse {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final UserModel user;

  LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      tokenType: json['tokenType'] as String,
      expiresIn: json['expiresIn'] as int,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'user': user.toJson(),
    };
  }
}
