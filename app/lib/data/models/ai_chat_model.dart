/// 消息角色枚举
enum MessageRole {
  user,
  assistant,
  system,
}

/// 聊天消息模型
class ChatMessage {
  final String id;
  final MessageRole role;
  final String content;
  final DateTime timestamp;
  
  ChatMessage({
    required this.id,
    required this.role,
    required this.content,
    required this.timestamp,
  });
  
  /// 从JSON创建模型
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      role: MessageRole.values.firstWhere(
        (e) => e.toString() == 'MessageRole.${json['role']}',
        orElse: () => MessageRole.user,
      ),
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'role': role.toString().split('.').last,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// AI模型配置
class AIModelConfig {
  final String modelName;
  final String provider;
  final String apiKey;
  final String? apiEndpoint;
  
  AIModelConfig({
    required this.modelName,
    required this.provider,
    required this.apiKey,
    this.apiEndpoint,
  });
  
  /// 从JSON创建模型
  factory AIModelConfig.fromJson(Map<String, dynamic> json) {
    return AIModelConfig(
      modelName: json['modelName'] as String,
      provider: json['provider'] as String,
      apiKey: json['apiKey'] as String,
      apiEndpoint: json['apiEndpoint'] as String?,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'modelName': modelName,
      'provider': provider,
      'apiKey': apiKey,
      'apiEndpoint': apiEndpoint,
    };
  }
}

/// 聊天会话模型
class ChatSession {
  final String id;
  final String title;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AIModelConfig modelConfig;
  
  ChatSession({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    required this.modelConfig,
  });
  
  /// 从JSON创建模型
  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      title: json['title'] as String,
      messages: (json['messages'] as List)
          .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      modelConfig: AIModelConfig.fromJson(json['modelConfig'] as Map<String, dynamic>),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'modelConfig': modelConfig.toJson(),
    };
  }
  
  /// 添加消息
  ChatSession addMessage(ChatMessage message) {
    final newMessages = List<ChatMessage>.from(messages)..add(message);
    return ChatSession(
      id: id,
      title: title,
      messages: newMessages,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      modelConfig: modelConfig,
    );
  }
}
