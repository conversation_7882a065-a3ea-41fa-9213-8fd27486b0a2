import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/weather_model.dart';
import 'package:one_moment_app/data/services/weather_service.dart';
import 'package:provider/provider.dart';

/// 天气组件
class WeatherWidget extends StatefulWidget {
  const WeatherWidget({super.key});

  @override
  State<WeatherWidget> createState() => _WeatherWidgetState();
}

class _WeatherWidgetState extends State<WeatherWidget> {
  late final WeatherService _weatherService;
  bool _isLoading = true;
  WeatherModel? _weather;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _weatherService = WeatherService(apiClient: apiClient);
    _loadWeather();
  }

  /// 加载天气
  Future<void> _loadWeather() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // 获取最新天气
      final weather = await _weatherService.getLatestWeather();

      setState(() {
        _weather = weather;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载天气失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.25, // 占据屏幕高度的四分之一
      width: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(_weather?.getWeatherBackground() ?? 'assets/images/weather_bg/default_bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(color: Colors.white),
                  ),
                )
              : _buildWeatherContent(),
    );
  }

  /// 构建天气内容
  Widget _buildWeatherContent() {
    if (_weather == null) {
      return const Center(
        child: Text(
          '暂无天气数据',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.black.withOpacity(0.1),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期和城市
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('yyyy年MM月dd日 EEEE', 'zh_CN').format(DateTime.now()),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _weather!.cityName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Spacer(),
          
          // 天气信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 温度和天气状况
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_weather!.temperature.toStringAsFixed(1)}°C',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _weather!.weatherCondition,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                    ),
                  ),
                  if (_weather!.windDirection != null)
                    Text(
                      '${_weather!.windDirection} ${_weather!.windSpeed?.toStringAsFixed(1) ?? ''}m/s',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
              
              // 天气图标
              Image.asset(
                _weather!.getWeatherIcon(),
                width: 80,
                height: 80,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.wb_sunny,
                    color: Colors.white,
                    size: 80,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
