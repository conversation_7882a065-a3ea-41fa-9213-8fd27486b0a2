import 'package:flutter/material.dart';
import 'package:one_moment_app/core/storage/database_service.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/presentation/pages/review_page.dart';
import 'package:intl/intl.dart';
import 'dart:io';

/// WOW时刻卡片
class WowMomentCard extends StatefulWidget {
  const WowMomentCard({super.key});

  @override
  State<WowMomentCard> createState() => _WowMomentCardState();
}

class _WowMomentCardState extends State<WowMomentCard> {
  MomentModel? _currentMoment;
  MomentModel? _lastWeekMoment;
  bool _isLoading = false;
  int _currentIndex = 0;
  
  @override
  void initState() {
    super.initState();
    _loadWowMoments();
  }
  
  /// 加载WOW时刻
  Future<void> _loadWowMoments() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      final dbService = await DatabaseService.instance;
      final allMoments = await dbService.getAllMoments();
      
      if (allMoments.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      
      // 获取今天的日期
      final today = DateTime.now();
      
      // 获取一周前的日期
      final lastWeek = today.subtract(const Duration(days: 7));
      
      // 查找今天的瞬间
      final todayMoments = allMoments.where((moment) {
        return moment.createdAt.year == today.year &&
            moment.createdAt.month == today.month &&
            moment.createdAt.day == today.day;
      }).toList();
      
      // 查找一周前的瞬间
      final lastWeekMoments = allMoments.where((moment) {
        return moment.createdAt.year == lastWeek.year &&
            moment.createdAt.month == lastWeek.month &&
            moment.createdAt.day == lastWeek.day;
      }).toList();
      
      // 如果今天没有瞬间，使用最新的瞬间
      if (todayMoments.isEmpty) {
        allMoments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        if (allMoments.isNotEmpty) {
          todayMoments.add(allMoments.first);
        }
      }
      
      setState(() {
        _currentMoment = todayMoments.isNotEmpty ? todayMoments.first : null;
        _lastWeekMoment = lastWeekMoments.isNotEmpty ? lastWeekMoments.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }
  
  /// 切换到上一个瞬间
  void _previousMoment() {
    setState(() {
      _currentIndex = _currentIndex == 0 ? 1 : 0;
    });
  }
  
  /// 切换到下一个瞬间
  void _nextMoment() {
    setState(() {
      _currentIndex = _currentIndex == 0 ? 1 : 0;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }
    
    if (_currentMoment == null && _lastWeekMoment == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                'WOW时刻',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              const Icon(
                Icons.hourglass_empty,
                size: 48,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              const Text('暂无记录，开始记录美好瞬间吧！'),
            ],
          ),
        ),
      );
    }
    
    final moment = _currentIndex == 0 ? _currentMoment : _lastWeekMoment;
    final dateFormat = DateFormat('yyyy年MM月dd日');
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和导航
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'WOW时刻',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: _previousMoment,
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: _nextMoment,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 日期
            Text(
              moment != null
                  ? dateFormat.format(moment.createdAt)
                  : '无记录',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            // 内容
            if (moment != null) ...[
              Text(
                moment.content,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              
              // 媒体预览
              if (moment.mediaType == MediaType.image && moment.mediaPath != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    File(moment.mediaPath!),
                    height: 150,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              
              // 查看更多按钮
              Center(
                child: TextButton(
                  onPressed: () {
                    // 跳转到回顾页面
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ReviewPage(),
                      ),
                    );
                  },
                  child: const Text('查看更多'),
                ),
              ),
            ] else
              const Text('这一天没有记录'),
          ],
        ),
      ),
    );
  }
}
