import 'dart:io';
import 'package:flutter/material.dart';
import 'package:one_moment_app/core/storage/database_service.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/presentation/pages/review_page.dart';
import 'package:intl/intl.dart';

/// 瞬间列表小部件
class MomentList extends StatefulWidget {
  final int? limit;

  const MomentList({super.key, this.limit});

  @override
  State<MomentList> createState() => _MomentListState();
}

class _MomentListState extends State<MomentList> {
  List<MomentModel> _moments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMoments();
  }

  /// 加载瞬间
  Future<void> _loadMoments() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dbService = await DatabaseService.instance;
      final moments = await dbService.getAllMoments();

      // 按创建时间排序（最新的在前面）
      moments.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // 限制数量
      if (widget.limit != null && moments.length > widget.limit!) {
        moments.length = widget.limit!;
      }

      setState(() {
        _moments = moments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_moments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.hourglass_empty,
              size: 48,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无记录',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            const Text('去记录一些美好瞬间吧'),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _moments.length,
      itemBuilder: (context, index) {
        final moment = _moments[index];
        return _MomentListItem(moment: moment);
      },
    );
  }
}

/// 瞬间列表项
class _MomentListItem extends StatelessWidget {
  final MomentModel moment;

  const _MomentListItem({required this.moment});

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy年MM月dd日 HH:mm');

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // 跳转到回顾页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ReviewPage(),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 时间和心情
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    dateFormat.format(moment.createdAt),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  _buildMoodIcon(moment.mood),
                ],
              ),
              const SizedBox(height: 8),

              // 内容
              Text(
                moment.content,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // 媒体预览
              if (moment.mediaType != MediaType.none && moment.mediaPath != null)
                _buildMediaPreview(moment.mediaType, moment.mediaPath!),

              // 位置和天气
              if (moment.location != null || moment.weather != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    children: [
                      if (moment.location != null)
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(Icons.location_on, size: 16),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  moment.location!,
                                  style: Theme.of(context).textTheme.bodySmall,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (moment.location != null && moment.weather != null)
                        const SizedBox(width: 16),
                      if (moment.weather != null)
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(Icons.cloud, size: 16),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  moment.weather!,
                                  style: Theme.of(context).textTheme.bodySmall,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),

              // 私密标记
              if (moment.isPrivate)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    children: [
                      const Icon(Icons.lock, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '私密',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建心情图标
  Widget _buildMoodIcon(MoodType mood) {
    IconData iconData;
    Color color;

    switch (mood) {
      case MoodType.happy:
        iconData = Icons.sentiment_very_satisfied;
        color = Colors.amber;
        break;
      case MoodType.calm:
        iconData = Icons.sentiment_satisfied;
        color = Colors.green;
        break;
      case MoodType.sad:
        iconData = Icons.sentiment_dissatisfied;
        color = Colors.blue;
        break;
      case MoodType.excited:
        iconData = Icons.mood;
        color = Colors.orange;
        break;
      case MoodType.anxious:
        iconData = Icons.mood_bad;
        color = Colors.purple;
        break;
      case MoodType.neutral:
      default:
        iconData = Icons.sentiment_neutral;
        color = Colors.grey;
        break;
    }

    return Icon(iconData, color: color);
  }

  /// 构建媒体预览
  Widget _buildMediaPreview(MediaType mediaType, String mediaPath) {
    switch (mediaType) {
      case MediaType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(mediaPath),
            height: 150,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        );
      case MediaType.video:
        return Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(
              Icons.play_circle_outline,
              color: Colors.white,
              size: 48,
            ),
          ),
        );
      case MediaType.audio:
        return Container(
          height: 80,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(
              Icons.audiotrack,
              color: Colors.grey,
              size: 48,
            ),
          ),
        );
      case MediaType.none:
      default:
        return const SizedBox.shrink();
    }
  }
}
