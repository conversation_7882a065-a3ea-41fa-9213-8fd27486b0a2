import 'package:flutter/material.dart';
import 'package:one_moment_app/core/storage/database_service.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/presentation/pages/review_page.dart';

/// 瞬间日历小部件
class MomentCalendar extends StatefulWidget {
  const MomentCalendar({super.key});

  @override
  State<MomentCalendar> createState() => _MomentCalendarState();
}

class _MomentCalendarState extends State<MomentCalendar> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  Map<DateTime, List<MomentModel>> _events = {};
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _loadEvents();
  }
  
  /// 加载事件
  Future<void> _loadEvents() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      final dbService = await DatabaseService.instance;
      final moments = await dbService.getAllMoments();
      
      // 按日期分组
      final events = <DateTime, List<MomentModel>>{};
      for (final moment in moments) {
        final date = DateTime(
          moment.createdAt.year,
          moment.createdAt.month,
          moment.createdAt.day,
        );
        
        if (events[date] == null) {
          events[date] = [];
        }
        
        events[date]!.add(moment);
      }
      
      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }
  
  /// 获取指定日期的事件
  List<MomentModel> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // 月份导航
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.chevron_left),
                        onPressed: () {
                          setState(() {
                            _focusedDay = DateTime(
                              _focusedDay.year,
                              _focusedDay.month - 1,
                              _focusedDay.day,
                            );
                          });
                        },
                      ),
                      Text(
                        '${_focusedDay.year}年${_focusedDay.month}月',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      IconButton(
                        icon: const Icon(Icons.chevron_right),
                        onPressed: () {
                          setState(() {
                            _focusedDay = DateTime(
                              _focusedDay.year,
                              _focusedDay.month + 1,
                              _focusedDay.day,
                            );
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // 星期标题
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: const [
                      Text('日'),
                      Text('一'),
                      Text('二'),
                      Text('三'),
                      Text('四'),
                      Text('五'),
                      Text('六'),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // 日历网格
                  _buildCalendarGrid(),
                ],
              ),
      ),
    );
  }
  
  /// 构建日历网格
  Widget _buildCalendarGrid() {
    // 获取当月第一天
    final firstDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    
    // 获取当月天数
    final daysInMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0).day;
    
    // 获取第一天是星期几（0是星期日，1是星期一，以此类推）
    final firstWeekday = firstDayOfMonth.weekday % 7;
    
    // 计算行数
    final rowCount = ((firstWeekday + daysInMonth) / 7).ceil();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: rowCount * 7,
      itemBuilder: (context, index) {
        // 计算日期
        final dayIndex = index - firstWeekday;
        
        // 如果日期小于1或大于当月天数，则显示空白
        if (dayIndex < 0 || dayIndex >= daysInMonth) {
          return const SizedBox.shrink();
        }
        
        final day = dayIndex + 1;
        final date = DateTime(_focusedDay.year, _focusedDay.month, day);
        final events = _getEventsForDay(date);
        final isSelected = _selectedDay != null &&
            _selectedDay!.year == date.year &&
            _selectedDay!.month == date.month &&
            _selectedDay!.day == date.day;
        final isToday = DateTime.now().year == date.year &&
            DateTime.now().month == date.month &&
            DateTime.now().day == date.day;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedDay = date;
            });
            
            // 跳转到回顾页面
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ReviewPage(),
              ),
            );
          },
          child: Container(
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : isToday
                      ? Theme.of(context).colorScheme.primaryContainer
                      : null,
              borderRadius: BorderRadius.circular(8),
              border: events.isNotEmpty
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    day.toString(),
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : isToday
                              ? Theme.of(context).colorScheme.primary
                              : null,
                      fontWeight: events.isNotEmpty ? FontWeight.bold : null,
                    ),
                  ),
                  if (events.isNotEmpty)
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white
                            : Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
