import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'package:flutter/material.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:intl/intl.dart';
import 'package:video_player/video_player.dart';

/// 瞬间详情卡片
class MomentDetailCard extends StatefulWidget {
  final MomentModel moment;

  const MomentDetailCard({super.key, required this.moment});

  @override
  State<MomentDetailCard> createState() => _MomentDetailCardState();
}

class _MomentDetailCardState extends State<MomentDetailCard> {
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoController();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  /// 初始化视频控制器
  Future<void> _initializeVideoController() async {
    if (widget.moment.mediaType == MediaType.video && widget.moment.mediaPath != null) {
      _videoController = VideoPlayerController.file(File(widget.moment.mediaPath!));

      await _videoController!.initialize();

      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
        });
      }
    }
  }

  /// 播放或暂停视频
  void _toggleVideo() {
    if (_videoController != null) {
      if (_videoController!.value.isPlaying) {
        _videoController!.pause();
      } else {
        _videoController!.play();
      }

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy年MM月dd日 HH:mm');

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 时间和心情
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  dateFormat.format(widget.moment.createdAt),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                _buildMoodIcon(widget.moment.mood),
              ],
            ),
            const SizedBox(height: 16),

            // 内容
            Text(widget.moment.content),
            const SizedBox(height: 16),

            // 媒体
            if (widget.moment.mediaType != MediaType.none && widget.moment.mediaPath != null)
              _buildMedia(),

            // 位置和天气
            if (widget.moment.location != null || widget.moment.weather != null)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    if (widget.moment.location != null)
                      Expanded(
                        child: Row(
                          children: [
                            const Icon(Icons.location_on, size: 16),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.moment.location!,
                                style: Theme.of(context).textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (widget.moment.location != null && widget.moment.weather != null)
                      const SizedBox(width: 16),
                    if (widget.moment.weather != null)
                      Expanded(
                        child: Row(
                          children: [
                            const Icon(Icons.cloud, size: 16),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.moment.weather!,
                                style: Theme.of(context).textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

            // 私密标记
            if (widget.moment.isPrivate)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    const Icon(Icons.lock, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '私密',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),

            // 操作按钮
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      // 分享功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('分享功能即将上线')),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      // 编辑功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('编辑功能即将上线')),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      // 删除功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('删除功能即将上线')),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建媒体
  Widget _buildMedia() {
    switch (widget.moment.mediaType) {
      case MediaType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(widget.moment.mediaPath!),
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        );
      case MediaType.video:
        return _isVideoInitialized
            ? AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    VideoPlayer(_videoController!),
                    IconButton(
                      icon: Icon(
                        _videoController!.value.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        size: 48,
                        color: Colors.white,
                      ),
                      onPressed: _toggleVideo,
                    ),
                  ],
                ),
              )
            : Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
      case MediaType.audio:
        return Container(
          height: 80,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () {
                  // 播放音频
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('音频播放功能即将上线')),
                  );
                },
              ),
              const SizedBox(width: 8),
              const Text('点击播放音频'),
            ],
          ),
        );
      case MediaType.none:
      default:
        return const SizedBox.shrink();
    }
  }

  /// 构建心情图标
  Widget _buildMoodIcon(MoodType mood) {
    IconData iconData;
    Color color;
    String label;

    switch (mood) {
      case MoodType.happy:
        iconData = Icons.sentiment_very_satisfied;
        color = Colors.amber;
        label = '开心';
        break;
      case MoodType.calm:
        iconData = Icons.sentiment_satisfied;
        color = Colors.green;
        label = '平静';
        break;
      case MoodType.sad:
        iconData = Icons.sentiment_dissatisfied;
        color = Colors.blue;
        label = '难过';
        break;
      case MoodType.excited:
        iconData = Icons.mood;
        color = Colors.orange;
        label = '兴奋';
        break;
      case MoodType.anxious:
        iconData = Icons.mood_bad;
        color = Colors.purple;
        label = '焦虑';
        break;
      case MoodType.neutral:
      default:
        iconData = Icons.sentiment_neutral;
        color = Colors.grey;
        label = '中性';
        break;
    }

    return Row(
      children: [
        Icon(iconData, color: color),
        const SizedBox(width: 4),
        Text(label),
      ],
    );
  }
}
