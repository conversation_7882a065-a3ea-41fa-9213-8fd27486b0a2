
import 'package:flutter/foundation.dart';
/// 环境类型
enum Environment {
  development,
  testing,
  production,
}
/// 环境配置
class EnvironmentConfig {
  static final EnvironmentConfig _instance = EnvironmentConfig._internal();
  factory EnvironmentConfig() {
    return _instance;
  }
  EnvironmentConfig._internal();
  // 当前环境
  Environment _environment = Environment.development;
  // 是否启用调试模式
  bool _debugMode = kDebugMode;
  // API基础URL
  final Map<Environment, String> _apiBaseUrls = {
    Environment.development: 'http://localhost:8080/api',
    Environment.testing: 'http://************:8080/api',
    Environment.production: 'https://api.onemoment.com/api',
  };
  // 模拟器特定配置 - 根据平台返回不同的URL
  // iOS模拟器使用localhost，Android模拟器使用********（指向宿主机的特殊IP）
  final Map<Environment, Map<String, String>> _platformApiBaseUrls = {
    Environment.development: {
      'ios': 'http://localhost:8080/api',
      'android': 'http://********:8080/api',
      'web': 'http://localhost:8080/api',
    },
    Environment.testing: {
      'ios': 'http://************:8080/api',
      'android': 'http://************:8080/api',
      'web': 'http://************:8080/api',
    Environment.production: {
      'ios': 'https://api.onemoment.com/api',
      'android': 'https://api.onemoment.com/api',
  /// 初始化环境配置
  void initialize({
    Environment environment = Environment.development,
    bool debugMode = kDebugMode,
    Map<Environment, String>? apiBaseUrls,
    Map<Environment, Map<String, String>>? platformApiBaseUrls,
  }) {
    _environment = environment;
    _debugMode = debugMode;
    if (apiBaseUrls != null) {
      _apiBaseUrls.addAll(apiBaseUrls);
    }
    if (platformApiBaseUrls != null) {
      platformApiBaseUrls.forEach((env, urls) {
        if (_platformApiBaseUrls.containsKey(env)) {
          _platformApiBaseUrls[env]!.addAll(urls);
        } else {
          _platformApiBaseUrls[env] = urls;
        }
      });
  /// 获取当前环境
  Environment get environment => _environment;
  /// 获取是否为开发环境
  bool get isDevelopment => _environment == Environment.development;
  /// 获取是否为测试环境
  bool get isTesting => _environment == Environment.testing;
  /// 获取是否为生产环境
  bool get isProduction => _environment == Environment.production;
  /// 获取是否启用调试模式
  bool get isDebugMode => _debugMode;
  /// 设置是否启用调试模式
  set isDebugMode(bool value) {
    _debugMode = value;
  /// 获取当前平台
  String get platform {
    if (kIsWeb) {
      return 'web';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else {
      return 'unknown';
  /// 获取API基础URL
  String get apiBaseUrl {
      return _apiBaseUrls[_environment] ?? '';
      // 根据平台返回不同的URL
      final currentPlatform = platform;
      final platformUrls = _platformApiBaseUrls[_environment];
      if (platformUrls != null && platformUrls.containsKey(currentPlatform)) {
        return platformUrls[currentPlatform]!;
      }
  /// 设置API基础URL
  void setApiBaseUrl(String url, {Environment? environment, String? platformName}) {
    final env = environment ?? _environment;
    final plat = platformName ?? platform;
    // 更新通用URL
    _apiBaseUrls[env] = url;
    // 更新平台特定URL
    if (_platformApiBaseUrls.containsKey(env)) {
      _platformApiBaseUrls[env]![plat] = url;
      _platformApiBaseUrls[env] = {plat: url};
  /// 获取环境名称
  String get environmentName {
    switch (_environment) {
      case Environment.development:
        return 'Development';
      case Environment.testing:
        return 'Testing';
      case Environment.production:
        return 'Production';
  /// 获取环境配置信息
  Map<String, dynamic> getConfig() {
    return {
      'environment': environmentName,
      'debugMode': _debugMode,
      'apiBaseUrl': apiBaseUrl,
    };

import 'package:flutter/services.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// AI配置管理
class AIConfigManager {
  static const String _configKey = 'ai_model_config';
  static const String _defaultConfigPath = 'assets/config/ai_default_config.json';
  final LoggerUtil _logger = LoggerUtil();
  
  /// 获取AI模型配置
  Future<AIModelConfig> getModelConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configKey);
      
      if (configJson != null) {
        _logger.i('从本地存储加载AI配置');
        return AIModelConfig.fromJson(json.decode(configJson));
      }
      // 如果本地没有配置，加载默认配置
      _logger.i('加载默认AI配置');
      return _loadDefaultConfig();
    } catch (e) {
      _logger.e('获取AI配置失败', e);
      // 返回一个基本配置
      return AIModelConfig(
        modelName: 'qwen-max',
        apiKey: '',
        provider: '通义千问',
      );
    }
  }
  /// 保存AI模型配置
  Future<bool> saveModelConfig(AIModelConfig config) async {
      final configJson = json.encode(config.toJson());
      final result = await prefs.setString(_configKey, configJson);
      _logger.i('保存AI配置${result ? '成功' : '失败'}: ${config.provider}');
      return result;
      _logger.e('保存AI配置失败', e);
      return false;
  /// 加载默认配置
  Future<AIModelConfig> _loadDefaultConfig() async {
      final configString = await rootBundle.loadString(_defaultConfigPath);
      final configJson = json.decode(configString);
      return AIModelConfig.fromJson(configJson);
      _logger.e('加载默认AI配置失败', e);
  /// 获取所有支持的AI模型配置
  Future<List<AIModelConfig>> getAllModelConfigs() async {
      final configString = await rootBundle.loadString('assets/config/ai_models.json');
      final List<dynamic> configsJson = json.decode(configString);
      return configsJson
          .map((config) => AIModelConfig.fromJson(config))
          .toList();
      _logger.e('获取所有AI模型配置失败', e);
      // 返回基本配置列表
      return [
        AIModelConfig(
          modelName: 'qwen-max',
          provider: '通义千问',
        ),
          modelName: 'deepseek-chat',
          provider: 'DeepSeek',
          modelName: 'kimi-v1',
          provider: 'Kimi',
          modelName: 'minimax-abab5.5',
          provider: 'MiniMax',
      ];
}

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// 调试拦截器
/// 用于记录网络请求和响应的详细信息
class DebugInterceptor extends Interceptor {
  final LoggerUtil _logger = LoggerUtil();
  final bool _enableDetailedLogs;
  
  DebugInterceptor({bool enableDetailedLogs = kDebugMode}) 
      : _enableDetailedLogs = enableDetailedLogs;
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final method = options.method;
    final url = '${options.baseUrl}${options.path}';
    
    // 记录请求信息
    final requestInfo = {
      'url': url,
      'method': method,
      'headers': options.headers,
      'queryParameters': options.queryParameters,
    };
    // 记录请求体（如果存在）
    if (options.data != null) {
      try {
        if (options.data is FormData) {
          final formData = options.data as FormData;
          requestInfo['data'] = 'FormData: ${formData.fields}';
          if (formData.files.isNotEmpty) {
            requestInfo['files'] = formData.files.map((file) => 
                '${file.key}: ${file.value.filename} (${file.value.contentType})').toList();
          }
        } else if (options.data is Map || options.data is List) {
          requestInfo['data'] = options.data;
        } else if (options.data is String) {
        } else {
          requestInfo['data'] = options.data.toString();
        }
      } catch (e) {
        requestInfo['data'] = 'Error parsing request data: $e';
      }
    }
    _logger.network(method, url, data: _enableDetailedLogs ? requestInfo : null);
    // 添加请求开始时间，用于计算请求耗时
    options.extra['startTime'] = DateTime.now().millisecondsSinceEpoch;
    handler.next(options);
  }
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final method = response.requestOptions.method;
    final url = '${response.requestOptions.baseUrl}${response.requestOptions.path}';
    final statusCode = response.statusCode;
    // 计算请求耗时
    final startTime = response.requestOptions.extra['startTime'] as int?;
    final endTime = DateTime.now().millisecondsSinceEpoch;
    final duration = startTime != null ? endTime - startTime : null;
    // 记录响应信息
    final responseInfo = {
      'statusCode': statusCode,
      'headers': response.headers.map,
      if (duration != null) 'duration': '$duration ms',
    // 记录响应体（如果存在）
    if (response.data != null) {
        if (response.data is Map || response.data is List) {
          responseInfo['data'] = response.data;
        } else if (response.data is String) {
          // 尝试解析JSON字符串
          try {
            responseInfo['data'] = json.decode(response.data as String);
          } catch (e) {
            responseInfo['data'] = response.data;
          responseInfo['data'] = response.data.toString();
        responseInfo['data'] = 'Error parsing response data: $e';
    _logger.network(method, url, response: _enableDetailedLogs ? responseInfo : {
    });
    handler.next(response);
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final method = err.requestOptions.method;
    final url = '${err.requestOptions.baseUrl}${err.requestOptions.path}';
    final startTime = err.requestOptions.extra['startTime'] as int?;
    // 记录错误信息
    final errorInfo = {
      'type': err.type.toString(),
      'message': err.message,
      if (err.response != null) 'statusCode': err.response?.statusCode,
    if (err.response?.data != null) {
        if (err.response!.data is Map || err.response!.data is List) {
          errorInfo['responseData'] = err.response!.data;
        } else if (err.response!.data is String) {
            errorInfo['responseData'] = json.decode(err.response!.data as String);
            errorInfo['responseData'] = err.response!.data;
          errorInfo['responseData'] = err.response!.data.toString();
        errorInfo['responseData'] = 'Error parsing response data: $e';
    _logger.network(method, url, error: errorInfo);
    _logger.e('网络请求错误: $method $url', err, err.stackTrace);
    handler.next(err);
}

import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
/// API服务
class ApiService {
  final Dio _dio;
  
  ApiService({Dio? dio}) : _dio = dio ?? Dio() {
    _dio.options.baseUrl = AppConstants.baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    
    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: kDebugMode,
      responseBody: kDebugMode,
    ));
  }
  /// 设置认证令牌
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  /// 清除认证令牌
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  /// GET请求
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response.data;
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error occurred',
        statusCode: e.response?.statusCode,
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  /// POST请求
  Future<dynamic> post(
    dynamic data,
    ProgressCallback? onSendProgress,
      final response = await _dio.post(
        data: data,
        onSendProgress: onSendProgress,
  /// PUT请求
  Future<dynamic> put(
      final response = await _dio.put(
  /// DELETE请求
  Future<dynamic> delete(
      final response = await _dio.delete(
}

import 'package:http/http.dart' as http;
import 'package:one_moment_app/core/errors/exceptions.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/security_service.dart';
/// AI服务
class AIService {
  final http.Client _client;
  final SecurityService _securityService;
  
  AIService({
    http.Client? client,
    SecurityService? securityService,
  })  : _client = client ?? http.Client(),
        _securityService = securityService ?? SecurityService();
  /// 发送消息到通义千问API
  Future<String> sendMessageToTongyi({
    required List<ChatMessage> messages,
    required String apiKey,
    String? apiEndpoint,
  }) async {
    try {
      // 检查消息内容是否包含敏感词
      for (final message in messages) {
        if (_securityService.containsSensitiveWords(message.content)) {
          final sensitiveWords = _securityService.getSensitiveWords(message.content);
          throw ContentSafetyException(
            message: '消息包含敏感内容',
            violationType: '敏感词: ${sensitiveWords.join(', ')}',
          );
        }
      }
      
      final endpoint = apiEndpoint ?? 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
      // 转换消息格式为通义千问API格式
      final List<Map<String, String>> formattedMessages = messages.map((message) {
        return {
          'role': message.role == MessageRole.user
              ? 'user'
              : message.role == MessageRole.assistant
                  ? 'assistant'
                  : 'system',
          'content': message.content,
        };
      }).toList();
      final response = await _client.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'model': 'qwen-max',
          'input': {
            'messages': formattedMessages,
          },
          'parameters': {
            'temperature': 0.7,
            'top_p': 0.9,
            'result_format': 'text',
        }),
      );
      if (response.statusCode != 200) {
        throw AIModelException(
          message: '通义千问API请求失败: ${response.statusCode}',
          modelName: 'qwen-max',
        );
      final responseData = json.decode(response.body);
      return responseData['output']['text'] as String;
    } catch (e) {
      if (e is ContentSafetyException || e is AIModelException) {
        rethrow;
      throw AIModelException(
        message: '通义千问API请求异常: $e',
        modelName: 'qwen-max',
    }
  }
  /// 发送消息到DeepSeek API
  Future<String> sendMessageToDeepSeek({
      final endpoint = apiEndpoint ?? 'https://api.deepseek.com/v1/chat/completions';
      // 转换消息格式为DeepSeek API格式
          'model': 'deepseek-chat',
          'messages': formattedMessages,
          'temperature': 0.7,
          'top_p': 0.9,
          'max_tokens': 2000,
          message: 'DeepSeek API请求失败: ${response.statusCode}',
          modelName: 'deepseek-chat',
      return responseData['choices'][0]['message']['content'] as String;
        message: 'DeepSeek API请求异常: $e',
        modelName: 'deepseek-chat',
  /// 发送消息到Kimi API
  Future<String> sendMessageToKimi({
      final endpoint = apiEndpoint ?? 'https://api.moonshot.cn/v1/chat/completions';
      // 转换消息格式为Kimi API格式
          'model': 'moonshot-v1-8k',
          message: 'Kimi API请求失败: ${response.statusCode}',
          modelName: 'moonshot-v1-8k',
        message: 'Kimi API请求异常: $e',
        modelName: 'moonshot-v1-8k',
  /// 发送消息到MiniMax API
  Future<String> sendMessageToMiniMax({
      final endpoint = apiEndpoint ?? 'https://api.minimax.chat/v1/text/chatcompletion';
      // 转换消息格式为MiniMax API格式
          'sender_type': message.role == MessageRole.user
              ? 'USER'
                  ? 'BOT'
                  : 'SYSTEM',
          'text': message.content,
          'model': 'abab5.5-chat',
          'tokens_to_generate': 2000,
          message: 'MiniMax API请求失败: ${response.statusCode}',
          modelName: 'abab5.5-chat',
      return responseData['reply'] as String;
        message: 'MiniMax API请求异常: $e',
        modelName: 'abab5.5-chat',
  /// 根据提供商发送消息
  Future<String> sendMessage({
    required AIModelConfig modelConfig,
    switch (modelConfig.provider.toLowerCase()) {
      case 'tongyi':
      case '通义千问':
        return sendMessageToTongyi(
          messages: messages,
          apiKey: modelConfig.apiKey,
          apiEndpoint: modelConfig.apiEndpoint,
      case 'deepseek':
        return sendMessageToDeepSeek(
      case 'kimi':
        return sendMessageToKimi(
      case 'minimax':
        return sendMessageToMiniMax(
      default:
          message: '不支持的AI提供商: ${modelConfig.provider}',
          modelName: modelConfig.modelName,
}

import 'dart:io';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/core/network/debug_interceptor.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// API客户端
class ApiClient {
  static const String _authTokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  final Dio _dio;
  final LocalStorage _localStorage;
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();
  ApiClient({required LocalStorage localStorage})
      : _localStorage = localStorage,
        _dio = Dio(
          BaseOptions(
            baseUrl: EnvironmentConfig().apiBaseUrl,
            connectTimeout: const Duration(seconds: 15),
            receiveTimeout: const Duration(seconds: 60),
            contentType: Headers.jsonContentType,
            responseType: ResponseType.json,
          ),
        ) {
    _logger.i('初始化API客户端，baseUrl: ${_dio.options.baseUrl}');
    // 添加拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: _onRequest,
        onResponse: _onResponse,
        onError: _onError,
      ),
    );
    // 添加调试拦截器
    if (_config.isDebugMode) {
      _dio.interceptors.add(DebugInterceptor(enableDetailedLogs: true));
    }
  }
  /// 请求拦截器
  void _onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 添加通用头部
    options.headers['Accept'] = 'application/json';
    options.headers['Content-Type'] = 'application/json';
    // 打印请求信息
    _logger.d('请求: ${options.method} ${options.path}');
    _logger.d('请求头: ${options.headers}');
    if (options.data != null) {
      _logger.d('请求体: ${options.data}');
    handler.next(options);
  /// 响应拦截器
  void _onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  /// 错误拦截器
  void _onError(DioException error, ErrorInterceptorHandler handler) async {
    _logger.e('API错误: ${error.type}', error, error.stackTrace);
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      _logger.e('网络连接超时: ${error.requestOptions.path}');
    } else if (error.type == DioExceptionType.connectionError) {
      _logger.e('网络连接错误: ${error.requestOptions.path}');
    handler.next(error);
  /// 登录（已废弃，请使用 authenticateUser）
  @Deprecated('请使用 authenticateUser 方法')
  Future<Map<String, dynamic>> login(String username, String password) async {
    final result = await authenticateUser(username, password);
    return {'success': result};
  /// 注册
  Future<Map<String, dynamic>> register(String username, String password, String email, {String? nickname, String? phone}) async {
    try {
      // 创建注册请求
      final request = {
        'username': username,
        'password': password,
        'email': email,
      };
      // 添加可选字段
      if (nickname != null) {
        request['nickname'] = nickname;
      } else {
        request['nickname'] = username;
      }
      if (phone != null) {
        request['phone'] = phone;
      _logger.i('发送注册请求到: /auth/register');
      _logger.d('注册请求数据: $request');
      final response = await _dio.post(
        '/auth/register',
        data: request,
      );
      _logger.d('注册响应: $response');
      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        _logger.d('注册响应数据: $data');
        // 保存令牌
        if (data.containsKey('accessToken')) {
          final token = data['accessToken'] as String;
          await _localStorage.setString(_authTokenKey, token);
          _logger.i('注册成功，保存令牌: ${token.substring(0, min(10, token.length))}...');
          // 保存刷新令牌
          if (data.containsKey('refreshToken')) {
            final refreshToken = data['refreshToken'] as String;
            await _localStorage.setString(_refreshTokenKey, refreshToken);
            _logger.i('保存刷新令牌');
          }
          // 如果有用户信息，也保存起来
          if (data.containsKey('user')) {
            final userJson = jsonEncode(data['user']);
            await _localStorage.setString('user_info', userJson);
            _logger.i('保存用户信息');
        }
        return data;
        throw Exception('注册失败: ${response.statusCode}');
    } catch (e) {
      _logger.e('注册失败', e);
      throw Exception('注册失败: $e');
  /// 登出（已废弃，请使用 signOut）
  @Deprecated('请使用 signOut 方法')
  Future<void> logout() async {
    await signOut();
  /// GET请求 (已废弃，请使用post方法)
  @Deprecated('请使用post方法')
  Future<dynamic> get(String path, {Map<String, dynamic>? queryParameters}) async {
    _logger.w('使用已废弃的get方法，请改用post方法');
    return post(path, data: queryParameters);
  /// POST请求
  Future<dynamic> post(String path, {dynamic data, Map<String, String>? headers}) async {
      final response = await _dio.post(path, data: data, options: headers != null ? Options(headers: headers) : null);
      return response.data;
      throw _handleError(e);
  /// PUT请求 (已废弃，请使用post方法)
  Future<dynamic> put(String path, {dynamic data}) async {
    _logger.w('使用已废弃的put方法，请改用post方法');
    return post(path, data: data);
  /// DELETE请求 (已废弃，请使用post方法)
  Future<dynamic> delete(String path) async {
    _logger.w('使用已废弃的delete方法，请改用post方法');
    return post(path, data: {'id': path.split('/').last});
  /// 上传文件
  Future<String> uploadFile(File file, String type) async {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
        'type': type,
      });
      final response = await _dio.post('/moments/upload', data: formData);
        return response.data as String;
        throw Exception('上传文件失败: ${response.statusCode}');
  /// 处理错误
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.sendTimeout) {
        _logger.e('网络连接超时', error, error.stackTrace);
        return Exception('网络连接超时，请检查网络');
      } else if (error.type == DioExceptionType.connectionError) {
        _logger.e('网络连接错误', error, error.stackTrace);
        return Exception('网络连接错误，请检查网络');
      } else if (error.response != null) {
        final statusCode = error.response!.statusCode;
        final data = error.response!.data;
        _logger.e('API响应错误: $statusCode', error, error.stackTrace);
        if (data is Map<String, dynamic> && data.containsKey('message')) {
          final message = data['message'] as String;
          _logger.e('错误信息: $message');
          return Exception(message);
        } else {
          return Exception('请求失败: $statusCode');
        _logger.e('未知的Dio错误', error, error.stackTrace);
    _logger.e('未知错误', error);
    return Exception('未知错误: $error');
  /// 测试API连接
  Future<bool> testConnection() async {
      _logger.i('测试API连接: ${_dio.options.baseUrl}');
      // 尝试发送一个简单的请求
      final response = await _dio.post('/health/check',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      _logger.i('API连接测试成功: ${response.statusCode}');
      return response.statusCode == 200;
      _logger.e('API连接测试失败', e);
      // 尝试使用不同的路径
      try {
        _logger.i('尝试备用路径: ${_dio.options.baseUrl}/moments/getMomentList');
        final response = await _dio.post('/moments/getMomentList',
          options: Options(
            sendTimeout: const Duration(seconds: 5),
            receiveTimeout: const Duration(seconds: 5),
        );
        _logger.i('备用路径测试成功: ${response.statusCode}');
        return response.statusCode == 200;
      } catch (e2) {
        _logger.e('备用路径测试失败', e2);
        // 尝试不带路径的基础URL
        try {
          final baseUrl = _dio.options.baseUrl;
          final baseUrlWithoutPath = baseUrl.substring(0, baseUrl.lastIndexOf('/api') + 4);
          _logger.i('尝试基础URL: $baseUrlWithoutPath');
          final testDio = Dio(
            BaseOptions(
              baseUrl: baseUrlWithoutPath,
              connectTimeout: const Duration(seconds: 5),
              receiveTimeout: const Duration(seconds: 5),
            ),
          );
          final response = await testDio.post('/health/check');
          _logger.i('基础URL测试成功: ${response.statusCode}');
          return response.statusCode == 200;
        } catch (e3) {
          _logger.e('所有连接测试都失败', e3);
          return false;
  /// 更新API基础URL
  void updateBaseUrl(String baseUrl) {
    _logger.i('更新API基础URL: $baseUrl');
    _dio.options.baseUrl = baseUrl;
  /// 获取当前API基础URL
  String get baseUrl => _dio.options.baseUrl;
  /// 获取Dio实例
  Dio get dio => _dio;
  /// 用户认证
  /// 注意：已移除认证功能，始终返回成功
  Future<bool> authenticateUser(String username, String password) async {
    _logger.i('认证功能已禁用，直接返回成功');
    return true;
  /// 注销
  /// 注意：已移除认证功能，此方法不再执行任何操作
  Future<void> signOut() async {
    _logger.i('注销功能已禁用');
  /// 检查是否已登录
  /// 注意：已移除认证功能，始终返回已登录状态
  bool isLoggedIn() {
    _logger.i('登录检查功能已禁用，始终返回已登录状态');
}

import 'package:logger/logger.dart';
/// 日志工具类
class LoggerUtil {
  static final LoggerUtil _instance = LoggerUtil._internal();
  late Logger _logger;
  // 是否启用详细日志
  bool _verbose = false;
  // 日志缓存，用于在应用内显示
  final List<LogEntry> _logBuffer = [];
  static const int _maxBufferSize = 1000;
  factory LoggerUtil() {
    return _instance;
  }
  LoggerUtil._internal() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.verbose : Level.info,
    );
  /// 设置是否启用详细日志
  void setVerbose(bool verbose) {
    _verbose = verbose;
  /// 获取是否启用详细日志
  bool get isVerbose => _verbose;
  /// 获取日志缓存
  List<LogEntry> get logBuffer => List.unmodifiable(_logBuffer);
  /// 清除日志缓存
  void clearLogBuffer() {
    _logBuffer.clear();
  /// 记录调试日志
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message);
    _addToBuffer('DEBUG', message, error, stackTrace);
  /// 记录信息日志
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message);
    _addToBuffer('INFO', message, error, stackTrace);
  /// 记录警告日志
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message);
    _addToBuffer('WARN', message, error, stackTrace);
  /// 记录错误日志
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message);
    _addToBuffer('ERROR', message, error, stackTrace);
  /// 记录网络请求日志
  void network(String method, String url, {dynamic data, dynamic response, dynamic error}) {
    if (!_verbose && !kDebugMode) return;
    final message = 'NETWORK $method $url';
    final details = {
      if (data != null) 'Request': data,
      if (response != null) 'Response': response,
      if (error != null) 'Error': error,
    };
    _logger.i('$message: ${details.toString()}');
    _addToBuffer('NETWORK', '$method $url', details, null);
  /// 添加日志到缓存
  void _addToBuffer(String level, String message, dynamic error, StackTrace? stackTrace) {
    final timestamp = DateTime.now();
    _logBuffer.add(LogEntry(
      level: level,
      message: message,
      error: error,
      stackTrace: stackTrace,
      timestamp: timestamp,
    ));
    // 限制缓存大小
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }
}
/// 日志条目
class LogEntry {
  final String level;
  final String message;
  final dynamic error;
  final StackTrace? stackTrace;
  final DateTime timestamp;
  LogEntry({
    required this.level,
    required this.message,
    this.error,
    this.stackTrace,
    required this.timestamp,
  });
  @override
  String toString() {
    final timeStr = '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
    return '[$timeStr] $level: $message${error != null ? '\nError: $error' : ''}';

import 'package:one_moment_app/data/models/mood_type.dart';
/// 情绪数字工具类
class MoodNumberUtil {
  /// 根据情绪类型获取数字颜色
  static Color getMoodColor(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return const Color(0xFFFFD700); // 金黄色
      case MoodType.excited:
        return const Color(0xFFFF6B6B); // 红色
      case MoodType.calm:
        return const Color(0xFF4ECDC4); // 青色
      case MoodType.sad:
        return const Color(0xFF95A5A6); // 灰色
      case MoodType.angry:
        return const Color(0xFFE74C3C); // 深红色
      case MoodType.anxious:
        return const Color(0xFF9B59B6); // 紫色
      case MoodType.grateful:
        return const Color(0xFF2ECC71); // 绿色
      case MoodType.neutral:
      default:
        return const Color(0xFF3498DB); // 蓝色
    }
  }
  /// 根据情绪类型获取数字背景渐变
  static LinearGradient getMoodGradient(MoodType mood) {
    final color = getMoodColor(mood);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        color.withOpacity(0.8),
        color,
        color.withOpacity(0.6),
      ],
    );
  /// 根据情绪类型获取数字装饰图标
  static IconData getMoodIcon(MoodType mood) {
        return Icons.sentiment_very_satisfied;
        return Icons.celebration;
        return Icons.spa;
        return Icons.sentiment_dissatisfied;
        return Icons.sentiment_very_dissatisfied;
        return Icons.psychology_alt;
        return Icons.favorite;
        return Icons.sentiment_neutral;
  /// 创建带情绪的数字Widget
  static Widget buildMoodNumber({
    required int number,
    required MoodType mood,
    required bool isSelected,
    required bool isToday,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [Colors.blue.shade400, Colors.blue.shade600],
                )
              : getMoodGradient(mood),
          borderRadius: BorderRadius.circular(12),
          border: isToday
              ? Border.all(color: Colors.orange, width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: getMoodColor(mood).withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 主要数字
            Center(
              child: Text(
                number.toString(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2,
                      offset: const Offset(1, 1),
                    ),
                  ],
                ),
              ),
            // 情绪装饰图标
            Positioned(
              top: 2,
              right: 2,
              child: Icon(
                getMoodIcon(mood),
                size: 12,
                color: Colors.white.withOpacity(0.8),
      ),
  /// 创建无情绪的普通数字Widget
  static Widget buildNormalNumber({
          color: isSelected
              ? Colors.blue
              : isToday
                  ? Colors.blue.withOpacity(0.3)
                  : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        child: Center(
          child: Text(
            number.toString(),
            style: TextStyle(
              color: isSelected
                  ? Colors.white
                  : isToday
                      ? Colors.blue
                      : Colors.black87,
              fontSize: 16,
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
          ),
}

import 'package:weather/weather.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
/// 位置和天气服务
class LocationWeatherService {
  final WeatherFactory _weatherFactory;
  
  LocationWeatherService({String? weatherApiKey})
      : _weatherFactory = WeatherFactory(weatherApiKey ?? 'YOUR_OPENWEATHERMAP_API_KEY');
  /// 获取当前位置
  Future<Position> getCurrentLocation() async {
    try {
      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationServiceDisabledException();
      }
      
      // 检查位置权限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw PermissionException(message: '位置权限被拒绝');
        }
      if (permission == LocationPermission.deniedForever) {
        throw PermissionException(message: '位置权限被永久拒绝，请在设置中启用');
      // 获取位置
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } on LocationServiceDisabledException {
      throw PermissionException(message: '位置服务未启用');
    } catch (e) {
      throw PermissionException(message: '无法获取位置: $e');
    }
  }
  /// 获取地址描述
  Future<String> getAddressFromCoordinates(double latitude, double longitude) async {
      // 这里应该使用地理编码服务，如Google Maps Geocoding API
      // 由于API限制，这里简化为返回坐标
      return '位置: $latitude, $longitude';
      throw NetworkException(message: '无法获取地址: $e');
  /// 获取当前天气
  Future<Weather> getCurrentWeather() async {
      // 获取当前位置
      final position = await getCurrentLocation();
      // 获取天气
      return await _weatherFactory.currentWeatherByLocation(
        position.latitude,
        position.longitude,
      if (e is PermissionException) {
        rethrow;
      throw NetworkException(message: '无法获取天气: $e');
  /// 获取天气描述
  String getWeatherDescription(Weather weather) {
    return '${weather.weatherMain ?? '未知'}, ${weather.temperature?.celsius?.toStringAsFixed(1) ?? '未知'}°C';
  /// 获取位置和天气信息
  Future<Map<String, String>> getLocationAndWeather() async {
      // 获取地址
      final address = await getAddressFromCoordinates(
      final weather = await _weatherFactory.currentWeatherByLocation(
      return {
        'location': address,
        'weather': getWeatherDescription(weather),
      };
      if (e is PermissionException || e is NetworkException) {
      throw NetworkException(message: '无法获取位置和天气: $e');
}

import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:camera/camera.dart';
import 'package:record/record.dart';
import 'package:video_player/video_player.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
/// 媒体服务
class MediaService {
  final ImagePicker _imagePicker;
  final Record _audioRecorder;
  CameraController? _cameraController;
  
  MediaService({
    ImagePicker? imagePicker,
    Record? audioRecorder,
  })  : _imagePicker = imagePicker ?? ImagePicker(),
        _audioRecorder = audioRecorder ?? Record();
  /// 从相册选择图片
  Future<File?> pickImage({
    ImageSource source = ImageSource.gallery,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int imageQuality = 85,
  }) async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );
      
      if (pickedFile == null) return null;
      // 复制到应用目录
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${const Uuid().v4()}${path.extension(pickedFile.path)}';
      final savedImage = File('${appDir.path}/images/$fileName');
      // 确保目录存在
      await Directory('${appDir.path}/images').create(recursive: true);
      // 复制文件
      await File(pickedFile.path).copy(savedImage.path);
      return savedImage;
    } catch (e) {
      throw PermissionException(message: '无法选择图片: $e');
    }
  }
  /// 从相册选择视频
  Future<File?> pickVideo({
    Duration maxDuration = const Duration(seconds: 10),
      final pickedFile = await _imagePicker.pickVideo(
        maxDuration: maxDuration,
      final savedVideo = File('${appDir.path}/videos/$fileName');
      await Directory('${appDir.path}/videos').create(recursive: true);
      await File(pickedFile.path).copy(savedVideo.path);
      return savedVideo;
      throw PermissionException(message: '无法选择视频: $e');
  /// 初始化相机
  Future<void> initializeCamera({
    required CameraDescription camera,
    required ResolutionPreset resolution,
      _cameraController = CameraController(
        camera,
        resolution,
        enableAudio: true,
      await _cameraController!.initialize();
      throw PermissionException(message: '无法初始化相机: $e');
  /// 释放相机资源
  Future<void> disposeCamera() async {
    if (_cameraController != null) {
      await _cameraController!.dispose();
      _cameraController = null;
  /// 拍照
  Future<File?> takePhoto() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      throw PermissionException(message: '相机未初始化');
    
      final XFile photo = await _cameraController!.takePicture();
      final fileName = '${const Uuid().v4()}${path.extension(photo.path)}';
      final savedPhoto = File('${appDir.path}/images/$fileName');
      await File(photo.path).copy(savedPhoto.path);
      return savedPhoto;
      throw PermissionException(message: '无法拍照: $e');
  /// 录制视频
  Future<File?> recordVideo({Duration maxDuration = const Duration(seconds: 10)}) async {
      await _cameraController!.startVideoRecording();
      // 设置最大录制时间
      await Future.delayed(maxDuration);
      final XFile video = await _cameraController!.stopVideoRecording();
      final fileName = '${const Uuid().v4()}${path.extension(video.path)}';
      await File(video.path).copy(savedVideo.path);
      throw PermissionException(message: '无法录制视频: $e');
  /// 开始录音
  Future<void> startAudioRecording() async {
      // 检查麦克风权限
      if (await _audioRecorder.hasPermission()) {
        // 准备录音目录
        final appDir = await getApplicationDocumentsDirectory();
        final audioDir = Directory('${appDir.path}/audios');
        if (!await audioDir.exists()) {
          await audioDir.create(recursive: true);
        }
        
        final fileName = '${const Uuid().v4()}.m4a';
        final filePath = '${audioDir.path}/$fileName';
        // 开始录音
        await _audioRecorder.start(
          path: filePath,
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          samplingRate: 44100,
        );
      } else {
        throw PermissionException(message: '没有麦克风权限');
      }
      throw PermissionException(message: '无法开始录音: $e');
  /// 停止录音
  Future<File?> stopAudioRecording() async {
      final path = await _audioRecorder.stop();
      if (path == null) return null;
      return File(path);
      throw PermissionException(message: '无法停止录音: $e');
  /// 获取可用的相机列表
  Future<List<CameraDescription>> getAvailableCameras() async {
      return await availableCameras();
      throw PermissionException(message: '无法获取相机列表: $e');
  /// 创建视频播放器控制器
  VideoPlayerController createVideoPlayerController(File videoFile) {
    return VideoPlayerController.file(videoFile);
  /// 创建网络视频播放器控制器
  VideoPlayerController createNetworkVideoPlayerController(String url) {
    return VideoPlayerController.networkUrl(Uri.parse(url));
}

import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:one_moment_app/core/errors/exceptions.dart';
/// 安全服务
class SecurityService {
  final FlutterSecureStorage _secureStorage;
  static const String _keyPrefix = 'one_moment_';
  static const String _encryptionKeyName = '${_keyPrefix}encryption_key';
  
  // 敏感词列表
  static const List<String> _sensitiveWords = [
    '自杀',
    '抑郁',
    '焦虑',
    '绝望',
    '死亡',
    // 可以根据需要添加更多敏感词
  ];
  SecurityService({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ?? const FlutterSecureStorage();
  /// 获取或生成加密密钥
  Future<String> _getOrCreateEncryptionKey() async {
    try {
      String? key = await _secureStorage.read(key: _encryptionKeyName);
      
      if (key == null) {
        // 生成新密钥
        final newKey = encrypt.Key.fromSecureRandom(32).base64;
        await _secureStorage.write(key: _encryptionKeyName, value: newKey);
        return newKey;
      }
      return key;
    } catch (e) {
      throw AuthException(message: 'Failed to get or create encryption key: $e');
    }
  }
  /// 加密数据
  Future<String> encryptData(String data) async {
      final key = await _getOrCreateEncryptionKey();
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(key)),
      );
      final iv = encrypt.IV.fromLength(16);
      final encrypted = encrypter.encrypt(data, iv: iv);
      return '${encrypted.base64}:${iv.base64}';
      throw AuthException(message: 'Failed to encrypt data: $e');
  /// 解密数据
  Future<String> decryptData(String encryptedData) async {
      final parts = encryptedData.split(':');
      if (parts.length != 2) {
        throw AuthException(message: 'Invalid encrypted data format');
      final encryptedText = parts[0];
      final ivText = parts[1];
      final iv = encrypt.IV.fromBase64(ivText);
      return encrypter.decrypt64(encryptedText, iv: iv);
      throw AuthException(message: 'Failed to decrypt data: $e');
  /// 安全存储数据
  Future<void> secureWrite(String key, String value) async {
      await _secureStorage.write(key: '$_keyPrefix$key', value: value);
      throw AuthException(message: 'Failed to securely write data: $e');
  /// 安全读取数据
  Future<String?> secureRead(String key) async {
      return await _secureStorage.read(key: '$_keyPrefix$key');
      throw AuthException(message: 'Failed to securely read data: $e');
  /// 安全删除数据
  Future<void> secureDelete(String key) async {
      await _secureStorage.delete(key: '$_keyPrefix$key');
      throw AuthException(message: 'Failed to securely delete data: $e');
  /// 检查内容是否包含敏感词
  bool containsSensitiveWords(String content) {
    final lowerContent = content.toLowerCase();
    
    for (final word in _sensitiveWords) {
      if (lowerContent.contains(word.toLowerCase())) {
        return true;
    return false;
  /// 获取内容中的敏感词
  List<String> getSensitiveWords(String content) {
    final foundWords = <String>[];
        foundWords.add(word);
    return foundWords;
  /// 过滤敏感词（用*替换）
  String filterSensitiveWords(String content) {
    String filteredContent = content;
      final replacement = '*' * word.length;
      filteredContent = filteredContent.replaceAll(word, replacement);
    return filteredContent;
}

/// 日期工具类
class DateUtil {
  /// 格式化日期（年月日）
  static String formatDate(DateTime date) {
    return DateFormat('yyyy年MM月dd日').format(date);
  }
  
  /// 格式化日期（年月日 时分）
  static String formatDateTime(DateTime date) {
    return DateFormat('yyyy年MM月dd日 HH:mm').format(date);
  /// 格式化日期（时分）
  static String formatTime(DateTime date) {
    return DateFormat('HH:mm').format(date);
  /// 格式化日期（星期几）
  static String formatWeekday(DateTime date) {
    final List<String> weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekdays[date.weekday - 1];
  /// 获取相对时间描述（例如：刚刚、5分钟前、1小时前等）
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else {
      return '${(difference.inDays / 365).floor()}年前';
    }
  /// 获取今天的日期（只包含年月日）
  static DateTime today() {
    return DateTime(now.year, now.month, now.day);
  /// 获取昨天的日期
  static DateTime yesterday() {
    final today = DateTime.now();
    return today.subtract(const Duration(days: 1));
  /// 获取明天的日期
  static DateTime tomorrow() {
    return today.add(const Duration(days: 1));
  /// 获取一周前的日期
  static DateTime oneWeekAgo() {
    return today.subtract(const Duration(days: 7));
  /// 获取一个月前的日期
  static DateTime oneMonthAgo() {
    return DateTime(today.year, today.month - 1, today.day);
  /// 获取一年前的日期
  static DateTime oneYearAgo() {
    return DateTime(today.year - 1, today.month, today.day);
  /// 判断两个日期是否是同一天
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  /// 判断日期是否是今天
  static bool isToday(DateTime date) {
    return isSameDay(date, now);
  /// 判断日期是否是昨天
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  /// 获取当前季节
  static String getCurrentSeason() {
    final month = now.month;
    if (month >= 3 && month <= 5) {
      return '春';
    } else if (month >= 6 && month <= 8) {
      return '夏';
    } else if (month >= 9 && month <= 11) {
      return '秋';
      return '冬';
}
/// 字符串工具类
class StringUtil {
  /// 判断字符串是否为空
  static bool isEmpty(String? str) {
    return str == null || str.trim().isEmpty;
  }
  
  /// 判断字符串是否不为空
  static bool isNotEmpty(String? str) {
    return !isEmpty(str);
  /// 截断字符串
  static String truncate(String str, int maxLength, {String suffix = '...'}) {
    if (str.length <= maxLength) {
      return str;
    }
    return '${str.substring(0, maxLength)}$suffix';
  /// 获取字符串的第一个字符
  static String getFirstChar(String str) {
    if (isEmpty(str)) {
      return '';
    return str[0];
  /// 获取字符串的最后一个字符
  static String getLastChar(String str) {
    return str[str.length - 1];
  /// 将字符串首字母大写
  static String capitalize(String str) {
    return str[0].toUpperCase() + str.substring(1);
  /// 将字符串转换为驼峰命名
  static String toCamelCase(String str) {
    
    final words = str.split(RegExp(r'[_\s-]+'));
    final result = StringBuffer(words[0].toLowerCase());
    for (var i = 1; i < words.length; i++) {
      result.write(capitalize(words[i]));
    return result.toString();
  /// 将字符串转换为帕斯卡命名
  static String toPascalCase(String str) {
    final result = StringBuffer();
    for (final word in words) {
      result.write(capitalize(word));
  /// 将字符串转换为下划线命名
  static String toSnakeCase(String str) {
    for (var i = 0; i < str.length; i++) {
      final char = str[i];
      
      if (char == ' ' || char == '-' || char == '_') {
        result.write('_');
      } else if (i > 0 && char.toUpperCase() == char) {
        result.write('_${char.toLowerCase()}');
      } else {
        result.write(char.toLowerCase());
      }
  /// 将字符串转换为短横线命名
  static String toKebabCase(String str) {
        result.write('-');
        result.write('-${char.toLowerCase()}');
  /// 获取字符串的字节长度
  static int getByteLength(String str) {
    return utf8.encode(str).length;
  /// 检查字符串是否是有效的电子邮件地址
  static bool isValidEmail(String email) {
    final emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegExp.hasMatch(email);
  /// 检查字符串是否是有效的手机号码（中国大陆）
  static bool isValidPhoneNumber(String phoneNumber) {
    final phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegExp.hasMatch(phoneNumber);
  /// 检查字符串是否是有效的URL
  static bool isValidUrl(String url) {
    final urlRegExp = RegExp(
      r'^(http|https)://[a-zA-Z0-9]+([\-\.]{1}[a-zA-Z0-9]+)*\.[a-zA-Z]{2,5}(:[0-9]{1,5})?(\/.*)?$',
    return urlRegExp.hasMatch(url);
}

/// UTF-8编码
class utf8 {
  /// 将字符串编码为UTF-8字节
  static List<int> encode(String str) {
    final List<int> bytes = [];
      final codeUnit = str.codeUnitAt(i);
      if (codeUnit < 0x80) {
        // 1字节
        bytes.add(codeUnit);
      } else if (codeUnit < 0x800) {
        // 2字节
        bytes.add(0xC0 | (codeUnit >> 6));
        bytes.add(0x80 | (codeUnit & 0x3F));
      } else if (codeUnit < 0x10000) {
        // 3字节
        bytes.add(0xE0 | (codeUnit >> 12));
        bytes.add(0x80 | ((codeUnit >> 6) & 0x3F));
        // 4字节
        bytes.add(0xF0 | (codeUnit >> 18));
        bytes.add(0x80 | ((codeUnit >> 12) & 0x3F));
    return bytes;
/// 服务器异常
class ServerException implements Exception {
  final String message;
  final int? statusCode;
  
  ServerException({required this.message, this.statusCode});
  @override
  String toString() => 'ServerException: $message (Status Code: $statusCode)';
}

/// 缓存异常
class CacheException implements Exception {
  CacheException({required this.message});
  String toString() => 'CacheException: $message';
/// 网络异常
class NetworkException implements Exception {
  NetworkException({required this.message});
  String toString() => 'NetworkException: $message';
/// 认证异常
class AuthException implements Exception {
  AuthException({required this.message});
  String toString() => 'AuthException: $message';
/// 权限异常
class PermissionException implements Exception {
  PermissionException({required this.message});
  String toString() => 'PermissionException: $message';
/// 内容安全异常
class ContentSafetyException implements Exception {
  final String? violationType;
  ContentSafetyException({required this.message, this.violationType});
  String toString() => 'ContentSafetyException: $message (Type: $violationType)';
/// AI模型异常
class AIModelException implements Exception {
  final String? modelName;
  AIModelException({required this.message, this.modelName});
  String toString() => 'AIModelException: $message (Model: $modelName)';

import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:path_provider/path_provider.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// 图片缓存服务
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();
  final LoggerUtil _logger = LoggerUtil();
  final Map<String, String> _urlCache = {}; // objectKey -> cachedUrl
  final Map<String, DateTime> _cacheTime = {}; // objectKey -> cacheTime
  
  // 缓存有效期：1小时
  static const Duration _cacheExpiry = Duration(hours: 1);
  /// 检查缓存是否有效
  bool _isCacheValid(String objectKey) {
    if (!_urlCache.containsKey(objectKey) || !_cacheTime.containsKey(objectKey)) {
      return false;
    }
    
    final cacheTime = _cacheTime[objectKey]!;
    final now = DateTime.now();
    return now.difference(cacheTime) < _cacheExpiry;
  }
  /// 获取缓存的URL
  String? getCachedUrl(String objectKey) {
    if (_isCacheValid(objectKey)) {
      _logger.d('使用缓存的图片URL: $objectKey');
      return _urlCache[objectKey];
    // 清理过期缓存
    _urlCache.remove(objectKey);
    _cacheTime.remove(objectKey);
    return null;
  /// 缓存URL
  void cacheUrl(String objectKey, String url) {
    _urlCache[objectKey] = url;
    _cacheTime[objectKey] = DateTime.now();
    _logger.d('缓存图片URL: $objectKey -> $url');
  /// 清理所有缓存
  void clearCache() {
    _urlCache.clear();
    _cacheTime.clear();
    _logger.i('清理所有图片URL缓存');
  /// 清理过期缓存
  void cleanExpiredCache() {
    final expiredKeys = <String>[];
    for (final entry in _cacheTime.entries) {
      if (now.difference(entry.value) >= _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    for (final key in expiredKeys) {
      _urlCache.remove(key);
      _cacheTime.remove(key);
    if (expiredKeys.isNotEmpty) {
      _logger.i('清理${expiredKeys.length}个过期的图片URL缓存');
  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    cleanExpiredCache(); // 先清理过期缓存
    return {
      'totalCached': _urlCache.length,
      'cacheKeys': _urlCache.keys.toList(),
    };
}

import 'package:go_router/go_router.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
import 'package:one_moment_app/presentation/pages/agent_list_page.dart';
import 'package:one_moment_app/presentation/pages/home_page.dart';
import 'package:one_moment_app/presentation/pages/moment_create_page.dart';
import 'package:one_moment_app/presentation/pages/moment_edit_page.dart';
import 'package:one_moment_app/presentation/pages/time_capsule_calendar_page.dart';
/// 应用路由配置
class AppRouter {
  /// 创建路由器
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    routes: [
      // 首页
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),
      // 时间胶囊日历页面
        path: '/time-capsule',
        name: 'time-capsule',
        builder: (context, state) => const TimeCapsuleCalendarPage(),
      // 创建瞬间页面
        path: '/moment-create',
        name: 'moment-create',
        builder: (context, state) => const MomentCreatePage(),
      // 编辑瞬间页面
        path: '/moment-edit',
        name: 'moment-edit',
        builder: (context, state) {
          final moment = state.extra as MomentModel;
          return MomentEditPage(moment: moment);
        },
      // AI助手列表页面
        path: '/agents',
        name: 'agents',
        builder: (context, state) => const AgentListPage(),
      // AI助手聊天页面
        path: '/agent-chat',
        name: 'agent-chat',
          final Map<String, dynamic> params = state.extra as Map<String, dynamic>? ?? {};
          final int? sessionId = params['sessionId'];
          final int? agentId = params['agentId'];
          return AgentChatPage(
            sessionId: sessionId,
            agentId: agentId,
          );
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      body: Center(
        child: Text('无法找到路径: ${state.uri.path}'),
    ),
  );
}

/// 应用主题配置
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();
  
  // 春季主题
  static ThemeData springTheme() {
    return ThemeData(
      primaryColor: const Color(0xFFF8BBD0), // 浅粉色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFF8BBD0),
        brightness: Brightness.light,
      ),
      fontFamily: 'PingFang',
      useMaterial3: true,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFF8BBD0),
        foregroundColor: Colors.black87,
        elevation: 0,
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF8BBD0),
          foregroundColor: Colors.black87,
        ),
    );
  }
  // 夏季主题
  static ThemeData summerTheme() {
      primaryColor: const Color(0xFF81D4FA), // 浅蓝色
        seedColor: const Color(0xFF81D4FA),
        backgroundColor: Color(0xFF81D4FA),
          backgroundColor: const Color(0xFF81D4FA),
  // 秋季主题
  static ThemeData autumnTheme() {
      primaryColor: const Color(0xFFFFCC80), // 浅橙色
        seedColor: const Color(0xFFFFCC80),
        backgroundColor: Color(0xFFFFCC80),
          backgroundColor: const Color(0xFFFFCC80),
  // 冬季主题
  static ThemeData winterTheme() {
      primaryColor: const Color(0xFFB3E5FC), // 冷蓝色
        seedColor: const Color(0xFFB3E5FC),
        backgroundColor: Color(0xFFB3E5FC),
          backgroundColor: const Color(0xFFB3E5FC),
  /// 根据当前月份获取季节主题
  static ThemeData getSeasonTheme() {
    final month = DateTime.now().month;
    
    // 3-5月为春季，6-8月为夏季，9-11月为秋季，12-2月为冬季
    if (month >= 3 && month <= 5) {
      return springTheme();
    } else if (month >= 6 && month <= 8) {
      return summerTheme();
    } else if (month >= 9 && month <= 11) {
      return autumnTheme();
    } else {
      return winterTheme();
    }
}

import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/router/app_router.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/data/services/auth_service.dart';
import 'package:one_moment_app/data/services/oss_service.dart';
import 'package:one_moment_app/data/services/tongyi_service.dart';
import 'package:one_moment_app/presentation/pages/home_page.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';
import 'package:one_moment_app/presentation/providers/auth_provider.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  // 设置应用方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  // 初始化本地存储
  final prefs = await SharedPreferences.getInstance();
  // 创建本地存储
  final localStorage = LocalStorage(prefs);
  // 创建API客户端
  final apiClient = ApiClient(localStorage: localStorage);
  // 创建OSS服务
  final ossService = OssService(apiClient: apiClient);
  // 创建通义千问服务
  final tongyiService = TongyiService(apiClient: apiClient);
  // 创建认证服务
  final authService = AuthService(apiClient: apiClient, localStorage: localStorage);
  // 运行应用
  runApp(
    MultiProvider(
      providers: [
        Provider<ApiClient>.value(value: apiClient),
        Provider<LocalStorage>.value(value: localStorage),
        Provider<OssService>.value(value: ossService),
        Provider<TongyiService>.value(value: tongyiService),
        Provider<AuthService>.value(value: authService),
        ChangeNotifierProvider(create: (_) => AuthProvider(authService: authService)),
        ChangeNotifierProvider(create: (_) => WeatherProvider(apiClient: apiClient)),
        ChangeNotifierProvider(create: (_) => AgentProvider(apiClient: apiClient, tongyiService: tongyiService)),
        ChangeNotifierProvider(create: (_) => MomentProvider(apiClient: apiClient, ossService: ossService)),
      ],
      child: const OneMomentApp(),
    ),
  );
}
class OneMomentApp extends StatefulWidget {
  const OneMomentApp({super.key});
  @override
  State<OneMomentApp> createState() => _OneMomentAppState();
class _OneMomentAppState extends State<OneMomentApp> {
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'One Moment',
      debugShowCheckedModeBanner: false,
      routerConfig: AppRouter.router,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF64B5F6), // 青春活力蓝
          primary: const Color(0xFF64B5F6),
          secondary: const Color(0xFFFFB74D), // 温暖橙色
          tertiary: const Color(0xFF81C784), // 清新绿色
          background: const Color(0xFFF5F5F5),
          surface: Colors.white,
        ),
        useMaterial3: true,
        fontFamily: 'Roboto',
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontWeight: FontWeight.bold),
          displayMedium: TextStyle(fontWeight: FontWeight.bold),
          displaySmall: TextStyle(fontWeight: FontWeight.bold),
          headlineLarge: TextStyle(fontWeight: FontWeight.bold),
          headlineMedium: TextStyle(fontWeight: FontWeight.bold),
          headlineSmall: TextStyle(fontWeight: FontWeight.w600),
        appBarTheme: const AppBarTheme(
          elevation: 0,
          centerTitle: true,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      supportedLocales: const [
        Locale('zh', 'CN'),
        Locale('en', 'US'),
      builder: (context, child) {
        return MediaQuery(
          // 设置文字缩放比例为1.0，防止系统字体大小影响布局
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: child!,
        );
      },
    );
  }
/// 消息角色枚举
enum MessageRole {
  /// 用户
  user,
  
  /// 助手
  assistant,
  /// 系统
  system,
}

/// 将字符串转换为消息角色
MessageRole messageRoleFromString(String role) {
  switch (role.toLowerCase()) {
    case 'user':
      return MessageRole.user;
    case 'assistant':
      return MessageRole.assistant;
    case 'system':
      return MessageRole.system;
    default:
  }
/// 将消息角色转换为字符串
String messageRoleToString(MessageRole role) {
  switch (role) {
    case MessageRole.user:
      return 'user';
    case MessageRole.assistant:
      return 'assistant';
    case MessageRole.system:
      return 'system';
/// Agent聊天消息模型
class AgentChatMessageModel {
  final int? id;
  final int sessionId;
  final MessageRole role;
  final String content;
  final DateTime? createdAt;
  AgentChatMessageModel({
    this.id,
    required this.sessionId,
    required this.role,
    required this.content,
    this.createdAt,
  });
  /// 从JSON创建Agent聊天消息模型
  factory AgentChatMessageModel.fromJson(Map<String, dynamic> json) {
    return AgentChatMessageModel(
      id: json['id'] as int?,
      sessionId: json['sessionId'] as int,
      role: messageRoleFromString(json['role'] as String),
      content: json['content'] as String,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
    );
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sessionId': sessionId,
      'role': messageRoleToString(role),
      'content': content,
      'createdAt': createdAt?.toIso8601String(),
    };
/// Agent配置模型
class AgentConfigModel {
  final int id;
  final String name;
  final String? avatar;
  final String? description;
  final String prompt;
  final String modelName;
  final DateTime createdAt;
  final DateTime updatedAt;

  AgentConfigModel({
    required this.id,
    required this.name,
    this.avatar,
    this.description,
    required this.prompt,
    this.modelName = 'qwen-turbo',
    required this.createdAt,
    required this.updatedAt,
  });
  /// 从JSON创建Agent配置模型
  factory AgentConfigModel.fromJson(Map<String, dynamic> json) {
    return AgentConfigModel(
      id: json['id'] as int,
      name: json['name'] as String,
      avatar: json['avatar'] as String?,
      description: json['description'] as String?,
      prompt: json['prompt'] as String,
      modelName: json['modelName'] as String? ?? 'qwen-turbo',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'description': description,
      'prompt': prompt,
      'modelName': modelName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
}
/// 社区帖子模型
class CommunityPostModel {
  /// 帖子ID
  final String id;
  
  /// 用户ID
  final String userId;
  /// 用户名
  final String userName;
  /// 用户头像
  final String userAvatar;
  /// 帖子内容
  final String content;
  /// 帖子图片
  final List<String>? images;
  /// 点赞数
  final int likeCount;
  /// 评论数
  final int commentCount;
  /// 创建时间
  final DateTime createdAt;
  /// 是否已点赞
  final bool isLiked;

  /// 构造函数
  CommunityPostModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    this.images,
    required this.likeCount,
    required this.commentCount,
    required this.createdAt,
    required this.isLiked,
  });
  /// 从JSON创建社区帖子模型
  factory CommunityPostModel.fromJson(Map<String, dynamic> json) {
    return CommunityPostModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatar: json['userAvatar'] as String,
      content: json['content'] as String,
      images: json['images'] != null
          ? List<String>.from(json['images'] as List)
          : null,
      likeCount: json['likeCount'] as int,
      commentCount: json['commentCount'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isLiked: json['isLiked'] as bool,
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'content': content,
      'images': images,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'createdAt': createdAt.toIso8601String(),
      'isLiked': isLiked,
    };
  /// 复制并修改
  CommunityPostModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userAvatar,
    String? content,
    List<String>? images,
    int? likeCount,
    int? commentCount,
    DateTime? createdAt,
    bool? isLiked,
  }) {
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      content: content ?? this.content,
      images: images ?? this.images,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      createdAt: createdAt ?? this.createdAt,
      isLiked: isLiked ?? this.isLiked,
}

import 'package:one_moment_app/data/models/media_type.dart';import 'package:one_moment_app/data/models/mood_type.dart';
/// 瞬间模型
class MomentModel {
  final String id;
  final String userId;
  final String username;
  final String contentType;
  final String textContent;
  final DateTime createdAt;
  final String? locationName;
  final String? weather;
  final double? temperature;
  final double? latitude;
  final double? longitude;
  final String? mediaUrl;
  final String? mediaObjectKey;
  final String? thumbnailUrl;
  final MoodType mood;
  final bool isPrivate;
  MomentModel({
    required this.id,
    required this.userId,
    required this.username,
    required this.contentType,
    required this.textContent,
    required this.createdAt,
    this.locationName,
    this.weather,
    this.temperature,
    this.latitude,
    this.longitude,
    this.mediaUrl,
    this.mediaObjectKey,
    this.thumbnailUrl,
    this.mood = MoodType.neutral,
    this.isPrivate = false,
  });
  // 兼容旧代码的getter
  String get content => textContent;
  String? get location => locationName;
  MediaType get mediaType =>
      contentType == 'image' ? MediaType.image :
      contentType == 'video' ? MediaType.video :
      contentType == 'audio' ? MediaType.audio :
      MediaType.none;
  String? get mediaPath => mediaUrl;
  /// 获取媒体文件的完整路径
  String? get mediaProxyUrl {
    if (mediaObjectKey == null || mediaObjectKey!.isEmpty) {
      return null;
    }
    // 如果mediaObjectKey包含路径，只取文件名部分
    String fileName = mediaObjectKey!;
    if (fileName.contains('/')) {
      fileName = fileName.split('/').last;
    return 'http://localhost:8080/api/moments/media/${Uri.encodeComponent(fileName)}';
  }
  /// 从JSON创建模型
  factory MomentModel.fromJson(Map<String, dynamic> json) {
    return MomentModel(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      contentType: json['contentType']?.toString() ?? 'none',
      textContent: json['textContent']?.toString() ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is String
              ? DateTime.parse(json['createdAt'])
              : DateTime.now())
          : DateTime.now(),
      locationName: json['locationName'] as String?,
      weather: json['weather'] as String?,
      temperature: json['temperature'] != null ? double.tryParse(json['temperature'].toString()) : null,
      latitude: json['latitude'] != null ? double.tryParse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.tryParse(json['longitude'].toString()) : null,
      mediaUrl: json['mediaUrl'] as String?,
      mediaObjectKey: json['mediaObjectKey'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      mood: json['mood'] != null
          ? MoodType.values.firstWhere(
              (e) => e.toString().split('.').last.toLowerCase() == json['mood'].toString().toLowerCase(),
              orElse: () => MoodType.neutral,
            )
          : MoodType.neutral,
      isPrivate: json['isPrivate'] as bool? ?? false,
    );
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'username': username,
      'contentType': contentType,
      'textContent': textContent,
      'createdAt': createdAt.toIso8601String(),
      'locationName': locationName,
      'weather': weather,
      'temperature': temperature,
      'latitude': latitude,
      'longitude': longitude,
      'mediaUrl': mediaUrl,
      'mediaObjectKey': mediaObjectKey,
      'thumbnailUrl': thumbnailUrl,
      'mood': mood.toString().split('.').last,
      'isPrivate': isPrivate,
    };
  /// 复制并修改模型
  MomentModel copyWith({
    String? id,
    String? userId,
    String? username,
    String? contentType,
    String? textContent,
    DateTime? createdAt,
    String? locationName,
    String? weather,
    double? temperature,
    double? latitude,
    double? longitude,
    String? mediaUrl,
    String? mediaObjectKey,
    String? thumbnailUrl,
    MoodType? mood,
    bool? isPrivate,
  }) {
      id: id ?? this.id,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      contentType: contentType ?? this.contentType,
      textContent: textContent ?? this.textContent,
      createdAt: createdAt ?? this.createdAt,
      locationName: locationName ?? this.locationName,
      weather: weather ?? this.weather,
      temperature: temperature ?? this.temperature,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaObjectKey: mediaObjectKey ?? this.mediaObjectKey,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      mood: mood ?? this.mood,
      isPrivate: isPrivate ?? this.isPrivate,
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MomentModel &&
        other.id == id &&
        other.userId == userId &&
        other.username == username &&
        other.contentType == contentType &&
        other.textContent == textContent &&
        other.createdAt == createdAt &&
        other.locationName == locationName &&
        other.weather == weather &&
        other.temperature == temperature &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.mediaUrl == mediaUrl &&
        other.mediaObjectKey == mediaObjectKey &&
        other.thumbnailUrl == thumbnailUrl &&
        other.mood == mood &&
        other.isPrivate == isPrivate;
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        username.hashCode ^
        contentType.hashCode ^
        textContent.hashCode ^
        createdAt.hashCode ^
        locationName.hashCode ^
        weather.hashCode ^
        temperature.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        mediaUrl.hashCode ^
        mediaObjectKey.hashCode ^
        thumbnailUrl.hashCode ^
        mood.hashCode ^
        isPrivate.hashCode;
}
/// 心情类型
enum MoodType {
  happy,    // 开心
  calm,     // 平静
  sad,      // 难过
  excited,  // 兴奋
  anxious,  // 焦虑
  neutral,  // 中性
  angry,    // 愤怒
  grateful, // 感激
}
/// AI配置模型
class AIConfigModel {
  final String id;
  final String name;
  final String provider;
  final String description;
  final List<String> features;

  AIConfigModel({
    required this.id,
    required this.name,
    required this.provider,
    required this.description,
    required this.features,
  });
  /// 从JSON创建AI配置模型
  factory AIConfigModel.fromJson(Map<String, dynamic> json) {
    return AIConfigModel(
      id: json['id'].toString(),
      name: json['name'] as String,
      provider: json['provider'] as String,
      description: json['description'] as String? ?? '',
      features: (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'provider': provider,
      'description': description,
      'features': features,
    };
}
/// 天气模型
class WeatherModel {
  final String cityName;
  final String weatherCondition;
  final double temperature;
  final int? humidity;
  final double? windSpeed;
  final String? windDirection;
  final DateTime updatedAt;

  WeatherModel({
    required this.cityName,
    required this.weatherCondition,
    required this.temperature,
    this.humidity,
    this.windSpeed,
    this.windDirection,
    required this.updatedAt,
  });
  /// 从JSON创建天气模型
  factory WeatherModel.fromJson(Map<String, dynamic> json) {
    return WeatherModel(
      cityName: json['cityName'] as String,
      weatherCondition: json['weatherCondition'] as String,
      temperature: json['temperature'] as double,
      humidity: json['humidity'] as int?,
      windSpeed: json['windSpeed'] as double?,
      windDirection: json['windDirection'] as String?,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'cityName': cityName,
      'weatherCondition': weatherCondition,
      'temperature': temperature,
      'humidity': humidity,
      'windSpeed': windSpeed,
      'windDirection': windDirection,
      'updatedAt': updatedAt.toIso8601String(),
    };
  /// 获取天气图标
  String getWeatherIcon() {
    switch (weatherCondition) {
      case '晴朗':
        return 'assets/images/weather/sunny.png';
      case '多云':
        return 'assets/images/weather/cloudy.png';
      case '阴天':
        return 'assets/images/weather/overcast.png';
      case '小雨':
        return 'assets/images/weather/light_rain.png';
      case '中雨':
        return 'assets/images/weather/moderate_rain.png';
      case '大雨':
        return 'assets/images/weather/heavy_rain.png';
      case '雷阵雨':
        return 'assets/images/weather/thunderstorm.png';
      case '小雪':
        return 'assets/images/weather/light_snow.png';
      case '中雪':
        return 'assets/images/weather/moderate_snow.png';
      case '大雪':
        return 'assets/images/weather/heavy_snow.png';
      case '雾':
        return 'assets/images/weather/fog.png';
      case '霾':
        return 'assets/images/weather/haze.png';
      case '沙尘暴':
        return 'assets/images/weather/sandstorm.png';
      default:
    }
  /// 获取天气背景图
  String getWeatherBackground() {
        return 'assets/images/weather_bg/sunny_bg.jpg';
        return 'assets/images/weather_bg/cloudy_bg.jpg';
        return 'assets/images/weather_bg/overcast_bg.jpg';
        return 'assets/images/weather_bg/rain_bg.jpg';
        return 'assets/images/weather_bg/snow_bg.jpg';
        return 'assets/images/weather_bg/fog_bg.jpg';
        return 'assets/images/weather_bg/default_bg.jpg';
}

import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
/// Agent聊天会话模型
class AgentChatSessionModel {
  final int id;
  final int userId;
  final int agentId;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AgentConfigModel? agent;
  final List<AgentChatMessageModel>? messages;
  AgentChatSessionModel({
    required this.id,
    required this.userId,
    required this.agentId,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.agent,
    this.messages,
  });
  /// 从JSON创建Agent聊天会话模型
  factory AgentChatSessionModel.fromJson(Map<String, dynamic> json) {
    return AgentChatSessionModel(
      id: json['id'] as int,
      userId: json['userId'] as int,
      agentId: json['agentId'] as int,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      agent: json['agent'] != null
          ? AgentConfigModel.fromJson(json['agent'] as Map<String, dynamic>)
          : null,
      messages: json['messages'] != null
          ? (json['messages'] as List)
              .map((e) => AgentChatMessageModel.fromJson(e as Map<String, dynamic>))
              .toList()
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'agentId': agentId,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'agent': agent?.toJson(),
      'messages': messages?.map((e) => e.toJson()).toList(),
    };
}
/// 媒体类型
enum MediaType {
  none,   // 无媒体
  image,  // 图片
  video,  // 视频
  audio,  // 音频
}
/// 登录请求模型
class LoginRequest {
  final String loginType; // 'phone' 或 'wechat'
  final String? phone;
  final String? password;
  final String? wechatCode;
  final String? wechatUserInfo;

  LoginRequest({
    required this.loginType,
    this.phone,
    this.password,
    this.wechatCode,
    this.wechatUserInfo,
  });
  Map<String, dynamic> toJson() {
    return {
      'loginType': loginType,
      if (phone != null) 'phone': phone,
      if (password != null) 'password': password,
      if (wechatCode != null) 'wechatCode': wechatCode,
      if (wechatUserInfo != null) 'wechatUserInfo': wechatUserInfo,
    };
  }
}
/// 注册请求模型
class RegisterRequest {
  final String phone;
  final String password;
  final String? nickname;
  final String? email;
  final String? verificationCode;
  RegisterRequest({
    required this.phone,
    required this.password,
    this.nickname,
    this.email,
    this.verificationCode,
      'phone': phone,
      'password': password,
      if (nickname != null) 'nickname': nickname,
      if (email != null) 'email': email,
      if (verificationCode != null) 'verificationCode': verificationCode,
/// 用户模型
class UserModel {
  final int id;
  final String username;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final List<String> roles;
  UserModel({
    required this.id,
    required this.username,
    this.avatarUrl,
    required this.createdAt,
    this.lastLoginAt,
    this.roles = const [],
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      username: json['username'] as String,
      nickname: json['nickname'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      roles: (json['roles'] as List<dynamic>?)?.cast<String>() ?? [],
    );
      'id': id,
      'username': username,
      'nickname': nickname,
      'email': email,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'roles': roles,
/// 登录响应模型
class LoginResponse {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final UserModel user;
  LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.user,
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      tokenType: json['tokenType'] as String,
      expiresIn: json['expiresIn'] as int,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'user': user.toJson(),
/// 消息角色枚举
enum MessageRole {
  user,
  assistant,
  system,
}

/// 聊天消息模型
class ChatMessage {
  final String id;
  final MessageRole role;
  final String content;
  final DateTime timestamp;
  
  ChatMessage({
    required this.id,
    required this.role,
    required this.content,
    required this.timestamp,
  });
  /// 从JSON创建模型
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      role: MessageRole.values.firstWhere(
        (e) => e.toString() == 'MessageRole.${json['role']}',
        orElse: () => MessageRole.user,
      ),
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'role': role.toString().split('.').last,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
    };
/// AI模型配置
class AIModelConfig {
  final String modelName;
  final String provider;
  final String apiKey;
  final String? apiEndpoint;
  AIModelConfig({
    required this.modelName,
    required this.provider,
    required this.apiKey,
    this.apiEndpoint,
  factory AIModelConfig.fromJson(Map<String, dynamic> json) {
    return AIModelConfig(
      modelName: json['modelName'] as String,
      provider: json['provider'] as String,
      apiKey: json['apiKey'] as String,
      apiEndpoint: json['apiEndpoint'] as String?,
      'modelName': modelName,
      'provider': provider,
      'apiKey': apiKey,
      'apiEndpoint': apiEndpoint,
/// 聊天会话模型
class ChatSession {
  final String title;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AIModelConfig modelConfig;
  ChatSession({
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    required this.modelConfig,
  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      title: json['title'] as String,
      messages: (json['messages'] as List)
          .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      modelConfig: AIModelConfig.fromJson(json['modelConfig'] as Map<String, dynamic>),
      'title': title,
      'messages': messages.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'modelConfig': modelConfig.toJson(),
  /// 添加消息
  ChatSession addMessage(ChatMessage message) {
    final newMessages = List<ChatMessage>.from(messages)..add(message);
      id: id,
      title: title,
      messages: newMessages,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      modelConfig: modelConfig,

import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
/// 通义千问服务
/// 通过后端API调用通义千问服务
class TongyiService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  /// 构造函数
  TongyiService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化通义千问服务');
  }
  /// 发送聊天消息
  /// 通过后端API调用通义千问服务
  Future<String> sendMessage({
    required List<AgentChatMessageModel> messages,
    required String modelName,
  }) async {
    try {
      _logger.i('发送消息到通义千问: 消息数量=${messages.length}, 模型=$modelName');
      // 构建请求参数
      final requestBody = {
        'model': modelName,
        'messages': messages.map((message) => {
          'role': message.role.toString().split('.').last.toLowerCase(),
          'content': message.content,
        }).toList(),
      };
      _logger.d('发送请求到后端AI服务: $requestBody');
      // 通过后端API发送请求
      final response = await _apiClient.post(
        '/ai/chat',
        data: {
          'message': messages.last.content,
          'model': modelName,
        },
      );
      _logger.d('收到后端AI服务响应: $response');
      // 解析响应
      if (response is Map<String, dynamic>) {
        _logger.d('解析AI响应: $response');
        // 检查是否有数据字段
        if (response.containsKey('data') && response['data'] is Map<String, dynamic>) {
          final data = response['data'] as Map<String, dynamic>;
          if (data.containsKey('message') && data['message'] != null) {
            _logger.d('从data.message获取响应: ${data['message']}');
            return data['message'] as String;
          }
        }
        // 检查是否直接包含消息字段
        if (response.containsKey('message') && response['message'] != null) {
          _logger.d('从message字段获取响应: ${response['message']}');
          return response['message'] as String;
        // 检查是否有错误信息
        if (response.containsKey('error') && response['error'] != null) {
          _logger.e('服务器返回错误: ${response['error']}');
          return '抱歉，服务器返回了一个错误: ${response['error']}';
        // 尝试将整个响应转换为字符串
        _logger.d('尝试使用整个响应作为消息');
        return response.toString();
      }
      _logger.e('解析AI响应失败: $response');
      return '抱歉，我无法理解服务器的响应。';
    } catch (e) {
      _logger.e('发送消息到通义千问失败: $e');
      // 返回错误消息
      return '抱歉，我遇到了一些问题，无法回应您的消息。错误信息: $e';
    }
  /// 分析内容
  Future<Map<String, dynamic>> analyzeContent(String content) async {
      _logger.i('分析内容: ${content.length}字符');
        '/ai/analyzeMoment',
          'content': content,
      _logger.d('收到内容分析响应: $response');
      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response['data'] is Map<String, dynamic>) {
        return response['data'] as Map<String, dynamic>;
      _logger.e('解析内容分析响应失败: $response');
      return {'error': '无法解析服务器响应'};
      _logger.e('内容分析失败: $e');
      return {'error': '内容分析失败: $e'};
  /// 获取当前AI模型信息
  Future<Map<String, dynamic>> getCurrentModel() async {
      _logger.i('获取当前AI模型信息');
      final response = await _apiClient.post('/ai/getCurrentModel');
      _logger.d('收到当前AI模型信息响应: $response');
      _logger.e('解析当前AI模型信息响应失败: $response');
      return {'name': '通义千问', 'provider': '阿里云'};
      _logger.e('获取当前AI模型信息失败: $e');
  /// 获取支持的模型列表
  Future<List<Map<String, dynamic>>> getAvailableModels() async {
      _logger.i('获取可用AI模型列表');
      final response = await _apiClient.post('/ai/getAvailableModels');
      _logger.d('收到可用AI模型列表响应: $response');
          response['data'] is List) {
        return (response['data'] as List).cast<Map<String, dynamic>>();
      _logger.e('解析可用AI模型列表响应失败: $response');
      return [{'name': '通义千问', 'provider': '阿里云'}];
      _logger.e('获取可用AI模型列表失败: $e');
  /// 与AI聊天
  Future<String> chat(int userId, String message) async {
      _logger.i('发送聊天消息: userId=$userId, message=${message.length}字符');
          'userId': userId,
          'message': message,
      _logger.d('收到聊天响应: $response');
        if (response.containsKey('data') && response['data'] != null) {
          return response['data'].toString();
      if (response is String) {
        return response;
      _logger.e('解析聊天响应失败: $response');
      _logger.e('聊天失败: $e');
      return '抱歉，我遇到了一些问题，无法回应您的消息。';
}

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// 阿里云OSS服务
class OssService {
  final ApiClient _apiClient;
  final Dio _dio = Dio();
  /// OSS配置
  Map<String, dynamic>? _ossConfig;
  /// 构造函数
  OssService({required ApiClient apiClient}) : _apiClient = apiClient;
  /// 获取OSS配置
  Future<Map<String, dynamic>> _getOssConfig() async {
    if (_ossConfig != null) {
      return _ossConfig!;
    }
    try {
      final response = await _apiClient.post('/api/oss/config', data: {});
      _ossConfig = response['data'];
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('获取OSS配置失败: $e');
      throw Exception('获取OSS配置失败');
  }
  /// 上传文件到OSS
  Future<String> uploadFile({
    required File file,
    required String objectKey,
  }) async {
      // 获取OSS配置
      final config = await _getOssConfig();
      // 构建表单数据
      final formData = FormData.fromMap({
        'OSSAccessKeyId': config['accessKeyId'],
        'policy': config['policy'],
        'signature': config['signature'],
        'key': objectKey,
        'success_action_status': '200',
        'file': await MultipartFile.fromFile(file.path),
      });
      // 上传文件
      final response = await _dio.post(
        config['host'],
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );
      // 检查响应状态
      if (response.statusCode == 200) {
        // 返回文件URL
        return '${config['host']}/$objectKey';
      } else {
        throw Exception('上传文件失败: ${response.statusCode}');
      }
      logger.e('上传文件到OSS失败: $e');
      throw Exception('上传文件到OSS失败');
  /// 下载文件
  Future<File> downloadFile({
    required String url,
    required String savePath,
      // 下载文件
      final response = await _dio.download(
        url,
        savePath,
          responseType: ResponseType.bytes,
        return File(savePath);
        throw Exception('下载文件失败: ${response.statusCode}');
      logger.e('从OSS下载文件失败: $e');
      throw Exception('从OSS下载文件失败');
  /// 获取文件URL
  String getFileUrl(String objectKey) {
    if (_ossConfig == null) {
      throw Exception('OSS配置未初始化');
    return '${_ossConfig!['host']}/$objectKey';
}

import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/errors/exceptions.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
/// Agent服务
class AgentService {
  late final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  AgentService({ApiClient? apiClient}) {
    if (apiClient != null) {
      _apiClient = apiClient;
    } else {
      // For testing, we'll just use a mock implementation
      _apiClient = _createMockApiClient();
    }
    _logger.i('初始化Agent服务');
  }
  /// 创建模拟API客户端
  ApiClient _createMockApiClient() {
    return ApiClient(
      localStorage: LocalStorage(
        SharedPreferences.getInstance() as SharedPreferences
      )
    );
  /// 获取所有Agent配置
  Future<List<AgentConfigModel>> getAllAgentConfigs() async {
    try {
      _logger.i('获取所有Agent配置');
      final response = await _apiClient.post('/agent/getAllAgentConfigs');
      final List<dynamic> data = response['data'];
      return data.map((json) => AgentConfigModel.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('获取Agent配置失败', e);
      throw ServerException(
        message: '获取Agent配置失败: ${e.message}',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      throw ServerException(message: '获取Agent配置失败: $e');
  /// 获取用户的所有聊天会话
  Future<List<AgentChatSessionModel>> getUserChatSessions(int userId) async {
      _logger.i('获取用户聊天会话: userId=$userId');
      final response = await _apiClient.post(
        '/agent/getUserChatSessions',
        data: {'userId': userId},
      return data.map((json) => AgentChatSessionModel.fromJson(json)).toList();
      _logger.e('获取用户聊天会话失败', e);
        message: '获取聊天会话失败: ${e.message}',
      throw ServerException(message: '获取聊天会话失败: $e');
  /// 获取聊天会话详情
  Future<AgentChatSessionModel> getChatSessionDetail(int sessionId) async {
      _logger.i('获取聊天会话详情: sessionId=$sessionId');
        '/agent/getChatSessionDetail',
        data: {'sessionId': sessionId},
      return AgentChatSessionModel.fromJson(response['data']);
      _logger.e('获取聊天会话详情失败', e);
        message: '获取聊天会话详情失败: ${e.message}',
      throw ServerException(message: '获取聊天会话详情失败: $e');
  /// 创建聊天会话
  Future<AgentChatSessionModel> createChatSession({
    required int userId,
    required int agentId,
    required String title,
  }) async {
      _logger.i('创建聊天会话: userId=$userId, agentId=$agentId, title=$title');
        '/agent/createChatSession',
        data: {
          'userId': userId,
          'agentId': agentId,
          'title': title,
        },
      _logger.e('创建聊天会话失败', e);
        message: '创建聊天会话失败: ${e.message}',
      throw ServerException(message: '创建聊天会话失败: $e');
  /// 发送聊天消息
  Future<AgentChatMessageModel> sendChatMessage({
    required int sessionId,
    required String content,
      _logger.i('发送聊天消息: sessionId=$sessionId, content=$content');
        '/agent/sendChatMessage',
          'sessionId': sessionId,
          'content': content,
      return AgentChatMessageModel.fromJson(response['data']);
      _logger.e('发送聊天消息失败', e);
        message: '发送聊天消息失败: ${e.message}',
      throw ServerException(message: '发送聊天消息失败: $e');
  /// 模拟获取Agent配置
  List<AgentConfigModel> getMockAgentConfigs() {
    _logger.i('获取模拟Agent配置');
    return [
      AgentConfigModel(
        id: 1,
        name: '心灵导师',
        avatar: 'assets/images/agents/mentor.png',
        description: '富有智慧的心灵导师，帮助你解决生活中的困惑',
        prompt: '你是一个富有智慧的心灵导师，善于倾听和给予建议。你的回答应该温暖、有智慧，并且能够帮助用户解决生活中的困惑。',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
        id: 2,
        name: '情绪疗愈师',
        avatar: 'assets/images/agents/therapist.png',
        description: '专注于情绪疗愈，帮助你缓解压力和焦虑',
        prompt: '你是一个专注于情绪疗愈的心理咨询师，善于帮助用户缓解压力和焦虑。你的回答应该充满理解和支持，并提供实用的情绪管理技巧。',
        id: 3,
        name: '创意伙伴',
        avatar: 'assets/images/agents/creative.png',
        description: '激发你的创造力，帮助你找到新的灵感',
        prompt: '你是一个充满创意的伙伴，善于激发用户的创造力和想象力。你的回答应该充满灵感和新颖的想法，帮助用户突破思维限制。',
        id: 4,
        name: '职场顾问',
        avatar: 'assets/images/agents/career.png',
        description: '提供职业发展建议，帮助你规划职业道路',
        prompt: '你是一个经验丰富的职场顾问，善于提供职业发展建议。你的回答应该专业、实用，并且能够帮助用户规划职业道路和解决工作中的问题。',
    ];
  /// 模拟获取用户聊天会话
  List<AgentChatSessionModel> getMockUserChatSessions(int userId) {
    _logger.i('获取模拟用户聊天会话: userId=$userId');
    final agents = getMockAgentConfigs();
      AgentChatSessionModel(
        userId: userId,
        agentId: 1,
        title: '关于生活的困惑',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
        agent: agents[0],
        agentId: 2,
        title: '如何缓解压力',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        agent: agents[1],
        agentId: 3,
        title: '寻找创作灵感',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        agent: agents[2],
  /// 模拟获取聊天会话详情
  AgentChatSessionModel getMockChatSessionDetail(int sessionId) {
    _logger.i('获取模拟聊天会话详情: sessionId=$sessionId');
    final agent = agents[(sessionId - 1) % agents.length];
    return AgentChatSessionModel(
      id: sessionId,
      userId: 1,
      agentId: agent.id,
      title: '模拟会话 $sessionId',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
      agent: agent,
      messages: _getMockChatMessages(sessionId),
  /// 模拟获取聊天消息
  List<AgentChatMessageModel> _getMockChatMessages(int sessionId) {
      AgentChatMessageModel(
        sessionId: sessionId,
        role: MessageRole.system,
        content: '欢迎使用One Moment的AI助手功能，我将尽力帮助你解决问题。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        role: MessageRole.user,
        content: '你好，我最近感到有些迷茫，不知道该如何规划未来。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 25)),
        role: MessageRole.assistant,
        content: '感谢你的分享。迷茫是很正常的感受，每个人在人生的不同阶段都可能经历这种感觉。让我们一起来探讨一下，你现在最关心的是什么方面的规划呢？是学业、职业还是个人发展？',
        createdAt: DateTime.now().subtract(const Duration(minutes: 24)),
        content: '主要是职业方面的，我不确定现在的工作是否适合我长期发展。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
        id: 5,
        content: '理解你的顾虑。关于职业发展，我建议可以从以下几个方面思考：\n\n1. 回顾一下目前工作中你最享受的部分和最不喜欢的部分\n2. 思考你的长期职业目标是什么\n3. 评估现在的工作是否能帮助你积累有价值的经验和技能\n4. 考虑你的个人价值观和工作环境是否匹配\n\n你能分享一下你现在工作中最喜欢和最不喜欢的方面吗？这样我们可以更深入地讨论。',
        createdAt: DateTime.now().subtract(const Duration(minutes: 18)),
}

import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/auth_model.dart';
/// 认证服务
class AuthService {
  final ApiClient _apiClient;
  final LocalStorage _localStorage;
  final LoggerUtil _logger = LoggerUtil();
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_info';
  AuthService({
    required ApiClient apiClient,
    required LocalStorage localStorage,
  })  : _apiClient = apiClient,
        _localStorage = localStorage {
    _logger.i('初始化认证服务');
  }
  /// 用户注册
  Future<LoginResponse> register(RegisterRequest request) async {
    try {
      _logger.i('用户注册: phone=${request.phone}');
      final response = await _apiClient.post(
        '/auth/register',
        data: request.toJson(),
      );
      if (response['success'] == true && response['data'] != null) {
        final loginResponse = LoginResponse.fromJson(response['data']);
        await _saveAuthData(loginResponse);
        _logger.i('用户注册成功');
        return loginResponse;
      } else {
        throw Exception(response['message'] ?? '注册失败');
      }
    } catch (e) {
      _logger.e('用户注册失败', e);
      rethrow;
    }
  /// 用户登录
  Future<LoginResponse> login(LoginRequest request) async {
      _logger.i('用户登录: loginType=${request.loginType}, phone=${request.phone}');
        '/auth/login',
        _logger.i('用户登录成功');
        throw Exception(response['message'] ?? '登录失败');
      _logger.e('用户登录失败', e);
  /// 刷新令牌
  Future<LoginResponse> refreshToken() async {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        throw Exception('刷新令牌不存在');
      _logger.i('刷新令牌');
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
        _logger.i('令牌刷新成功');
        throw Exception(response['message'] ?? '刷新令牌失败');
      _logger.e('刷新令牌失败', e);
      await logout(); // 刷新失败时清除本地数据
  /// 检查登录状态
  Future<Map<String, dynamic>> checkStatus() async {
      final response = await _apiClient.post('/auth/checkStatus');
      return response['data'] ?? {};
      _logger.e('检查登录状态失败', e);
      return {'loginRequired': false, 'message': '检查登录状态失败'};
  /// 检查AI功能登录状态
  Future<Map<String, dynamic>> checkAILoginStatus() async {
      final accessToken = await getAccessToken();
      final headers = accessToken != null
          ? {'Authorization': 'Bearer $accessToken'}
          : <String, String>{};
        '/auth/checkAILoginStatus',
        headers: headers,
      _logger.e('检查AI登录状态失败', e);
      return {'isLoggedIn': false, 'loginRequired': true, 'message': 'AI对话功能需要登录'};
  /// 用户登出
  Future<void> logout() async {
      _logger.i('用户登出');
      await _clearAuthData();
      _logger.e('用户登出失败', e);
  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    return await _localStorage.getString(_accessTokenKey);
  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    return await _localStorage.getString(_refreshTokenKey);
  /// 获取用户信息
  Future<UserModel?> getUserInfo() async {
    final userJson = await _localStorage.getString(_userKey);
    if (userJson != null) {
      try {
        final userMap = Map<String, dynamic>.from(
          await _localStorage.getMap(_userKey) ?? {},
        );
        return UserModel.fromJson(userMap);
      } catch (e) {
        _logger.e('解析用户信息失败', e);
        return null;
    return null;
  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    return accessToken != null && accessToken.isNotEmpty;
  /// 保存认证数据
  Future<void> _saveAuthData(LoginResponse loginResponse) async {
    await _localStorage.setString(_accessTokenKey, loginResponse.accessToken);
    await _localStorage.setString(_refreshTokenKey, loginResponse.refreshToken);
    await _localStorage.setMap(_userKey, loginResponse.user.toJson());
  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await _localStorage.remove(_accessTokenKey);
    await _localStorage.remove(_refreshTokenKey);
    await _localStorage.remove(_userKey);
  /// 初始化测试账号
  Future<String> initTestAccount() async {
      _logger.i('初始化测试账号');
      final response = await _apiClient.post('/auth/initTestAccount');
      if (response['success'] == true) {
        return response['data'] ?? '测试账号初始化成功';
        throw Exception(response['message'] ?? '初始化测试账号失败');
      _logger.e('初始化测试账号失败', e);
}

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/weather_model.dart';
/// 天气服务
class WeatherService {
  late final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  WeatherService({ApiClient? apiClient}) {
    if (apiClient != null) {
      _apiClient = apiClient;
    } else {
      _apiClient = ApiClient(localStorage: LocalStorage());
    }
    _logger.i('初始化天气服务');
  }
  /// 获取最新天气
  Future<WeatherModel?> getLatestWeather({String cityName = '北京'}) async {
    _logger.i('获取最新天气: $cityName');
    try {
      final response = await _apiClient.post(
        '/weather/getLatestWeather',
        data: {'cityName': cityName},
      );
      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response.containsKey('success')) {
        // 新的API响应格式
        if (response['success'] == true && response['data'] != null) {
          return WeatherModel.fromJson(response['data']);
        } else {
          _logger.e('获取天气失败: ${response['message']}');
          return null;
        }
      } else {
        // 旧的API响应格式
        return WeatherModel.fromJson(response);
      }
    } catch (e) {
      _logger.e('获取天气失败: $e');
      return null;
  /// 获取指定日期的天气
  Future<WeatherModel?> getWeatherByDate({
    required String cityName,
    required DateTime date,
  }) async {
    _logger.i('获取指定日期天气: $cityName, ${date.toIso8601String().split('T')[0]}');
        '/weather/getWeatherByDate',
        data: {
          'cityName': cityName,
          'date': date.toIso8601String().split('T')[0],
        },
          _logger.e('获取指定日期天气失败: ${response['message']}');
      _logger.e('获取指定日期天气失败: $e');
}

import 'package:dio/dio.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// 文件上传响应
class FileUploadResponse {
  final String fileUrl;
  final String objectKey;
  FileUploadResponse({required this.fileUrl, required this.objectKey});
  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      fileUrl: json['fileUrl'] as String,
      objectKey: json['objectKey'] as String,
    );
  }
}
/// 文件上传服务
class FileUploadService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  /// 构造函数
  FileUploadService({required ApiClient apiClient}) : _apiClient = apiClient;
  /// 上传文件到后端
  Future<FileUploadResponse> uploadFile(File file) async {
    _logger.i('开始上传文件: ${file.path}');
    try {
      // 创建表单数据
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
      });
      // 发送请求
      final response = await _apiClient.dio.post(
        '/moments/upload',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );
      // 检查响应
      if (response.statusCode == 200) {
        // 处理新的API响应格式
        final responseData = response.data;
        _logger.i('文件上传成功: $responseData');
        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data') &&
            responseData.containsKey('success')) {
          // 新的API响应格式
          if (responseData['success'] == true) {
            final data = responseData['data'];
            if (data is Map<String, dynamic>) {
              return FileUploadResponse.fromJson(data);
            } else {
              // 旧格式，只返回URL
              return FileUploadResponse(
                fileUrl: data.toString(),
                objectKey: '',
              );
            }
          } else {
            throw Exception('上传文件失败: ${responseData['message']}');
          }
        } else {
          // 旧的API响应格式或其他格式
          return FileUploadResponse(
            fileUrl: responseData.toString(),
            objectKey: '',
          );
        }
      } else {
        throw Exception('上传文件失败: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('上传文件失败: $e');
      throw Exception('上传文件失败: $e');
    }

import 'package:one_moment_app/core/network/ai_service.dart' as core_ai_service;
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/config/ai_config.dart';import 'package:one_moment_app/data/models/ai_config_model.dart';
/// AI服务
class AIService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  final core_ai_service.AIService _coreAIService = core_ai_service.AIService();
  final AIConfigManager _configManager = AIConfigManager();
  AIService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化AI服务');
  }
  /// 发送消息到AI服务
  Future<String> sendMessage(List<ChatMessage> messages) async {
    try {
      // 使用后端API发送消息
      final lastMessage = messages.lastWhere(
        (msg) => msg.role == MessageRole.user,
        orElse: () => throw Exception('没有找到用户消息'),
      );
      // 构建请求参数，包含完整的消息历史
      final requestData = {
        'message': lastMessage.content,
        'history': messages.where((msg) => msg.role != MessageRole.user || msg != lastMessage)
            .map((msg) => {
                  'role': msg.role.toString().split('.').last.toLowerCase(),
                  'content': msg.content,
                })
            .toList(),
      };
      _logger.i('发送AI聊天请求: $requestData');
      final response = await _apiClient.post(
        '/ai/chat',
        data: requestData,
      _logger.i('AI聊天响应: $response');
      if (response is Map<String, dynamic>) {
        // 处理标准API响应格式
        if (response.containsKey('data')) {
          if (response['data'] is String) {
            return response['data'] as String;
          } else if (response['data'] is Map<String, dynamic> &&
                    response['data'].containsKey('message')) {
            return response['data']['message'] as String;
          }
        } else if (response.containsKey('message') && response['message'] is String) {
          return response['message'] as String;
        }
      }
      // 如果无法解析响应，直接返回字符串表示
      return response.toString();
    } catch (e) {
      _logger.e('发送消息失败', e);
      throw Exception('发送消息失败: $e');
    }
  /// 获取当前AI模型配置
  Future<AIConfigModel> getCurrentModel() async {
      final response = await _apiClient.post('/ai/getCurrentModel');
      return AIConfigModel.fromJson(response as Map<String, dynamic>);
      _logger.e('获取当前AI模型失败', e);
      // 如果API调用失败，使用本地配置
      final localConfig = await _configManager.getModelConfig();
      return AIConfigModel(
        id: '1',
        name: localConfig.modelName,
        provider: localConfig.provider,
        description: '默认AI模型',
        features: ['文本分析', '情感识别'],
  /// 获取可用的AI模型列表
  Future<List<AIConfigModel>> getAvailableModels() async {
      final response = await _apiClient.post('/ai/getAvailableModels');
      if (response is! List<dynamic>) {
        _logger.e('获取可用AI模型响应格式错误: 预期List，实际${response.runtimeType}');
        throw Exception('获取可用AI模型响应格式错误');
      return response
          .map((item) => AIConfigModel.fromJson(item as Map<String, dynamic>))
          .toList();
      _logger.e('获取可用AI模型失败', e);
      // 如果API调用失败，返回默认模型列表
      return [
        AIConfigModel(
          id: '1',
          name: 'Tongyi Qianwen',
          provider: '通义千问',
          description: '阿里云提供的大语言模型',
          features: ['文本分析', '情感识别'],
        ),
          id: '2',
          name: 'DeepSeek',
          provider: 'DeepSeek',
          description: 'DeepSeek提供的大语言模型',
          features: ['文本分析', '代码生成'],
          id: '3',
          name: 'Kimi',
          provider: 'Kimi',
          description: 'Kimi提供的大语言模型',
          features: ['文本分析', '知识问答'],
          id: '4',
          name: 'MiniMax',
          provider: 'MiniMax',
          description: 'MiniMax提供的大语言模型',
          features: ['文本分析', '多轮对话'],
      ];
  /// 设置当前AI模型
  Future<bool> setCurrentModel(String modelId) async {
      await _apiClient.post('/ai/setCurrentModel', data: {'modelId': modelId});
      return true;
      _logger.e('设置当前AI模型失败', e);
      try {
        // 获取所有模型
        final models = await getAvailableModels();
        // 查找指定ID的模型
        final model = models.firstWhere((m) => m.id == modelId);
        // 保存到本地
        final success = await _configManager.saveModelConfig(AIModelConfig(apiKey: '', 
          modelName: model.name,
          provider: model.provider,
        ));
        return success;
      } catch (e) {
        _logger.e('保存本地AI模型配置失败', e);
        return false;
  /// 分析瞬间内容
  Future<String> analyzeMomentContent(String content) async {
      _logger.i('发送瞬间内容分析请求: $content');
      final response = await _apiClient.post('/ai/analyzeMoment', data: {'content': content});
      _logger.i('分析瞬间内容响应: $response');
          } else if (response['data'] is Map<String, dynamic>) {
            if (response['data'].containsKey('analysis')) {
              return response['data']['analysis'] as String;
            } else if (response['data'].containsKey('message')) {
              return response['data']['message'] as String;
            }
        } else if (response.containsKey('analysis')) {
          return response['analysis'] as String;
      _logger.e('分析瞬间内容失败', e);
      throw Exception('分析瞬间内容失败: $e');
}

import 'dart:math';
import 'package:dio/dio.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// 瞬间服务
class MomentService {
  final ApiClient _apiClient;
  final LoggerUtil _logger = LoggerUtil();
  MomentService({required ApiClient apiClient}) : _apiClient = apiClient {
    _logger.i('初始化瞬间服务');
  }
  /// 获取所有瞬间
  Future<List<MomentModel>> getAllMoments({int page = 0, int size = 10}) async {
    _logger.i('获取所有瞬间: page=$page, size=$size');
    try {
      final response = await _apiClient.post(
        '/moments/getMomentList',
        data: {
          'page': page,
          'size': size,
          'sort': 'createdAt,desc',
        },
      );
      _logger.d('获取所有瞬间响应: $response');
      if (response is! Map<String, dynamic>) {
        _logger.e('获取瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('获取瞬间响应格式错误');
      }
      if (!response.containsKey('content')) {
        _logger.e('获取瞬间响应缺少content字段: $response');
        // 尝试处理不同的响应格式
        if (response.containsKey('data') && response['data'] is List) {
          _logger.i('使用替代响应格式: data字段');
          final content = response['data'] as List<dynamic>;
          return _processContentList(content);
        }
        // 如果响应本身就是一个列表
        if (response.containsKey('_embedded') &&
            response['_embedded'] is Map &&
            (response['_embedded'] as Map).containsKey('moments')) {
          _logger.i('使用替代响应格式: _embedded.moments字段');
          final content = response['_embedded']['moments'] as List<dynamic>;
        if (response is List) {
          _logger.i('响应本身是一个列表');
          return _processContentList(response);
        throw Exception('获取瞬间响应格式错误: 缺少content字段');
      final content = response['content'];
      return _processContentList(content);
    } catch (e) {
      _logger.e('获取瞬间失败', e);
      // 返回空列表，不抛出异常
      _logger.w('返回空列表');
      return [];
    }
  /// 处理内容列表
  List<MomentModel> _processContentList(dynamic content) {
    if (content is! List<dynamic>) {
      _logger.e('内容格式错误: 预期List，实际${content.runtimeType}');
      throw Exception('内容格式错误: 不是List');
    final List<MomentModel> moments = [];
    for (var item in content) {
      try {
        if (item is Map<String, dynamic>) {
          moments.add(_mapResponseToMomentModel(item));
        } else {
          _logger.w('跳过无效的瞬间数据: $item');
      } catch (e) {
        _logger.e('解析瞬间数据失败', e);
    _logger.i('成功处理${moments.length}个瞬间');
    return moments;
  /// 获取指定日期的瞬间
  Future<List<MomentModel>> getMomentsByDate(DateTime date) async {
      _logger.i('获取指定日期的瞬间: ${date.toString()}');
      final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      final response = await _apiClient.post('/moments/getMomentsByDate', data: {'date': dateStr});
      _logger.d('获取指定日期的瞬间响应: $response');
      // 处理不同的响应格式
      if (response is Map<String, dynamic>) {
          _logger.i('使用data字段中的列表');
          final data = response['data'] as List<dynamic>;
          return data.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
      } else if (response is List<dynamic>) {
        _logger.i('响应直接是列表');
        return response.map((item) => _mapResponseToMomentModel(item as Map<String, dynamic>)).toList();
      _logger.e('获取指定日期的瞬间响应格式错误: $response');
      return []; // 返回空列表而不是抛出异常
      _logger.e('获取指定日期的瞬间失败: $e');
  /// 获取指定日期范围的瞬间
  Future<List<MomentModel>> getMomentsByDateRange(DateTime startDate, DateTime endDate) async {
      _logger.i('获取日期范围内的瞬间: ${startDate.toString()} - ${endDate.toString()}');
      final startDateStr = '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')}';
      final endDateStr = '${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}';
      final response = await _apiClient.post('/moments/getMomentsByDateRange', data: {
        'startDate': startDateStr,
        'endDate': endDateStr,
      });
      _logger.d('获取日期范围内的瞬间响应: $response');
        } else if (response.containsKey('content') && response['content'] is List) {
          _logger.i('使用content字段中的列表（分页响应）');
          final data = response['content'] as List<dynamic>;
          _logger.e('获取日期范围内的瞬间响应格式错误: 无法找到data或content字段');
          return [];
      } else {
        _logger.e('获取日期范围内的瞬间响应格式错误: 预期List或Map，实际${response.runtimeType}');
        return [];
      _logger.e('获取日期范围内的瞬间失败: $e');
  /// 获取随机瞬间
  Future<MomentModel> getRandomMoment() async {
      final response = await _apiClient.post('/moments/getRandomMoment');
      return _mapResponseToMomentModel(response as Map<String, dynamic>);
      throw Exception('获取随机瞬间失败: $e');
  /// 获取指定ID的瞬间
  Future<MomentModel> getMomentById(String id) async {
    _logger.i('获取瞬间详情: id=$id');
      // 确保ID是数字格式
      final numericId = int.tryParse(id) ?? id;
      final response = await _apiClient.post('/moments/getMomentById', data: {'momentId': numericId});
      _logger.d('获取瞬间详情响应: $response');
        _logger.e('获取瞬间详情响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('获取瞬间详情响应格式错误');
      return _mapResponseToMomentModel(response);
      _logger.e('获取瞬间详情失败', e);
      throw Exception('获取瞬间详情失败: $e');
  /// 创建瞬间
  Future<MomentModel> createMoment(MomentModel moment, {File? mediaFile}) async {
    _logger.i('创建瞬间: ${moment.content}, 媒体类型: ${moment.mediaType}');
      // 上传媒体文件
      String? mediaUrl;
      if (mediaFile != null) {
        _logger.i('上传媒体文件: ${mediaFile.path}');
        try {
          // 创建表单数据
          final formData = FormData.fromMap({
            'file': await MultipartFile.fromFile(
              mediaFile.path,
              filename: mediaFile.path.split('/').last,
            ),
          });
          // 上传文件
          final response = await _apiClient.post(
            '/moments/upload',
            data: formData,
          );
          // 处理响应
          mediaUrl = response as String;
          _logger.i('媒体文件上传成功: $mediaUrl');
        } catch (e) {
          _logger.e('媒体文件上传失败', e);
          throw Exception('媒体文件上传失败: $e');
      // 创建瞬间请求
      final contentType = moment.mediaType == MediaType.none
          ? 'TEXT'
          : moment.mediaType.toString().split('.').last.toUpperCase();
      _logger.d('准备创建瞬间请求，内容类型: $contentType');
      final request = {
        'contentType': contentType,
        'textContent': moment.content,
        'mediaUrl': mediaUrl,
        'locationName': moment.location,
        'latitude': 0.0, // 暂不支持
        'longitude': 0.0, // 暂不支持
        'weather': moment.weather,
        'temperature': 0.0, // 暂不支持
        'mood': moment.mood.toString().split('.').last.toUpperCase(),
        'isPrivate': moment.isPrivate,
        'tags': [], // 暂不支持标签
      };
      _logger.d('发送创建瞬间请求: $request');
      final response = await _apiClient.post('/moments/createMoment', data: request);
      _logger.d('创建瞬间响应: $response');
        _logger.e('创建瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('创建瞬间响应格式错误');
      // 检查是否是错误响应
      if (response.containsKey('success') && response['success'] == false) {
        final errorMessage = response['message'] ?? '创建瞬间失败';
        _logger.e('创建瞬间失败: $errorMessage');
        throw Exception(errorMessage);
      // 处理成功响应，提取data字段
      final momentData = response.containsKey('data') ? response['data'] : response;
      final result = _mapResponseToMomentModel(momentData);
      _logger.i('瞬间创建成功: ${result.id}');
      return result;
      _logger.e('创建瞬间失败', e);
      throw Exception('创建瞬间失败: $e');
  /// 更新瞬间
  Future<MomentModel> updateMoment(
    String id,
    String content, {
    String? location,
    String? weather,
    MoodType? mood,
    bool? isPrivate,
  }) async {
    _logger.i('更新瞬间: id=$id');
        'textContent': content,
        'locationName': location,
        'weather': weather,
        'mood': mood?.toString().split('.').last.toUpperCase(),
        'isPrivate': isPrivate,
      _logger.d('更新瞬间请求: $request');
      // 添加momentId到请求中
      request['momentId'] = numericId;
      final response = await _apiClient.post('/moments/updateMoment', data: request);
      _logger.d('更新瞬间响应: $response');
        _logger.e('更新瞬间响应格式错误: 预期Map，实际${response.runtimeType}');
        throw Exception('更新瞬间响应格式错误');
      _logger.e('更新瞬间失败', e);
      throw Exception('更新瞬间失败: $e');
  /// 更新瞬间（使用模型）
  @Deprecated('请使用 updateMoment(String id, String content, ...) 方法')
  Future<MomentModel> updateMomentModel(MomentModel moment) async {
      return updateMoment(
        moment.id,
        moment.content,
        location: moment.location,
        weather: moment.weather,
        mood: moment.mood,
        isPrivate: moment.isPrivate,
  /// 删除瞬间
  Future<void> deleteMoment(String id) async {
    _logger.i('删除瞬间: id=$id');
      await _apiClient.post('/moments/deleteMoment', data: {'momentId': numericId});
      _logger.i('删除瞬间成功: id=$id');
      _logger.e('删除瞬间失败', e);
      throw Exception('删除瞬间失败: $e');
  /// 将API响应映射为瞬间模型
  MomentModel _mapResponseToMomentModel(Map<String, dynamic> response) {
    _logger.d('映射瞬间响应: $response');
      return MomentModel(
        id: response['id']?.toString() ?? '',
        userId: response['userId']?.toString() ?? '',
        username: response['username']?.toString() ?? '',
        contentType: response['contentType']?.toString() ?? 'none',
        textContent: response['textContent']?.toString() ?? '',
        createdAt: response['createdAt'] != null
            ? DateTime.parse(response['createdAt'].toString())
            : DateTime.now(),
        locationName: response['locationName']?.toString(),
        weather: response['weather']?.toString(),
        temperature: response['temperature'] != null
            ? double.tryParse(response['temperature'].toString())
            : null,
        latitude: response['latitude'] != null
            ? double.tryParse(response['latitude'].toString())
        longitude: response['longitude'] != null
            ? double.tryParse(response['longitude'].toString())
        mediaUrl: response['mediaUrl']?.toString(),
        mediaObjectKey: response['mediaObjectKey']?.toString(),
        thumbnailUrl: response['thumbnailUrl']?.toString(),
        mood: _mapMoodType(response['mood']?.toString()),
        isPrivate: response['isPrivate'] as bool? ?? false,
      _logger.e('映射瞬间响应失败', e);
      _logger.e('响应数据: $response');
      // 尝试使用不同的字段名
        id: (response['id'] ?? '').toString(),
        textContent: response['textContent'] ?? response['content'] ?? '',
        locationName: response['locationName'] ?? response['location'],
        weather: response['weather'],
        mediaUrl: response['mediaUrl'] ?? response['thumbnailUrl'],
        mediaObjectKey: response['mediaObjectKey'],
        thumbnailUrl: response['thumbnailUrl'],
        isPrivate: response['isPrivate'] ?? false,
  /// 映射媒体类型
  MediaType _mapMediaType(String? contentType) {
    if (contentType == null) return MediaType.none;
    switch (contentType.toUpperCase()) {
      case 'IMAGE':
        return MediaType.image;
      case 'VIDEO':
        return MediaType.video;
      case 'AUDIO':
        return MediaType.audio;
      default:
        return MediaType.none;
  /// 映射心情类型
  MoodType _mapMoodType(String? mood) {
    if (mood == null) return MoodType.neutral;
    switch (mood.toLowerCase()) {
      case 'happy':
        return MoodType.happy;
      case 'calm':
        return MoodType.calm;
      case 'sad':
        return MoodType.sad;
      case 'excited':
        return MoodType.excited;
      case 'anxious':
        return MoodType.anxious;
        return MoodType.neutral;
}

import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/services/agent_service.dart';
import 'package:one_moment_app/data/services/tongyi_service.dart';
/// Agent状态
enum AgentStatus {
  /// 初始状态
  initial,
  /// 加载中
  loading,
  /// 加载成功
  loaded,
  /// 加载失败
  error,
}
/// Agent提供者
class AgentProvider with ChangeNotifier {
  late final AgentService _agentService;
  late final TongyiService _tongyiService;
  final LoggerUtil _logger = LoggerUtil();
  AgentStatus _status = AgentStatus.initial;
  List<AgentConfigModel> _agentConfigs = [];
  List<AgentChatSessionModel> _chatSessions = [];
  AgentChatSessionModel? _currentSession;
  String? _errorMessage;
  bool _isAiResponding = false;
  /// 构造函数
  AgentProvider({
    AgentService? agentService,
    TongyiService? tongyiService,
    ApiClient? apiClient
  }) {
    if (agentService != null) {
      _agentService = agentService;
    } else if (apiClient != null) {
      _agentService = AgentService(apiClient: apiClient);
    } else {
      _agentService = AgentService();
    }
    if (tongyiService != null) {
      _tongyiService = tongyiService;
      _tongyiService = TongyiService(apiClient: apiClient);
      // 使用默认的TongyiService
      _tongyiService = TongyiService(apiClient: ApiClient(localStorage: LocalStorage()));
  }
  /// AI是否正在响应
  bool get isAiResponding => _isAiResponding;
  /// 获取状态
  AgentStatus get status => _status;
  /// 获取Agent配置列表
  List<AgentConfigModel> get agentConfigs => _agentConfigs;
  /// 获取聊天会话列表
  List<AgentChatSessionModel> get chatSessions => _chatSessions;
  /// 获取当前会话
  AgentChatSessionModel? get currentSession => _currentSession;
  /// 获取错误信息
  String? get errorMessage => _errorMessage;
  /// 获取所有Agent配置
  Future<void> fetchAgentConfigs() async {
    _status = AgentStatus.loading;
    _errorMessage = null;
    notifyListeners();
    try {
      _logger.i('获取所有Agent配置');
      // 在实际应用中，这里应该调用后端API
      // final configs = await _agentService.getAllAgentConfigs();
      // 使用模拟数据
      final configs = _agentService.getMockAgentConfigs();
      _agentConfigs = configs;
      _status = AgentStatus.loaded;
    } catch (e) {
      _logger.e('获取Agent配置失败', e);
      _errorMessage = '获取Agent配置失败，请稍后重试';
      _status = AgentStatus.error;
  /// 获取用户的所有聊天会话
  Future<void> fetchUserChatSessions(int userId) async {
      _logger.i('获取用户聊天会话: userId=$userId');
      // final sessions = await _agentService.getUserChatSessions(userId);
      final sessions = _agentService.getMockUserChatSessions(userId);
      _chatSessions = sessions;
      _logger.e('获取用户聊天会话失败', e);
      _errorMessage = '获取聊天会话失败，请稍后重试';
  /// 获取聊天会话详情
  Future<void> fetchChatSessionDetail(int sessionId) async {
      _logger.i('获取聊天会话详情: sessionId=$sessionId');
      // final session = await _agentService.getChatSessionDetail(sessionId);
      final session = _agentService.getMockChatSessionDetail(sessionId);
      _currentSession = session;
      _logger.e('获取聊天会话详情失败', e);
      _errorMessage = '获取聊天会话详情失败，请稍后重试';
  /// 创建聊天会话
  Future<AgentChatSessionModel?> createChatSession({
    required int userId,
    required int agentId,
    required String title,
    bool requireLogin = true,
  }) async {
      _logger.i('创建聊天会话: userId=$userId, agentId=$agentId, title=$title');
      // final session = await _agentService.createChatSession(
      //   userId: userId,
      //   agentId: agentId,
      //   title: title,
      // );
      final agent = _agentConfigs.firstWhere((a) => a.id == agentId);
      final session = AgentChatSessionModel(
        id: _chatSessions.length + 1,
        userId: userId,
        agentId: agentId,
        title: title,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        agent: agent,
        messages: [
          AgentChatMessageModel(
            id: 1,
            sessionId: _chatSessions.length + 1,
            role: MessageRole.system,
            content: '欢迎使用One Moment的AI助手功能，我将尽力帮助你解决问题。',
            createdAt: DateTime.now(),
          ),
        ],
      );
      _chatSessions.add(session);
      notifyListeners();
      return session;
      _logger.e('创建聊天会话失败', e);
      _errorMessage = '创建聊天会话失败，请稍后重试';
      return null;
  /// 发送聊天消息
  Future<AgentChatMessageModel?> sendChatMessage({
    required String content,
    if (_currentSession == null) {
      _errorMessage = '当前没有活动的聊天会话';
      _logger.i('发送聊天消息: sessionId=${_currentSession!.id}, content=$content');
      // 创建用户消息
      final userMessage = AgentChatMessageModel(
        id: _currentSession!.messages?.length ?? 0 + 1,
        sessionId: _currentSession!.id,
        role: MessageRole.user,
        content: content,
      // 添加到当前会话
      _currentSession!.messages?.add(userMessage);
      // 设置AI响应状态
      _isAiResponding = true;
      // 调用通义千问API
      final messages = _currentSession!.messages ?? [];
      final modelName = _currentSession!.agent?.modelName ?? 'qwen-turbo';
      String aiResponse = ''; // 初始化为空字符串
      try {
        _logger.d('准备调用通义千问API，消息数量: ${messages.length}');
        // 添加重试逻辑
        int retryCount = 0;
        const maxRetries = 2;
        Exception? lastException;
        while (retryCount <= maxRetries) {
          try {
            String response = await _tongyiService.sendMessage(
              messages: messages,
              modelName: modelName,
            );
            // 检查响应是否有效
            if (response.isNotEmpty) {
              aiResponse = response; // 赋值给aiResponse
              _logger.d('成功获取AI响应: ${aiResponse.substring(0, aiResponse.length > 50 ? 50 : aiResponse.length)}...');
              break; // 成功获取响应，跳出循环
            } else {
              throw Exception('AI响应为空');
            }
          } catch (e) {
            lastException = e as Exception;
            _logger.w('调用通义千问API失败 (尝试 ${retryCount + 1}/${maxRetries + 1}): $e');
            retryCount++;
            if (retryCount <= maxRetries) {
              // 等待一段时间后重试
              await Future.delayed(Duration(milliseconds: 500 * retryCount));
          }
        }
        // 如果所有重试都失败，则抛出最后一个异常
        if (retryCount > maxRetries) {
          throw lastException ?? Exception('调用通义千问API失败，已重试 $maxRetries 次');
      } catch (e) {
        _logger.e('调用通义千问API最终失败', e);
        aiResponse = '抱歉，我暂时无法回应您的消息。请稍后再试。(错误: ${e.toString()})';
      } finally {
        _isAiResponding = false;
      }
      // 创建AI回复消息
      final aiMessage = AgentChatMessageModel(
        role: MessageRole.assistant,
        content: aiResponse,
      _currentSession!.messages?.add(aiMessage);
      return aiMessage;
      _logger.e('发送聊天消息失败', e);
      _errorMessage = '发送聊天消息失败，请稍后重试';
      _isAiResponding = false;
      // 即使发生错误，也创建一个错误消息并添加到会话中
      final errorMessage = AgentChatMessageModel(
        content: '抱歉，我暂时无法回应您的消息。请稍后再试。(错误: ${e.toString()})',
      _currentSession!.messages?.add(errorMessage);
      return errorMessage;
  /// 设置当前会话
  void setCurrentSession(AgentChatSessionModel session) {
    _currentSession = session;
  /// 清除当前会话
  void clearCurrentSession() {
    _currentSession = null;
  /// 重置状态
  void reset() {
    _status = AgentStatus.initial;
    _agentConfigs = [];
    _chatSessions = [];

import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/auth_model.dart';
import 'package:one_moment_app/data/services/auth_service.dart';
/// 认证状态
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}
/// 认证Provider
class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  final LoggerUtil _logger = LoggerUtil();
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;
  bool _isLoading = false;
  AuthProvider({required AuthService authService}) : _authService = authService {
    _logger.i('初始化认证Provider');
    _checkAuthStatus();
  }
  // Getters
  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated;
  /// 检查认证状态
  Future<void> _checkAuthStatus() async {
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        _user = await _authService.getUserInfo();
        _state = AuthState.authenticated;
      } else {
        _state = AuthState.unauthenticated;
      }
    } catch (e) {
      _logger.e('检查认证状态失败', e);
      _state = AuthState.unauthenticated;
    }
    notifyListeners();
  /// 用户注册
  Future<bool> register({
    required String phone,
    required String password,
    String? nickname,
    String? email,
  }) async {
    _setLoading(true);
    _clearError();
      final request = RegisterRequest(
        phone: phone,
        password: password,
        nickname: nickname,
        email: email,
      );
      final response = await _authService.register(request);
      _user = response.user;
      _state = AuthState.authenticated;
      _setLoading(false);
      
      _logger.i('用户注册成功: ${_user?.username}');
      return true;
      _logger.e('用户注册失败', e);
      _setError(e.toString());
      _state = AuthState.error;
      return false;
  /// 手机号登录
  Future<bool> loginWithPhone({
      final request = LoginRequest(
        loginType: 'phone',
      final response = await _authService.login(request);
      _logger.i('用户登录成功: ${_user?.username}');
      _logger.e('用户登录失败', e);
  /// 微信登录
  Future<bool> loginWithWechat({
    required String wechatCode,
    String? wechatUserInfo,
        loginType: 'wechat',
        wechatCode: wechatCode,
        wechatUserInfo: wechatUserInfo,
      _logger.i('微信登录成功: ${_user?.username}');
      _logger.e('微信登录失败', e);
  /// 刷新令牌
  Future<bool> refreshToken() async {
      final response = await _authService.refreshToken();
      notifyListeners();
      _logger.i('令牌刷新成功');
      _logger.e('令牌刷新失败', e);
      await logout();
  /// 检查AI功能登录状态
  Future<Map<String, dynamic>> checkAILoginStatus() async {
      return await _authService.checkAILoginStatus();
      _logger.e('检查AI登录状态失败', e);
      return {'isLoggedIn': false, 'loginRequired': true, 'message': 'AI对话功能需要登录'};
  /// 用户登出
  Future<void> logout() async {
      await _authService.logout();
      _user = null;
      _clearError();
      _logger.i('用户登出成功');
      _logger.e('用户登出失败', e);
  /// 初始化测试账号
  Future<String> initTestAccount() async {
      return await _authService.initTestAccount();
      _logger.e('初始化测试账号失败', e);
      rethrow;
  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;

import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/file_upload_service.dart';
import 'package:one_moment_app/data/services/oss_service.dart';
/// 瞬间提供者
class MomentProvider extends ChangeNotifier {
  final ApiClient _apiClient;
  final OssService _ossService;
  final FileUploadService _fileUploadService;
  final LoggerUtil _logger = LoggerUtil();
  /// 瞬间列表
  List<MomentModel> _moments = [];
  /// 是否正在加载
  bool _isLoading = false;
  /// 是否正在上传
  bool _isUploading = false;
  /// 错误信息
  String? _errorMessage;
  /// 已上传的文件URL
  String? _uploadedFileUrl;
  /// 已上传的文件对象键
  String? _uploadedFileObjectKey;
  /// 构造函数
  MomentProvider({
    required ApiClient apiClient,
    required OssService ossService,
  }) : _apiClient = apiClient,
       _ossService = ossService,
       _fileUploadService = FileUploadService(apiClient: apiClient);
  /// 获取瞬间列表
  List<MomentModel> get moments => _moments;
  bool get isLoading => _isLoading;
  bool get isUploading => _isUploading;
  String? get errorMessage => _errorMessage;
  String? get uploadedFileUrl => _uploadedFileUrl;
  String? get uploadedFileObjectKey => _uploadedFileObjectKey;
  /// 获取所有瞬间
  Future<void> fetchMoments() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      final response = await _apiClient.post('/moments/getMomentList', data: {});
      _logger.i('获取瞬间列表响应: $response');
      List<dynamic> data;
      if (response is Map<String, dynamic>) {
        if (response.containsKey('content')) {
          // 分页响应
          data = response['content'] as List<dynamic>;
        } else if (response.containsKey('data')) {
          // 包含data字段的响应
          data = response['data'] as List<dynamic>;
        } else {
          _logger.e('获取瞬间列表响应格式错误: $response');
          _errorMessage = '获取瞬间列表失败: 响应格式错误';
          _isLoading = false;
          notifyListeners();
          return;
        }
      } else if (response is List<dynamic>) {
        // 直接返回的是列表
        data = response;
      } else {
        _logger.e('获取瞬间列表响应格式错误: $response');
        _errorMessage = '获取瞬间列表失败: 响应格式错误';
        _isLoading = false;
        notifyListeners();
        return;
      }
      _moments = data.map((item) => MomentModel.fromJson(item)).toList();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _logger.e('获取瞬间列表失败: $e');
      _errorMessage = '获取瞬间列表失败: $e';
    }
  }
  /// 获取指定日期的瞬间
  Future<List<MomentModel>> fetchMomentsByDate(DateTime date) async {
      final response = await _apiClient.post('/moments/getMomentsByDate', data: {
        'date': date.toIso8601String().split('T')[0],
      });
      _logger.i('获取指定日期瞬间响应: $response');
      if (response is Map<String, dynamic> && response.containsKey('data')) {
        // 处理包含data字段的响应
        data = response['data'] as List<dynamic>;
        _logger.e('获取指定日期瞬间响应格式错误: $response');
        _errorMessage = '获取指定日期瞬间失败: 响应格式错误';
        return [];
      final moments = data.map((item) => MomentModel.fromJson(item)).toList();
      return moments;
      _logger.e('获取指定日期瞬间失败: $e');
      _errorMessage = '获取指定日期瞬间失败: $e';
      // 返回空列表
      return [];
  /// 创建瞬间
  Future<bool> createMoment({
    required String content,
    required MediaType mediaType,
    required String location,
    required String weather,
    required MoodType mood,
  }) async {
    _isUploading = true;
      // 创建瞬间，使用已上传的文件URL和对象键
      final response = await _apiClient.post('/moments/createMoment', data: {
        'contentType': mediaType.toString().split('.').last,
        'textContent': content,
        'mediaUrl': _uploadedFileUrl,
        'mediaObjectKey': _uploadedFileObjectKey,
        'locationName': location,
        'weather': weather,
        'mood': mood.toString().split('.').last
      _logger.i('创建瞬间响应: $response');
      // 处理响应
        Map<String, dynamic> momentData;
        // 检查是否有data字段
        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is Map<String, dynamic>) {
            momentData = data;
          } else {
            _logger.e('创建瞬间响应data字段格式错误: $data');
            _errorMessage = '创建瞬间失败: 响应格式错误';
            _isUploading = false;
            notifyListeners();
            return false;
          }
          // 直接返回的是瞬间数据
          momentData = response;
        // 添加到列表
        final newMoment = MomentModel.fromJson(momentData);
        _moments.insert(0, newMoment);
        _isUploading = false;
        return true;
      _logger.e('创建瞬间响应格式错误: $response');
      _errorMessage = '创建瞬间失败: 响应格式错误';
      _isUploading = false;
      return false;
      _logger.e('创建瞬间失败: $e');
      _errorMessage = '创建瞬间失败: $e';
  /// 更新瞬间
  Future<bool> updateMoment({
    required String id,
    File? mediaFile,
    String? location,
    String? weather,
      // 如果有新的媒体文件，先上传
      if (mediaFile != null) {
        final uploadSuccess = await uploadFile(file: mediaFile, mediaType: mediaType);
        if (!uploadSuccess) {
          _isUploading = false;
          return false;
      // 更新瞬间
      final response = await _apiClient.post('/moments/updateMoment', data: {
        'momentId': id,
      _logger.i('更新瞬间响应: $response');
            _logger.e('更新瞬间响应data字段格式错误: $data');
            _errorMessage = '更新瞬间失败: 响应格式错误';
        // 更新列表中的瞬间
        final updatedMoment = MomentModel.fromJson(momentData);
        final index = _moments.indexWhere((moment) => moment.id == id);
        if (index != -1) {
          _moments[index] = updatedMoment;
      _logger.e('更新瞬间响应格式错误: $response');
      _errorMessage = '更新瞬间失败: 响应格式错误';
      _logger.e('更新瞬间失败: $e');
      _errorMessage = '更新瞬间失败: $e';
  /// 删除瞬间
  Future<bool> deleteMoment(String momentId) async {
      await _apiClient.post('/moments/deleteMoment', data: {
        'momentId': momentId,
      // 从列表中移除
      _moments.removeWhere((moment) => moment.id == momentId);
      return true;
      _logger.e('删除瞬间失败: $e');
      _errorMessage = '删除瞬间失败: $e';
  /// 清除错误
  void clearError() {
  /// 上传文件
  Future<bool> uploadFile({
    required File file,
    _uploadedFileUrl = null;
    _uploadedFileObjectKey = null;
      // 上传文件到后端
      final response = await _fileUploadService.uploadFile(file);
      // 保存文件URL和对象键
      _uploadedFileUrl = response.fileUrl;
      _uploadedFileObjectKey = response.objectKey;
      _logger.e('上传文件失败: $e');
      _errorMessage = '上传文件失败: $e';
  /// 清除已上传的文件
  void clearUploadedFile() {
}

import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/weather_model.dart';
import 'package:one_moment_app/data/services/weather_service.dart';
/// 天气状态
enum WeatherStatus {
  /// 初始状态
  initial,
  /// 加载中
  loading,
  /// 加载成功
  loaded,
  /// 加载失败
  error,
}
/// 天气提供者
class WeatherProvider with ChangeNotifier {
  late final WeatherService _weatherService;
  final LoggerUtil _logger = LoggerUtil();
  WeatherStatus _status = WeatherStatus.initial;
  WeatherModel? _weather;
  String? _errorMessage;
  /// 构造函数
  WeatherProvider({WeatherService? weatherService, ApiClient? apiClient}) {
    if (weatherService != null) {
      _weatherService = weatherService;
    } else if (apiClient != null) {
      _weatherService = WeatherService(apiClient: apiClient);
    } else {
      _weatherService = WeatherService();
    }
  }
  /// 获取天气状态
  WeatherStatus get status => _status;
  /// 获取天气数据
  WeatherModel? get weather => _weather;
  /// 获取错误信息
  String? get errorMessage => _errorMessage;
  /// 获取最新天气
  Future<void> fetchLatestWeather({String cityName = '北京'}) async {
    _status = WeatherStatus.loading;
    _errorMessage = null;
    notifyListeners();
    try {
      _logger.i('获取最新天气: $cityName');
      final weather = await _weatherService.getLatestWeather(cityName: cityName);
      if (weather != null) {
        _weather = weather;
        _status = WeatherStatus.loaded;
      } else {
        _errorMessage = '无法获取天气信息，请检查网络连接或定位权限';
        _status = WeatherStatus.error;
      }
    } catch (e) {
      _logger.e('获取天气失败', e);
      _errorMessage = '获取天气信息失败，请稍后重试';
      _status = WeatherStatus.error;
  /// 获取指定日期的天气
  Future<void> fetchWeatherByDate({
    required DateTime date,
    String cityName = '北京',
  }) async {
      _logger.i('获取指定日期天气: $cityName, ${date.toIso8601String().split('T')[0]}');
      final weather = await _weatherService.getWeatherByDate(
        cityName: cityName,
        date: date,
      );
        _errorMessage = '无法获取指定日期的天气信息';
      _logger.e('获取指定日期天气失败', e);
  /// 重置状态
  void reset() {
    _status = WeatherStatus.initial;
    _weather = null;

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:location/location.dart';
import 'package:provider/provider.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
/// 创建瞬间页面
class MomentCreatePage extends StatefulWidget {
  const MomentCreatePage({Key? key}) : super(key: key);
  @override
  State<MomentCreatePage> createState() => _MomentCreatePageState();
}
class _MomentCreatePageState extends State<MomentCreatePage> {
  final TextEditingController _contentController = TextEditingController();
  MediaType _selectedMediaType = MediaType.none;
  File? _mediaFile;
  String _location = '未知位置';
  String _weather = '未知天气';
  MoodType _selectedMood = MoodType.neutral;
  bool _isLoading = false;
  bool _isLocationLoading = false;
  bool _isCheckingTodayMoment = true;
  MomentModel? _todayMoment;
  void initState() {
    super.initState();
    // 不再检查今日瞬间，每次都是新的创建页面
    _getLocationAndWeather();
    setState(() {
      _isCheckingTodayMoment = false;
    });
  }
  /// 检查今日是否已发布瞬间
  Future<void> _checkTodayMoment() async {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);
      // 获取今天的日期（只保留年月日）
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);
      // 获取今日瞬间
      final moments = await momentProvider.fetchMomentsByDate(todayDate);
      if (moments.isNotEmpty) {
        // 如果有多条记录，只取最新的一条
        final latestMoment = moments.reduce((a, b) =>
          a.createdAt.isAfter(b.createdAt) ? a : b);
        setState(() {
          _todayMoment = latestMoment;
          _isCheckingTodayMoment = false;
        });
        // 预填充编辑内容
        _contentController.text = latestMoment.textContent;
        _selectedMood = latestMoment.mood;
        _location = latestMoment.locationName ?? '未知位置';
        _weather = latestMoment.weather ?? '未知天气';
      } else {
      }
    } catch (e) {
      print('检查今日瞬间失败: $e');
      setState(() {
        _isCheckingTodayMoment = false;
      });
    }
  void dispose() {
    _contentController.dispose();
    super.dispose();
  /// 获取位置和天气
  Future<void> _getLocationAndWeather() async {
      _isLocationLoading = true;
      // 获取位置
      final location = await _getCurrentLocation();
      if (location != null) {
          _location = '正在获取位置...';
        // 根据位置获取地址
        // 这里应该调用地理编码API获取地址
          _location = '北京市海淀区'; // 模拟数据
      // 获取天气
      final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
      final weather = weatherProvider.weather;
      if (weather != null) {
          _weather = '${weather.weatherCondition}，${weather.temperature}°C';
      final logger = LoggerUtil();
      logger.e('获取位置和天气失败: $e');
    } finally {
        _isLocationLoading = false;
  /// 获取当前位置
  Future<LocationData?> _getCurrentLocation() async {
    final location = Location();
    bool serviceEnabled;
    PermissionStatus permissionGranted;
    // 检查位置服务是否启用
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        return null;
    // 检查位置权限
    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
    // 获取位置
    return await location.getLocation();
  /// 选择图片
  Future<void> _pickImage(ImageSource source) async {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);
      if (pickedFile != null) {
          _isLoading = true;
        // 上传文件到后端
        final momentProvider = Provider.of<MomentProvider>(context, listen: false);
        final file = File(pickedFile.path);
        final success = await momentProvider.uploadFile(
          file: file,
          mediaType: MediaType.image,
        );
        if (success) {
          setState(() {
            _mediaFile = file;
            _selectedMediaType = MediaType.image;
            _isLoading = false;
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('上传图片失败')),
          );
        }
      logger.e('选择图片失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败')),
      );
        _isLoading = false;
  /// 选择视频
  Future<void> _pickVideo(ImageSource source) async {
      final pickedFile = await picker.pickVideo(source: source);
          mediaType: MediaType.video,
            _selectedMediaType = MediaType.video;
            const SnackBar(content: Text('上传视频失败')),
      logger.e('选择视频失败: $e');
        const SnackBar(content: Text('选择视频失败')),
  /// 显示媒体选择底部弹窗
  void _showMediaPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择图片'),
                  _pickImage(ImageSource.gallery);
                leading: const Icon(Icons.videocam),
                title: const Text('录制视频'),
                  _pickVideo(ImageSource.camera);
                leading: const Icon(Icons.video_library),
                title: const Text('从相册选择视频'),
                  _pickVideo(ImageSource.gallery);
            ],
          ),
      },
    );
  /// 发布或更新瞬间
  Future<void> _publishMoment() async {
    // 验证内容
    if (_contentController.text.trim().isEmpty) {
        const SnackBar(content: Text('请输入内容')),
      return;
      _isLoading = true;
      // 总是创建新瞬间
      bool success = await momentProvider.createMoment(
        content: _contentController.text.trim(),
        mediaType: _selectedMediaType,
        location: _location,
        weather: _weather,
        mood: _selectedMood,
      if (success) {
        // 发布/更新成功，显示成功提示
        if (mounted) {
            SnackBar(
              content: Text(_todayMoment != null ? '瞬间更新成功！' : '瞬间发布成功！'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          // 等待提示显示后再返回
          await Future.delayed(const Duration(seconds: 1));
          // 返回上一页
          if (mounted) {
            Navigator.pop(context, true);
          }
        // 发布/更新失败
            SnackBar(content: Text(_todayMoment != null ? '更新失败，请重试' : '发布失败，请重试')),
      logger.e('${_todayMoment != null ? '更新' : '发布'}瞬间失败: $e');
        SnackBar(content: Text('${_todayMoment != null ? '更新' : '发布'}失败，请重试')),
  Widget build(BuildContext context) {
    if (_isCheckingTodayMoment) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('记录瞬间'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
    return Scaffold(
      appBar: AppBar(
        title: const Text('记录瞬间'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _publishMoment,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('发布'),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 内容输入
            TextField(
              controller: _contentController,
              maxLines: 5,
              maxLength: 500,
              decoration: const InputDecoration(
                hintText: '记录这一刻的想法...',
                border: OutlineInputBorder(),
            const SizedBox(height: 16),
            // 媒体预览
            if (_mediaFile != null) ...[
              _selectedMediaType == MediaType.image
                  ? Image.file(
                      _mediaFile!,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    )
                  : Container(
                      color: Colors.black,
                      child: const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white,
                          size: 48,
                        ),
                      ),
                    ),
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _mediaFile = null;
                    _selectedMediaType = MediaType.none;
                  });
                icon: const Icon(Icons.delete),
                label: const Text('删除'),
              const SizedBox(height: 16),
            // 添加媒体按钮
            if (_mediaFile == null)
              OutlinedButton.icon(
                onPressed: _showMediaPicker,
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('添加图片/视频'),
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 48),
                ),
            const SizedBox(height: 24),
            // 位置和天气信息
            Row(
              children: [
                const Icon(Icons.location_on, size: 16),
                const SizedBox(width: 4),
                _isLocationLoading
                    ? const Text('正在获取位置...')
                    : Text(_location),
                const Spacer(),
                const Icon(Icons.wb_sunny, size: 16),
                Text(_weather),
              ],
            // 心情选择
            const Text(
              '选择心情',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
            const SizedBox(height: 8),
            Wrap(
              spacing: 12,
                _buildMoodChip(MoodType.happy, '开心', Icons.sentiment_very_satisfied),
                _buildMoodChip(MoodType.calm, '平静', Icons.sentiment_satisfied),
                _buildMoodChip(MoodType.sad, '难过', Icons.sentiment_dissatisfied),
                _buildMoodChip(MoodType.excited, '兴奋', Icons.mood),
                _buildMoodChip(MoodType.anxious, '焦虑', Icons.mood_bad),
                _buildMoodChip(MoodType.neutral, '一般', Icons.sentiment_neutral),
          ],
  /// 构建心情选择芯片
  Widget _buildMoodChip(MoodType mood, String label, IconData icon) {
    final isSelected = _selectedMood == mood;
    return FilterChip(
      selected: isSelected,
      label: Text(label),
      avatar: Icon(
        icon,
        color: isSelected ? Colors.white : null,
      onSelected: (selected) {
          _selectedMood = mood;

import 'package:one_moment_app/core/network/ai_service.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
/// AI设置页面
class AISettingsPage extends StatefulWidget {
  const AISettingsPage({super.key});
  @override
  State<AISettingsPage> createState() => _AISettingsPageState();
}
class _AISettingsPageState extends State<AISettingsPage> {
  final LoggerUtil _logger = LoggerUtil();
  final AIService _aiService = AIService();
  
  bool _isLoading = true;
  String _errorMessage = '';
  late AIModelConfig _currentConfig;
  List<AIModelConfig> _availableModels = [];
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _apiEndpointController = TextEditingController();
  void initState() {
    super.initState();
    _loadSettings();
  }
  void dispose() {
    _apiKeyController.dispose();
    _apiEndpointController.dispose();
    super.dispose();
  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      
      // 加载当前配置
      _currentConfig = await _aiService.getCurrentModelConfig();
      _apiKeyController.text = _currentConfig.apiKey ?? '';
      _apiEndpointController.text = _currentConfig.apiEndpoint ?? '';
      // 加载可用模型
      _availableModels = await _aiService.getAllModelConfigs();
        _isLoading = false;
    } catch (e) {
      _logger.e('加载AI设置失败', e);
        _errorMessage = '加载设置失败: $e';
    }
  /// 保存设置
  Future<void> _saveSettings() async {
      // 更新配置
      final updatedConfig = AIModelConfig(
        modelName: _currentConfig.modelName,
        provider: _currentConfig.provider,
        apiKey: _apiKeyController.text,
        apiEndpoint: _apiEndpointController.text,
      );
      // 保存配置
      final success = await _aiService.saveModelConfig(updatedConfig);
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('设置已保存'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = '保存设置失败';
        });
      }
      _logger.e('保存AI设置失败', e);
        _errorMessage = '保存设置失败: $e';
  /// 选择模型
  void _selectModel(AIModelConfig model) {
    setState(() {
      _currentConfig = model;
      _apiEndpointController.text = model.apiEndpoint ?? '';
    });
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI设置'),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSettings,
              tooltip: '保存',
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  /// 构建页面主体
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8.0),
              margin: const EdgeInsets.only(bottom: 16.0),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
          
          // 当前模型
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                  Text(
                    '当前AI模型',
                    style: Theme.of(context).textTheme.titleMedium,
                  const SizedBox(height: 16.0),
                  ListTile(
                    title: Text(_currentConfig.provider),
                    subtitle: Text(_currentConfig.modelName),
                    leading: const Icon(Icons.smart_toy),
                    trailing: const Icon(Icons.check_circle, color: Colors.green),
          ),
          const SizedBox(height: 16.0),
          // API密钥
                    'API配置',
                  TextFormField(
                    controller: _apiKeyController,
                    decoration: const InputDecoration(
                      labelText: 'API密钥',
                      hintText: '输入您的API密钥',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.key),
                    obscureText: true,
                    controller: _apiEndpointController,
                      labelText: 'API端点',
                      hintText: '输入API端点URL',
                      prefixIcon: Icon(Icons.link),
          // 可用模型
                    '可用AI模型',
                  ..._availableModels.map((model) => ListTile(
                    title: Text(model.provider),
                    subtitle: Text(model.modelName),
                    trailing: _currentConfig.provider == model.provider && 
                              _currentConfig.modelName == model.modelName
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.circle_outlined),
                    onTap: () => _selectModel(model),
                  )),
          const SizedBox(height: 32.0),
          // 保存按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: const Text('保存设置'),

import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/utils/mood_number_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';
/// 时间胶囊日历页面
class TimeCapsuleCalendarPage extends StatefulWidget {
  const TimeCapsuleCalendarPage({Key? key}) : super(key: key);
  @override
  State<TimeCapsuleCalendarPage> createState() => _TimeCapsuleCalendarPageState();
}
class _TimeCapsuleCalendarPageState extends State<TimeCapsuleCalendarPage> {
  final LoggerUtil _logger = LoggerUtil();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<MomentModel> _selectedDayMoments = [];
  Map<DateTime, List<MomentModel>> _monthlyMoments = {};
  bool _isLoading = false;
  // 心情动画相关状态
  bool _showMoodAnimation = false;
  double _moodAnimationOpacity = 0.0;
  String _selectedDayMoodIcon = '';
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _loadMonthlyMoments();
    _loadMomentsForSelectedDay();
  }
  /// 加载当月的所有moment数据
  Future<void> _loadMonthlyMoments() async {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);
      // 获取当月第一天和最后一天
      final firstDay = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final lastDay = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
      // 清空当前月度数据
      _monthlyMoments.clear();
      // 逐日加载数据
      for (int day = 1; day <= lastDay.day; day++) {
        final date = DateTime(_focusedDay.year, _focusedDay.month, day);
        try {
          final moments = await momentProvider.fetchMomentsByDate(date);
          if (moments.isNotEmpty) {
            _monthlyMoments[DateTime(date.year, date.month, date.day)] = moments;
          }
        } catch (e) {
          _logger.e('加载日期 $date 的moment失败: $e');
        }
      }
      if (mounted) {
        setState(() {});
    } catch (e) {
      _logger.e('加载月度moment数据失败: $e');
    }
  /// 加载选定日期的时刻
  Future<void> _loadMomentsForSelectedDay() async {
    if (_selectedDay == null) return;
    setState(() {
      _isLoading = true;
    });
      _logger.i('加载选定日期的时刻: ${_selectedDay.toString()}');
      // 使用MomentProvider获取指定日期的时刻数据
      // 使用Future.delayed避免在build过程中调用setState
      Future.delayed(Duration.zero, () async {
          final moments = await momentProvider.fetchMomentsByDate(_selectedDay!);
          _logger.i('获取到 ${moments.length} 个时刻');
          if (mounted) {
            setState(() {
              _selectedDayMoments = moments;
              _isLoading = false;
            });
          _logger.e('加载时刻失败: $e');
              _selectedDayMoments = [];
      });
      _logger.e('加载时刻失败: $e');
      setState(() {
        _selectedDayMoments = [];
        _isLoading = false;
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 直接展示日历，去掉Banner
          _buildCalendar(),
          // 心情动画显示区域
          if (_showMoodAnimation)
            _buildMoodAnimation(),
          // 时刻列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _selectedDayMoments.isEmpty
                    ? _buildEmptyState()
                    : _buildMomentsList(),
          ),
        ],
      ),
    );
  /// 构建Banner
  Widget _buildBanner() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.15,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.7),
          ],
        ),
      child: SafeArea(
        child: Center(
          child: Text(
            '时间胶囊',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
  /// 构建心情动画
  Widget _buildMoodAnimation() {
      height: 120,
      child: Center(
        child: AnimatedOpacity(
          opacity: _moodAnimationOpacity,
          duration: const Duration(milliseconds: 800),
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.black.withOpacity(0.1),
            child: Center(
              child: Text(
                _selectedDayMoodIcon,
                style: const TextStyle(fontSize: 60),
              ),
  /// 显示心情动画
  void _showMoodAnimationForDay(DateTime day) {
    final moments = _monthlyMoments[DateTime(day.year, day.month, day.day)] ?? [];
    if (moments.isNotEmpty) {
      // 获取最新moment的心情
      final latestMoment = moments.reduce((a, b) =>
        a.createdAt.isAfter(b.createdAt) ? a : b);
        _showMoodAnimation = true;
        _moodAnimationOpacity = 1.0;
        _selectedDayMoodIcon = _getMoodEmoji(latestMoment.mood);
      // 2秒后开始淡出
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _moodAnimationOpacity = 0.0;
          });
      // 3秒后隐藏动画容器
      Future.delayed(const Duration(seconds: 3), () {
            _showMoodAnimation = false;
  /// 获取心情对应的emoji
  String _getMoodEmoji(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return '😊';
      case MoodType.excited:
        return '🎉';
      case MoodType.calm:
        return '😌';
      case MoodType.sad:
        return '😢';
      case MoodType.angry:
        return '😠';
      case MoodType.anxious:
        return '😰';
      case MoodType.grateful:
        return '🙏';
      case MoodType.neutral:
      default:
        return '😐';
  /// 构建日历
  Widget _buildCalendar() {
      padding: const EdgeInsets.all(16),
      child: TableCalendar(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        calendarFormat: _calendarFormat,
        availableCalendarFormats: const {
          CalendarFormat.month: '月',
          CalendarFormat.twoWeeks: '2周',
          CalendarFormat.week: '周',
        },
        startingDayOfWeek: StartingDayOfWeek.monday,
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        onDaySelected: (selectedDay, focusedDay) {
          if (!isSameDay(_selectedDay, selectedDay)) {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
            _loadMomentsForSelectedDay();
            // 显示心情动画
            _showMoodAnimationForDay(selectedDay);
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
              _calendarFormat = format;
        onPageChanged: (focusedDay) {
            _focusedDay = focusedDay;
          _loadMonthlyMoments();
        calendarBuilders: CalendarBuilders(
          defaultBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, false, false);
          },
          todayBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, true, false);
          selectedBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, false, true);
        calendarStyle: CalendarStyle(
          todayDecoration: BoxDecoration(
            color: Colors.transparent,
          selectedDecoration: BoxDecoration(
          defaultDecoration: BoxDecoration(
          markerDecoration: BoxDecoration(
        headerStyle: HeaderStyle(
          formatButtonTextStyle: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 14.0,
          formatButtonDecoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.primary),
            borderRadius: BorderRadius.circular(16.0),
  /// 构建自定义日历日期
  Widget _buildCalendarDay(DateTime day, bool isToday, bool isSelected) {
    final dateKey = DateTime(day.year, day.month, day.day);
    final moments = _monthlyMoments[dateKey] ?? [];
      // 如果有多个moment，使用最新的一个的情绪
      return MoodNumberUtil.buildMoodNumber(
        number: day.day,
        mood: latestMoment.mood,
        isSelected: isSelected,
        isToday: isToday,
        onTap: () {
            _selectedDay = day;
            _focusedDay = day;
          _loadMomentsForSelectedDay();
      );
    } else {
      // 没有moment的日期使用普通数字
      return MoodNumberUtil.buildNormalNumber(
  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
          Icon(
            Icons.event_note,
            size: 64,
            color: Colors.grey[400],
          const SizedBox(height: 16),
          Text(
            _selectedDay != null
                ? '${DateFormat('yyyy年MM月dd日').format(_selectedDay!)}没有记录'
                : '没有记录',
              fontSize: 16,
              color: Colors.grey[600],
          const SizedBox(height: 8),
          const Text(
            '选择其他日期查看更多时刻',
              fontSize: 14,
              color: Colors.grey,
  /// 构建时刻列表
  Widget _buildMomentsList() {
        crossAxisAlignment: CrossAxisAlignment.start,
            child: ListView.builder(
              itemCount: _selectedDayMoments.length,
              itemBuilder: (context, index) {
                final moment = _selectedDayMoments[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 时间和心情
                        Row(
                          children: [
                            Icon(
                              _getMoodIcon(moment.mood.toString().split('.').last),
                              size: 20,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('HH:mm').format(moment.createdAt),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            const Spacer(),
                            if (moment.locationName != null) ...[
                              Icon(
                                Icons.location_on,
                                size: 16,
                                color: Colors.grey[500],
                              const SizedBox(width: 4),
                              Text(
                                moment.locationName!,
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 12),
                        // 内容
                        Text(
                          moment.content,
                          style: Theme.of(context).textTheme.bodyLarge,
                        // 图片（如果有）
                        if (moment.contentType == 'image' && moment.mediaObjectKey != null) ...[
                          const SizedBox(height: 12),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: FutureBuilder<String?>(
                              future: _getImageUrl(moment.mediaObjectKey!),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Container(
                                    height: 200,
                                    width: double.infinity,
                                    color: Colors.grey.shade200,
                                    child: const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }
                                if (snapshot.hasError || !snapshot.hasData) {
                                    child: Center(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.image_not_supported,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            '图片加载失败',
                                            style: TextStyle(color: Colors.grey),
                                        ],
                                      ),
                                return Image.network(
                                  snapshot.data!,
                                  height: 200,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      height: 200,
                                      width: double.infinity,
                                      color: Colors.grey.shade200,
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.image_not_supported,
                                              size: 48,
                                              color: Colors.grey,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              '图片加载失败',
                                              style: TextStyle(color: Colors.grey),
                                          ],
                                        ),
                                    );
                                  },
                                );
                              },
                          ),
                        ],
                        // 天气信息（如果有）
                        if (moment.weather != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                                Icons.wb_sunny,
                                color: Colors.orange,
                                moment.weather!,
                                  color: Colors.grey[600],
                              if (moment.temperature != null) ...[
                                const SizedBox(width: 8),
                                Text(
                                  '${moment.temperature!.toInt()}°C',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                              ],
                      ],
                    ),
                );
              },
  /// 获取心情图标
  IconData _getMoodIcon(String mood) {
      case 'happy':
        return Icons.sentiment_very_satisfied;
      case 'calm':
        return Icons.sentiment_satisfied;
      case 'sad':
        return Icons.sentiment_dissatisfied;
      case 'excited':
        return Icons.mood;
      case 'anxious':
        return Icons.mood_bad;
      case 'neutral':
        return Icons.sentiment_neutral;
  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      return null;
      _logger.e('获取图片URL失败: $e');

import 'package:provider/provider.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/presentation/pages/moment_edit_page.dart';
/// 瞬间详情页面
class MomentDetailPage extends StatefulWidget {
  final String momentId;
  const MomentDetailPage({
    super.key,
    required this.momentId,
  });
  @override
  State<MomentDetailPage> createState() => _MomentDetailPageState();
}
class _MomentDetailPageState extends State<MomentDetailPage> {
  final LoggerUtil _logger = LoggerUtil();
  late final MomentService _momentService;
  bool _isLoading = true;
  bool _isDeleting = false;
  MomentModel? _moment;
  String _errorMessage = '';
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _loadMoment();
  }
  /// 加载瞬间
  Future<void> _loadMoment() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      final moment = await _momentService.getMomentById(widget.momentId);
        _moment = moment;
        _isLoading = false;
    } catch (e) {
      _logger.e('加载瞬间失败', e);
        _errorMessage = '加载瞬间失败: $e';
    }
  /// 删除瞬间
  Future<void> _deleteMoment() async {
        _isDeleting = true;
      await _momentService.deleteMoment(widget.momentId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('瞬间已删除'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // 返回true表示已删除
      }
      _logger.e('删除瞬间失败', e);
        _isDeleting = false;
          SnackBar(
            content: Text('删除瞬间失败: $e'),
            backgroundColor: Colors.red,
  /// 编辑瞬间
  Future<void> _editMoment() async {
    if (_moment == null) return;
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => MomentEditPage(moment: _moment!),
      ),
    );
    if (result == true) {
      _loadMoment();
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('瞬间详情'),
        actions: [
          if (_moment != null && !_isLoading && !_isDeleting)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _editMoment,
              tooltip: '编辑',
            ),
              icon: const Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmDialog();
              },
              tooltip: '删除',
        ],
      body: _buildBody(),
  /// 构建页面主体
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ElevatedButton(
              onPressed: _loadMoment,
              child: const Text('重试'),
          ],
        ),
    if (_moment == null) {
        child: Text('瞬间不存在'),
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期和时间
          Text(
            DateFormat('yyyy年MM月dd日 HH:mm').format(_moment!.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                ),
          const SizedBox(height: 16),
          // 内容
            _moment!.content,
            style: Theme.of(context).textTheme.bodyLarge,
          // 媒体内容
          if (_moment!.mediaType != MediaType.none && _moment!.mediaPath != null)
            _buildMediaContent(),
          // 位置和天气
          if (_moment!.location != null || _moment!.weather != null)
            Row(
              children: [
                if (_moment!.location != null)
                  Chip(
                    avatar: const Icon(Icons.location_on, size: 16),
                    label: Text(_moment!.location!),
                  ),
                const SizedBox(width: 8),
                if (_moment!.weather != null)
                    avatar: const Icon(Icons.cloud, size: 16),
                    label: Text(_moment!.weather!),
              ],
          // 心情
          _buildMoodChip(_getMoodString(_moment!.mood)),
          // 私密标记
          if (_moment!.isPrivate)
            const Chip(
              avatar: Icon(Icons.lock, size: 16),
              label: Text('私密'),
  /// 构建媒体内容
  Widget _buildMediaContent() {
    switch (_moment!.mediaType) {
      case MediaType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: FadeInImage.assetNetwork(
            placeholder: 'assets/images/placeholder.png',
            image: _moment!.mediaPath!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: 200,
            imageErrorBuilder: (context, error, stackTrace) {
              _logger.e('加载图片失败: $error');
              return Container(
                width: double.infinity,
                height: 200,
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(
                    Icons.broken_image,
                    size: 64,
                    color: Colors.grey,
              );
            },
      case MediaType.video:
        return Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          child: const Center(
            child: Icon(
              Icons.play_circle_filled,
              color: Colors.grey,
      case MediaType.audio:
          height: 80,
              Icons.audiotrack,
              size: 48,
      case MediaType.none:
      default:
        return const SizedBox.shrink();
  /// 构建心情标签
  Widget _buildMoodChip(String mood) {
    IconData iconData;
    Color color;
    switch (mood) {
      case '开心':
        iconData = Icons.sentiment_very_satisfied;
        color = Colors.amber;
        break;
      case '平静':
        iconData = Icons.sentiment_satisfied;
        color = Colors.green;
      case '难过':
        iconData = Icons.sentiment_dissatisfied;
        color = Colors.blue;
      case '兴奋':
        iconData = Icons.mood;
        color = Colors.orange;
      case '焦虑':
        iconData = Icons.mood_bad;
        color = Colors.purple;
      case '中性':
        iconData = Icons.sentiment_neutral;
        color = Colors.grey;
    return Chip(
      avatar: Icon(iconData, color: color, size: 18),
      label: Text(mood),
      backgroundColor: color.withOpacity(0.1),
  /// 获取心情字符串
  String _getMoodString(MoodType mood) {
      case MoodType.happy:
        return '开心';
      case MoodType.calm:
        return '平静';
      case MoodType.sad:
        return '难过';
      case MoodType.excited:
        return '兴奋';
      case MoodType.anxious:
        return '焦虑';
      case MoodType.neutral:
        return '中性';
  /// 显示删除确认对话框
  Future<void> _showDeleteConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条瞬间吗？此操作不可撤销。'),
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              '删除',
              style: TextStyle(color: Colors.red),
      await _deleteMoment();

import 'package:provider/provider.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';
/// Agent列表页面
class AgentListPage extends StatefulWidget {
  const AgentListPage({Key? key}) : super(key: key);
  @override
  State<AgentListPage> createState() => _AgentListPageState();
}
class _AgentListPageState extends State<AgentListPage> {
  void initState() {
    super.initState();
    _loadData();
  }
  /// 加载数据
  Future<void> _loadData() async {
    final provider = context.read<AgentProvider>();
    await provider.fetchAgentConfigs();
    await provider.fetchUserChatSessions(1); // 假设用户ID为1
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('内心对话'),
      ),
      body: Consumer<AgentProvider>(
        builder: (context, provider, child) {
          final status = provider.status;
          final agents = provider.agentConfigs;
          final sessions = provider.chatSessions;
          final errorMessage = provider.errorMessage;
          if (status == AgentStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }
          if (status == AgentStatus.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    errorMessage ?? '加载失败',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('重试'),
                ],
              ),
            );
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '选择AI助手',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                _buildAgentGrid(agents),
                const SizedBox(height: 24),
                  '最近对话',
                _buildSessionList(sessions),
              ],
            ),
          );
        },
    );
  /// 构建Agent网格
  Widget _buildAgentGrid(List<AgentConfigModel> agents) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      itemCount: agents.length,
      itemBuilder: (context, index) {
        final agent = agents[index];
        return _buildAgentCard(agent);
      },
  /// 构建Agent卡片
  Widget _buildAgentCard(AgentConfigModel agent) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      child: InkWell(
        onTap: () {
          // 跳转到聊天页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AgentChatPage(
                agentId: agent.id,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 32,
                backgroundColor: Colors.blue.withOpacity(0.2),
                child: agent.avatar != null
                    ? Image.asset(
                        agent.avatar!,
                        width: 48,
                        height: 48,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.smart_toy,
                            size: 32,
                            color: Colors.blue,
                          );
                        },
                      )
                    : const Icon(
                        Icons.smart_toy,
                        size: 32,
                        color: Colors.blue,
                      ),
              const SizedBox(height: 16),
              Text(
                agent.name,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              const SizedBox(height: 8),
                agent.description ?? '',
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
            ],
          ),
        ),
  /// 构建会话列表
  Widget _buildSessionList(List<AgentChatSessionModel> sessions) {
    if (sessions.isEmpty) {
      return const Center(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无对话记录'),
      );
    }
    return ListView.builder(
      itemCount: sessions.length,
        final session = sessions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withOpacity(0.2),
              child: session.agent?.avatar != null
                  ? Image.asset(
                      session.agent!.avatar!,
                      width: 24,
                      height: 24,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.smart_toy,
                          size: 24,
                          color: Colors.blue,
                        );
                      },
                    )
                  : const Icon(
                      Icons.smart_toy,
                      size: 24,
                      color: Colors.blue,
            title: Text(session.title),
            subtitle: Text(
              '上次对话: ${session.updatedAt.year}/${session.updatedAt.month}/${session.updatedAt.day} ${session.updatedAt.hour}:${session.updatedAt.minute}',
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // 跳转到聊天页面
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AgentChatPage(
                    sessionId: session.id,
              );
            },
        );

import 'package:flutter_markdown/flutter_markdown.dart';
import 'dart:convert';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/network/ai_service.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
/// AI聊天页面
class AIChatPage extends StatefulWidget {
  const AIChatPage({super.key});
  @override
  State<AIChatPage> createState() => _AIChatPageState();
}
class _AIChatPageState extends State<AIChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AIService _aiService = AIService();
  ChatSession? _currentSession;
  bool _isLoading = false;
  void initState() {
    super.initState();
    _createNewSession();
  }
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  /// 创建新会话
  Future<void> _createNewSession() async {
    try {
      setState(() {
        _isLoading = true;
      });
      // 创建新会话
      final session = ChatSession(
        id: const Uuid().v4(),
        title: '新对话',
        messages: [
          ChatMessage(
            id: const Uuid().v4(),
            role: MessageRole.system,
            content: '你是一个友好的AI助手，可以帮助用户记录生活中的美好瞬间，提供情感支持和建议。',
            timestamp: DateTime.now(),
          ),
            role: MessageRole.assistant,
            content: '你好！我是你的AI助手，很高兴能陪伴你记录生活中的美好瞬间。有什么我可以帮助你的吗？',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        modelConfig: AIModelConfig(
          modelName: '通义千问',
          provider: '通义千问',
          apiKey: 'YOUR_API_KEY', // 实际应用中应从安全存储中获取
          apiEndpoint: null,
        ),
      );
      // 保存会话
      await dbService.insertChatSession(session);
        _currentSession = session;
        _isLoading = false;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建会话失败: $e')),
        );
      }
    }
  /// 发送消息
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _currentSession == null) return;
    // 清空输入框
    _messageController.clear();
    // 创建用户消息
    final userMessage = ChatMessage(
      id: const Uuid().v4(),
      role: MessageRole.user,
      content: message,
      timestamp: DateTime.now(),
    );
    // 更新会话
    setState(() {
      _currentSession = _currentSession!.addMessage(userMessage);
      _isLoading = true;
    });
    // 滚动到底部
    _scrollToBottom();
      // 发送消息到AI服务
      final response = await _aiService.sendMessage(
        messages: _currentSession!.messages,
        modelConfig: _currentSession!.modelConfig,
      // 创建AI回复消息
      final aiMessage = ChatMessage(
        role: MessageRole.assistant,
        content: response,
        timestamp: DateTime.now(),
      // 更新会话
        _currentSession = _currentSession!.addMessage(aiMessage);
      await dbService.insertChatSession(_currentSession!);
      // 滚动到底部
      _scrollToBottom();
          SnackBar(content: Text('发送消息失败: $e')),
  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI聊天'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'new') {
                _createNewSession();
              } else if (value == 'settings') {
                // 打开设置
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('AI设置功能即将上线')),
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new',
                child: Text('新对话'),
              ),
                value: 'settings',
                child: Text('AI设置'),
            ],
      ),
      body: _isLoading && _currentSession == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // AI模型信息
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  color: Theme.of(context).colorScheme.primaryContainer,
                  child: Row(
                    children: [
                      const Icon(Icons.smart_toy),
                      const SizedBox(width: 8),
                      Text(
                        '当前模型: ${_currentSession?.modelConfig.provider ?? '未知'} - ${_currentSession?.modelConfig.modelName ?? '未知'}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                ),
                // 聊天消息列表
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _currentSession?.messages.length ?? 0,
                    itemBuilder: (context, index) {
                      final message = _currentSession!.messages[index];
                      // 跳过系统消息
                      if (message.role == MessageRole.system) {
                        return const SizedBox.shrink();
                      }
                      return _ChatMessageBubble(message: message);
                    },
                // 加载指示器
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: LinearProgressIndicator(),
                // 输入框
                Padding(
                  padding: const EdgeInsets.all(8.0),
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: const InputDecoration(
                            hintText: '输入消息...',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: null,
                          textInputAction: TextInputAction.send,
                          onSubmitted: (_) => _sendMessage(),
                        ),
                      IconButton(
                        icon: const Icon(Icons.send),
                        onPressed: _isLoading ? null : _sendMessage,
                // 免责声明
                  padding: const EdgeInsets.all(8),
                  color: Colors.grey[200],
                  child: const Text(
                    '免责声明：AI生成的内容仅供参考，不代表本应用观点。',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                    textAlign: TextAlign.center,
              ],
            ),
/// 聊天消息气泡
class _ChatMessageBubble extends StatelessWidget {
  final ChatMessage message;
  const _ChatMessageBubble({required this.message});
    final isUser = message.role == MessageRole.user;
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser)
            CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(Icons.smart_toy, color: Colors.white),
          if (!isUser) const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(16),
              child: _buildMessageContent(message, isUser, context),
          if (isUser) const SizedBox(width: 8),
          if (isUser)
              child: const Icon(Icons.person, color: Colors.white),
  /// 构建消息内容，支持JSON解析和Markdown渲染
  Widget _buildMessageContent(ChatMessage message, bool isUser, BuildContext context) {
    String content = message.content;
    // 尝试解析JSON
    if (!isUser && content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        final jsonData = json.decode(content);
        if (jsonData is Map<String, dynamic>) {
          // 提取实际的消息内容
          if (jsonData.containsKey('message')) {
            content = jsonData['message'].toString();
          } else if (jsonData.containsKey('content')) {
            content = jsonData['content'].toString();
          } else if (jsonData.containsKey('text')) {
            content = jsonData['text'].toString();
          } else if (jsonData.containsKey('response')) {
            content = jsonData['response'].toString();
          } else {
            // 如果没有找到标准字段，格式化显示JSON
            content = _formatJsonForDisplay(jsonData);
          }
        }
      } catch (e) {
        // JSON解析失败，保持原内容
    // 对于AI消息，使用Markdown渲染
    if (!isUser) {
      return MarkdownBody(
        data: content,
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontSize: 14,
          h1: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          h2: TextStyle(
            fontSize: 16,
          h3: TextStyle(
          code: TextStyle(
            backgroundColor: Theme.of(context).colorScheme.surface,
            color: Theme.of(context).colorScheme.onSurface,
            fontFamily: 'monospace',
          codeblockDecoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(4),
    } else {
      // 用户消息使用普通文本
      return Text(
        content,
        style: TextStyle(
          color: isUser ? Colors.white : null,
  /// 格式化JSON用于显示
  String _formatJsonForDisplay(Map<String, dynamic> jsonData) {
    final buffer = StringBuffer();
    jsonData.forEach((key, value) {
      buffer.writeln('**$key**: $value');
    return buffer.toString();

/// 社区页面
class CommunityPage extends StatefulWidget {
  final Map<String, dynamic> community;
  const CommunityPage({
    super.key,
    required this.community,
  });
  @override
  State<CommunityPage> createState() => _CommunityPageState();
}
class _CommunityPageState extends State<CommunityPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<Map<String, dynamic>> _posts = [];
  bool _isLoading = false;
  void initState() {
    super.initState();
    
    // 加载帖子
    _loadPosts();
  }
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  /// 加载帖子
  void _loadPosts() {
    setState(() {
      _isLoading = true;
    });
    // 模拟加载延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        // 使用社区中的帖子数据
        _posts.clear();
        _posts.addAll(List<Map<String, dynamic>>.from(widget.community['posts'] ?? []));
        _isLoading = false;
      });
  /// 发送消息
  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;
    // 清空输入框
    _messageController.clear();
    // 添加新帖子
      _posts.insert(0, {
        'id': 'p${DateTime.now().millisecondsSinceEpoch}',
        'content': message,
        'author': '我',
        'time': '刚刚',
        'likes': 0,
        'comments': 0,
    // 滚动到顶部
    _scrollToTop();
  /// 滚动到顶部
  void _scrollToTop() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.community['name'] ?? '社区'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // 显示社区信息
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(widget.community['name'] ?? ''),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.community['description'] ?? ''),
                      const SizedBox(height: 16),
                      Text('成员: ${widget.community['memberCount'] ?? 0}'),
                      Text('帖子: ${widget.community['postCount'] ?? 0}'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('关闭'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 社区信息
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
              ],
            ),
            child: Row(
              children: [
                // 社区图标
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  child: Image.asset(
                    widget.community['icon'] ?? 'assets/images/communities/default.png',
                    width: 32,
                    height: 32,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.group,
                        size: 32,
                        color: Theme.of(context).colorScheme.primary,
                      );
                    },
                const SizedBox(width: 16),
                
                // 社区名称和描述
                Expanded(
                  child: Column(
                      Text(
                        widget.community['name'] ?? '未知社区',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                        widget.community['description'] ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey,
                            ),
          
          // 帖子列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _posts.isEmpty
                    ? const Center(child: Text('暂无帖子'))
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        itemCount: _posts.length,
                        itemBuilder: (context, index) {
                          return _buildPostCard(_posts[index]);
                        },
          // 输入框
          Padding(
            padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: '分享你的想法...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 12.0,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                const SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: _sendMessage,
                  mini: true,
                  child: const Icon(Icons.send),
    );
  /// 构建帖子卡片
  Widget _buildPostCard(Map<String, dynamic> post) {
    final isMyPost = post['author'] == '我';
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 作者信息
            Row(
                // 作者头像
                  radius: 16,
                  backgroundColor: isMyPost
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey,
                  child: const Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.white,
                // 作者名称和时间
                        post['author'] ?? '匿名',
                        style: Theme.of(context).textTheme.titleSmall,
                        post['time'] ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
            const SizedBox(height: 16),
            
            // 帖子内容
            Text(
              post['content'] ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
            // 点赞和评论
                // 点赞按钮
                InkWell(
                  onTap: () {
                    // 点赞功能
                    setState(() {
                      post['likes'] = (post['likes'] ?? 0) + 1;
                    });
                  },
                  borderRadius: BorderRadius.circular(16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post['likes'] ?? 0}',
                          style: Theme.of(context).textTheme.bodySmall,
                      ],
                // 评论按钮
                    // 评论功能
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('评论功能即将上线')),
                    );
                        const Icon(
                          Icons.chat_bubble_outline,
                          color: Colors.grey,
                          '${post['comments'] ?? 0}',
          ],
        ),

import 'package:provider/provider.dart';
import 'package:one_moment_app/presentation/providers/auth_provider.dart';
/// 登录页面
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});
  @override
  State<LoginPage> createState() => _LoginPageState();
}
class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLogin = true; // true: 登录, false: 注册
  bool _obscurePassword = true;
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
  void _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    bool success = false;
    if (_isLogin) {
      success = await authProvider.loginWithPhone(
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
      );
    } else {
      success = await authProvider.register(
        nickname: '用户${_phoneController.text.substring(7)}',
    if (success && mounted) {
      Navigator.of(context).pop(true); // 返回true表示登录成功
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40),
                // 标题
                Text(
                  _isLogin ? '登录' : '注册',
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                  _isLogin ? '欢迎回来，开始记录美好瞬间' : '加入我们，记录生活点滴',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                const SizedBox(height: 48),
                // 手机号输入框
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    labelText: '手机号',
                    hintText: '请输入11位手机号',
                    prefixIcon: const Icon(Icons.phone),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入手机号';
                    }
                    if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                      return '请输入正确的手机号格式';
                    return null;
                  },
                const SizedBox(height: 16),
                // 密码输入框
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                    labelText: '密码',
                    hintText: '请输入密码',
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      return '请输入密码';
                    if (!_isLogin && value.length < 6) {
                      return '密码长度至少6位';
                const SizedBox(height: 24),
                // 登录/注册按钮
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _handleSubmit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6366F1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      child: authProvider.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              _isLogin ? '登录' : '注册',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                            ),
                    );
                // 切换登录/注册
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isLogin = !_isLogin;
                    });
                  child: Text(
                    _isLogin ? '还没有账号？立即注册' : '已有账号？立即登录',
                    style: const TextStyle(
                      color: Color(0xFF6366F1),
                      fontWeight: FontWeight.w500,
                // 测试账号提示
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                          const SizedBox(width: 8),
                          Text(
                            '测试账号',
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontWeight: FontWeight.w600,
                          ),
                        ],
                      const SizedBox(height: 8),
                      Text(
                        '手机号：18888888888\n密码：123456',
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 14,
                    ],
                const Spacer(),
                // 错误信息
                    if (authProvider.errorMessage != null) {
                      return Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline, color: Colors.red[600], size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                authProvider.errorMessage!,
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontSize: 14,
                                ),
                          ],
                      );
                    return const SizedBox.shrink();
              ],
            ),
          ),
    );

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';
/// 调试页面
/// 用于测试API连接和数据获取
class DebugPage extends StatefulWidget {
  const DebugPage({Key? key}) : super(key: key);
  @override
  State<DebugPage> createState() => _DebugPageState();
}
class _DebugPageState extends State<DebugPage> {
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();
  bool _isLoading = false;
  String _testResult = '';
  String _apiUrl = '';
  List<Map<String, dynamic>> _moments = [];
  bool _isLoggedIn = false;
  void initState() {
    super.initState();
    _apiUrl = _config.apiBaseUrl;
    _checkLoginStatus();
  }
  /// 检查登录状态
  void _checkLoginStatus() {
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    setState(() {
      _isLoggedIn = apiClient.isLoggedIn();
    });
  /// 测试API连接
  Future<void> _testApiConnection() async {
      _isLoading = true;
      _testResult = '正在测试API连接...';
    try {
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final result = await apiClient.testConnection();
      setState(() {
        _testResult = result ? 'API连接成功' : 'API连接失败';
      });
    } catch (e) {
        _testResult = '测试过程中发生错误: $e';
      _logger.e('测试API连接失败', e);
    } finally {
        _isLoading = false;
    }
  /// 获取瞬间列表
  Future<void> _getMoments() async {
      _moments = [];
      final momentService = MomentService(apiClient: apiClient);
      final moments = await momentService.getAllMoments();
        _moments = moments.map((m) => {
          'id': m.id,
          'content': m.content,
          'createdAt': m.createdAt.toString(),
        }).toList();
      _logger.i('成功获取${moments.length}个瞬间');
      _logger.e('获取瞬间失败', e);
      // 显示错误信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('获取瞬间失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
  /// 更新API地址
  void _updateApiUrl() async {
      apiClient.updateBaseUrl(_apiUrl);
      // 测试新的连接
      await _testApiConnection();
      _logger.e('更新API地址失败', e);
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API调试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 环境信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('环境: ${_config.environmentName}', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Text('平台: ${_config.platform}', style: Theme.of(context).textTheme.bodyMedium),
                    Text('API地址: ${_config.apiBaseUrl}', style: Theme.of(context).textTheme.bodyMedium),
                    Text(
                      '登录功能已禁用',
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // API地址输入
                    Text('修改API地址', style: Theme.of(context).textTheme.titleMedium),
                    TextField(
                      controller: TextEditingController(text: _apiUrl),
                      decoration: const InputDecoration(
                        labelText: 'API地址',
                        hintText: 'http://localhost:8080/api',
                        border: OutlineInputBorder(),
                      onChanged: (value) {
                        setState(() {
                          _apiUrl = value;
                        });
                      },
                    ElevatedButton(
                      onPressed: _isLoading ? null : _updateApiUrl,
                      child: const Text('更新API地址'),
            // 测试结果
                    Text('API连接测试', style: Theme.of(context).textTheme.titleMedium),
                    Text(_testResult, style: Theme.of(context).textTheme.bodyMedium),
                      onPressed: _isLoading ? null : _testApiConnection,
                      child: const Text('测试API连接'),
            // 获取瞬间
                    Text('获取瞬间', style: Theme.of(context).textTheme.titleMedium),
                      onPressed: _isLoading ? null : _getMoments,
                      child: const Text('获取瞬间列表'),
                    if (_isLoading)
                      const Center(child: CircularProgressIndicator())
                    else if (_moments.isEmpty)
                      const Text('暂无数据')
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _moments.length,
                        itemBuilder: (context, index) {
                          final moment = _moments[index];
                          return ListTile(
                            title: Text(moment['content'] as String),
                            subtitle: Text(moment['createdAt'] as String),
                            leading: const Icon(Icons.photo),
                          );
                        },
          ],
    );

import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/services/tongyi_service.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
import 'package:one_moment_app/presentation/pages/agent_list_page.dart';
import 'package:one_moment_app/presentation/pages/time_capsule_calendar_page.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
/// 主页
class HomePage extends StatefulWidget {
  const HomePage({super.key});
  @override
  State<HomePage> createState() => _HomePageState();
}
class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  final GlobalKey<_InnerHeartContentState> _innerHeartKey = GlobalKey<_InnerHeartContentState>();
  // 底部导航栏项目
  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.camera_alt_outlined),
      activeIcon: Icon(Icons.camera_alt),
      label: 'Moment',
    ),
      icon: Icon(Icons.favorite_outline),
      activeIcon: Icon(Icons.favorite),
      label: '悦己',
      icon: Icon(Icons.history_outlined),
      activeIcon: Icon(Icons.history),
      label: '时间胶囊',
      icon: Icon(Icons.person_outline),
      activeIcon: Icon(Icons.person),
      label: '我',
  ];
  // 页面内容
  List<Widget> get _pages => [
    const _MomentContent(),
    _InnerHeartContent(key: _innerHeartKey),
    const TimeCapsuleCalendarPage(),
    const _ProfileContent(),
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: () => _showLogoDialog(),
          child: Text(
            _selectedIndex == 0 ? 'One Moment' :
            _selectedIndex == 1 ? '悦己' :
            _selectedIndex == 2 ? '时间胶囊' : '个人中心',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 0,
        // 为不同页面添加对应的按钮
        actions: _selectedIndex == 1 ? [
          // 悦己页面的新对话按钮
          IconButton(
            onPressed: () {
              _innerHeartKey.currentState?._startNewConversation();
            },
            icon: const Icon(Icons.refresh),
            tooltip: '新的对话',
        ] : null,
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        items: _bottomNavItems,
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          // 只有当切换到不同页面时才刷新数据
          if (_selectedIndex != index) {
            setState(() {
              _selectedIndex = index;
            });
            // 任务5：每次切换页面时触发数据刷新
            _refreshCurrentPage(index);
          }
        },
      floatingActionButton: _selectedIndex == 1 ? null : FloatingActionButton(
        onPressed: () {
          // 跳转到记录页面
          context.pushNamed('moment-create');
        tooltip: '添加记录',
        child: const Icon(Icons.add),
    );
  }
  /// 检查是否有今日瞬间
  bool _checkHasTodayMoment() {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);
      // 从已有的moments中查找今日瞬间，而不是重新请求API
      final allMoments = momentProvider.moments;
      final todayMoments = allMoments.where((moment) {
        final momentDate = DateTime(
          moment.createdAt.year,
          moment.createdAt.month,
          moment.createdAt.day,
        );
        return momentDate.isAtSameMomentAs(todayDate);
      }).toList();
      if (todayMoments.isNotEmpty) {
        _todayMoment = todayMoments.first;
        return true;
      }
      return false;
    } catch (e) {
    }
  MomentModel? _todayMoment;
  /// 显示logo弹窗
  void _showLogoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.favorite,
                color: Theme.of(context).colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 8),
              const Text('One Moment'),
            ],
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
              const Text(
                '记录生活中的每一个美好瞬间',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              const SizedBox(height: 16),
              const Text('版本: v1.0.0'),
              const SizedBox(height: 8),
              const Text('开发者: One Moment Team'),
              Text(
                '© ${DateTime.now().year} One Moment App',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
      },
  /// 刷新当前页面数据
  void _refreshCurrentPage(int index) {
    // 使用Future.microtask确保在当前构建周期完成后执行
    Future.microtask(() {
      if (!mounted) return;
      switch (index) {
        case 0: // Moment页面
          try {
            final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
            final momentProvider = Provider.of<MomentProvider>(context, listen: false);
            // 异步刷新，避免阻塞UI
            weatherProvider.fetchLatestWeather(cityName: '北京');
            momentProvider.fetchMoments();
          } catch (e) {
            print('刷新Moment页面数据失败: $e');
          break;
        case 1: // 悦己页面
          // 悦己页面不需要特殊刷新逻辑，聊天记录已经缓存
          print('切换到悦己页面');
        case 2: // 时间胶囊页面
            // 只刷新moments数据，时间胶囊页面会自动重新加载当前月份数据
            print('刷新时间胶囊页面数据失败: $e');
        case 3: // 个人中心页面
          // 个人中心页面会在initState中显示隐私协议，不需要额外刷新
          print('切换到个人中心页面');
    });
/// Moment页面内容
class _MomentContent extends StatefulWidget {
  const _MomentContent();
  State<_MomentContent> createState() => _MomentContentState();
class _MomentContentState extends State<_MomentContent> {
  bool _isLoading = true;
  bool _hasTodayMoment = false;
  void initState() {
    super.initState();
    // 使用Future.microtask确保在build完成后执行
      _fetchWeather();
      _checkTodayMoment();
  Future<void> _fetchWeather() async {
    final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
      await weatherProvider.fetchLatestWeather(cityName: '北京');
      // 错误处理
      print('获取天气失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
  /// 检查今日是否已发布瞬间
  Future<void> _checkTodayMoment() async {
      // 获取今天的日期（只保留年月日）
      // 获取今日瞬间
      final moments = await momentProvider.fetchMomentsByDate(todayDate);
      if (moments.isNotEmpty) {
          _hasTodayMoment = true;
          _todayMoment = moments.first; // 获取最新的一条
      print('检查今日瞬间失败: $e');
  String _getWeatherImage(String weatherCondition) {
    // 根据天气状况返回对应的图片资源
    switch (weatherCondition) {
      case '晴朗':
        return 'assets/images/weather/sunny.png';
      case '多云':
        return 'assets/images/weather/cloudy.png';
      case '阴天':
        return 'assets/images/weather/overcast.png';
      case '小雨':
      case '中雨':
        return 'assets/images/weather/rainy.png';
      case '大雨':
      case '暴雨':
        return 'assets/images/weather/heavy_rain.png';
      case '雷阵雨':
        return 'assets/images/weather/thunder.png';
      case '小雪':
      case '中雪':
      case '大雪':
        return 'assets/images/weather/snowy.png';
      default:
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 天气部分
          _buildWeatherSection(),
          // 日历部分
          _buildCalendarSection(),
        ],
  Widget _buildWeatherSection() {
    return Consumer<WeatherProvider>(
      builder: (context, weatherProvider, child) {
        final weather = weatherProvider.weather;
        final status = weatherProvider.status;
        return Container(
          height: MediaQuery.of(context).size.height * 0.18, // 减小高度，使三个模块都能在首屏显示
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.7),
              ],
          child: _isLoading || status == WeatherStatus.loading
              ? const Center(child: CircularProgressIndicator(color: Colors.white))
              : Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                weather?.cityName ?? '未知城市',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                                '${weather?.temperature.toStringAsFixed(1) ?? '0.0'}°C · ${weather?.weatherCondition ?? '未知'}',
                                  fontSize: 16,
                                '${DateTime.now().year}年${DateTime.now().month}月${DateTime.now().day}日',
                                  fontSize: 14,
                            ],
                          ),
                          Icon(
                            (weather?.weatherCondition ?? '').contains('雨')
                                ? Icons.water_drop
                                : (weather?.weatherCondition ?? '').contains('雪')
                                    ? Icons.ac_unit
                                    : (weather?.weatherCondition ?? '').contains('云')
                                        ? Icons.cloud
                                        : Icons.wb_sunny,
                            color: Colors.white,
                            size: 64,
                        ],
                      ),
                    ],
                  ),
  Widget _buildCalendarSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
          // 今日日期和瞬间 - 扩大显示区域
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            child: Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight: 300, // 设置最小高度，让内容充满屏幕
              child: Padding(
                padding: const EdgeInsets.all(24.0), // 增加内边距
                child: _hasTodayMoment ? _buildTodayMomentView() : _buildNoMomentView(),
  /// 构建今日已发布瞬间视图
  Widget _buildTodayMomentView() {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 显示瞬间内容
            Text(
              _todayMoment?.textContent ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
        const SizedBox(height: 16),
        // 如果有媒体，显示媒体
        if (_todayMoment?.mediaObjectKey != null) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: FutureBuilder<String?>(
              future: _getImageUrl(_todayMoment!.mediaObjectKey!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container(
                    height: 250,
                    width: double.infinity,
                    color: Colors.grey.shade200,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                if (snapshot.hasError || !snapshot.hasData) {
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                            Icons.image_not_supported,
                            size: 48,
                            color: Colors.grey,
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: TextStyle(color: Colors.grey),
                return Image.network(
                  snapshot.data!,
                  height: 250,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 250,
                      width: double.infinity,
                      color: Colors.grey.shade200,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              size: 48,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '图片加载失败',
                              style: TextStyle(color: Colors.grey),
                          ],
                        ),
                    );
                  },
                );
              },
          const SizedBox(height: 16),
            // 显示时间和位置
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey,
                const SizedBox(width: 4),
                Text(
                  DateFormat('HH:mm').format(_todayMoment?.createdAt ?? DateTime.now()),
                  style: TextStyle(color: Colors.grey),
                const SizedBox(width: 16),
                if (_todayMoment?.locationName != null) ...[
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey,
                  const SizedBox(width: 4),
                  Text(
                    _todayMoment?.locationName ?? '',
                    style: TextStyle(color: Colors.grey),
                ],
        // 编辑按钮在右上角
        Positioned(
          top: 0,
          right: 0,
          child: IconButton(
              if (_todayMoment != null) {
                context.pushNamed('moment-edit', extra: _todayMoment);
              }
            icon: const Icon(Icons.edit),
            tooltip: '编辑今日瞬间',
            iconSize: 20,
      ],
  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      });
      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      return null;
      print('获取图片URL失败: $e');
  /// 构建未发布瞬间视图
  Widget _buildNoMomentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
        Text(
          '今天',
          style: Theme.of(context).textTheme.titleLarge,
        const SizedBox(height: 8),
          '${DateTime.now().day}',
          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                fontWeight: FontWeight.bold,
        const Text(
          '快来留住属于今天的一克拉时刻吧',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ElevatedButton.icon(
          onPressed: () {
            context.pushNamed('moment-create');
          },
          icon: const Icon(Icons.add_a_photo),
          label: const Text('记录瞬间'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
/// 时间胶囊页面
class _TimeCapsulePage extends StatefulWidget {
  const _TimeCapsulePage();
  State<_TimeCapsulePage> createState() => _TimeCapsulePageState();
class _TimeCapsulePageState extends State<_TimeCapsulePage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  bool _isTimelineView = false;
        title: const Text('时间胶囊'),
        actions: [
            icon: Icon(_isTimelineView ? Icons.calendar_month : Icons.timeline),
              setState(() {
                _isTimelineView = !_isTimelineView;
              });
            tooltip: _isTimelineView ? '切换到日历视图' : '切换到时间轴视图',
      body: _isTimelineView ? _buildTimelineView() : _buildCalendarView(),
      floatingActionButton: FloatingActionButton(
  Widget _buildCalendarView() {
        TableCalendar(
          firstDay: DateTime.utc(2020, 1, 1),
          lastDay: DateTime.utc(2030, 12, 31),
          focusedDay: _focusedDay,
          calendarFormat: _calendarFormat,
          selectedDayPredicate: (day) {
            return isSameDay(_selectedDay, day);
          onDaySelected: (selectedDay, focusedDay) {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
          onFormatChanged: (format) {
              _calendarFormat = format;
          onPageChanged: (focusedDay) {
            _focusedDay = focusedDay;
          calendarStyle: CalendarStyle(
            todayDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
              shape: BoxShape.circle,
            selectedDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            markerDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary,
          headerStyle: HeaderStyle(
            titleCentered: true,
            formatButtonVisible: true,
            formatButtonShowsNext: false,
            formatButtonDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
        const Divider(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
                DateFormat('yyyy年MM月dd日').format(_selectedDay),
                style: Theme.of(context).textTheme.titleMedium,
              const Spacer(),
                '共3条记录',
                style: Theme.of(context).textTheme.bodyMedium,
        Expanded(
          child: Container(limit: 10),
  Widget _buildTimelineView() {
    // 模拟数据
    final List<Map<String, dynamic>> timelineData = [
      {
        'date': DateTime.now(),
        'count': 2,
        'date': DateTime.now().subtract(const Duration(days: 1)),
        'count': 1,
        'date': DateTime.now().subtract(const Duration(days: 3)),
        'count': 3,
        'date': DateTime.now().subtract(const Duration(days: 7)),
        'date': DateTime.now().subtract(const Duration(days: 14)),
    ];
    return ListView.builder(
      itemCount: timelineData.length,
      itemBuilder: (context, index) {
        final item = timelineData[index];
        final date = item['date'] as DateTime;
        final count = item['count'] as int;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                DateFormat('yyyy年MM月dd日').format(date),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
            for (int i = 0; i < count; i++)
              Card(
                margin: const EdgeInsets.only(bottom: 12.0, left: 16.0),
                child: Padding(
                          CircleAvatar(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            child: const Icon(Icons.camera_alt, color: Colors.white),
                          const SizedBox(width: 12),
                                '记录瞬间',
                                style: Theme.of(context).textTheme.titleMedium,
                                DateFormat('HH:mm').format(date),
                                style: Theme.of(context).textTheme.bodySmall,
                      const SizedBox(height: 12),
                      const Text('这是一个美好的瞬间，值得记录和回味...'),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                          Chip(
                            label: const Text('快乐'),
                            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            label: const Text('生活'),
                            backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
            const Divider(),
/// 悦己聊天内容
class _InnerHeartContent extends StatefulWidget {
  const _InnerHeartContent({Key? key}) : super(key: key);
  State<_InnerHeartContent> createState() => _InnerHeartContentState();
class _InnerHeartContentState extends State<_InnerHeartContent> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
    _loadCachedMessages();
  /// 加载缓存的对话内容
  Future<void> _loadCachedMessages() async {
      final localStorage = Provider.of<LocalStorage>(context, listen: false);
      final cachedMessages = await localStorage.getStringList('chat_messages') ?? [];
      if (cachedMessages.isEmpty) {
        // 如果没有缓存消息，添加欢迎消息
        _messages.add(ChatMessage(
          text: '你好！我是悦己，你的专属心理健康助手。我在这里倾听你的心声，陪伴你探索内心世界。有什么想要分享的吗？',
          isUser: false,
          timestamp: DateTime.now(),
        ));
      } else {
        // 加载缓存的消息
        for (String messageJson in cachedMessages) {
            final messageData = json.decode(messageJson);
            _messages.add(ChatMessage(
              text: messageData['text'],
              isUser: messageData['isUser'],
              timestamp: DateTime.parse(messageData['timestamp']),
            ));
            print('解析缓存消息失败: $e');
        }
        setState(() {});
        _scrollToBottom();
      print('加载缓存消息失败: $e');
      // 如果加载失败，添加欢迎消息
      _messages.add(ChatMessage(
        text: '你好！我是悦己，你的专属心理健康助手。我在这里倾听你的心声，陪伴你探索内心世界。有什么想要分享的吗？',
        isUser: false,
        timestamp: DateTime.now(),
      ));
  /// 保存对话内容到缓存
  Future<void> _saveCachedMessages() async {
      final messageJsonList = _messages.map((message) {
        return json.encode({
          'text': message.text,
          'isUser': message.isUser,
          'timestamp': message.timestamp.toIso8601String(),
      await localStorage.setStringList('chat_messages', messageJsonList);
      print('保存缓存消息失败: $e');
  /// 开始新的对话
  void _startNewConversation() {
    setState(() {
      _messages.clear();
    _saveCachedMessages();
    _scrollToBottom();
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _isLoading) return;
    // 添加用户消息
        text: text,
        isUser: true,
      _isLoading = true;
    _messageController.clear();
      // 调用后端AI服务
      final response = await apiClient.post('/ai/chat', data: {
        'message': text,
      String aiResponseText = '';
      // 解析AI响应
      if (response is Map<String, dynamic>) {
        if (response['success'] == true && response['data'] != null) {
          final data = response['data'];
          if (data is Map<String, dynamic>) {
            // 如果data是对象，提取message字段
            aiResponseText = data['message'] ?? data['response'] ?? '抱歉，我无法理解您的消息。';
          } else if (data is String) {
            // 如果data是字符串，直接使用
            aiResponseText = data;
          } else {
            aiResponseText = '抱歉，我无法理解您的消息。';
        } else {
          aiResponseText = response['message'] ?? '抱歉，我遇到了一些问题，请稍后再试。';
      } else if (response is String) {
        // 如果响应直接是字符串
        try {
          final jsonResponse = json.decode(response);
          if (jsonResponse is Map<String, dynamic>) {
            aiResponseText = jsonResponse['message'] ?? jsonResponse['response'] ?? response;
            aiResponseText = response;
        } catch (e) {
          aiResponseText = response;
        aiResponseText = '抱歉，我遇到了一些问题，请稍后再试。';
      setState(() {
          text: aiResponseText,
        _isLoading = false;
      // 保存对话到缓存
      _saveCachedMessages();
      _scrollToBottom();
      print('AI聊天失败: $e');
          text: '抱歉，我遇到了一些问题，请稍后再试。',
      // 即使失败也保存对话到缓存
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        // 聊天消息列表
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16.0),
            itemCount: _messages.length + (_isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _messages.length && _isLoading) {
                return _buildTypingIndicator();
              return _buildMessageBubble(_messages[index]);
        // 输入框
        Container(
          padding: const EdgeInsets.all(16.0),
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: '输入你想说的话...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide.none,
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
              IconButton(
                onPressed: _isLoading ? null : _sendMessage,
                icon: Icon(
                  Icons.send,
                  color: _isLoading
                      ? Colors.grey
                      : Theme.of(context).colorScheme.primary,
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: const CircleBorder(),
  Widget _buildMessageBubble(ChatMessage message) {
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: message.isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Icon(
                Icons.psychology,
                size: 18,
            const SizedBox(width: 8),
          Flexible(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(18).copyWith(
                  bottomLeft: message.isUser ? const Radius.circular(18) : const Radius.circular(4),
                  bottomRight: message.isUser ? const Radius.circular(4) : const Radius.circular(18),
                border: message.isUser ? null : Border.all(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 根据是否为用户消息决定是否使用Markdown渲染
                  if (message.isUser)
                    Text(
                      message.text,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                    )
                  else
                    MarkdownBody(
                      data: message.text,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                        strong: TextStyle(
                          fontWeight: FontWeight.bold,
                        em: TextStyle(
                          fontStyle: FontStyle.italic,
                        code: TextStyle(
                          backgroundColor: Colors.grey.shade200,
                          fontFamily: 'monospace',
                          fontSize: 14,
                        codeblockDecoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                      selectable: true,
                  const SizedBox(height: 4),
                    DateFormat('HH:mm').format(message.timestamp),
                    style: TextStyle(
                      color: message.isUser
                          ? Colors.white70
                          : Colors.grey,
                      fontSize: 12,
          if (message.isUser) ...[
              backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                Icons.person,
                color: Theme.of(context).colorScheme.secondary,
  Widget _buildTypingIndicator() {
          CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            child: Icon(
              Icons.psychology,
              size: 18,
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(18).copyWith(
                bottomLeft: const Radius.circular(4),
              border: Border.all(
            child: Row(
              mainAxisSize: MainAxisSize.min,
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                const SizedBox(width: 8),
                  '悦己正在思考...',
                  style: TextStyle(
                    fontSize: 14,
/// 聊天消息模型
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
/// 发现内容
class _DiscoveryContent extends StatelessWidget {
  const _DiscoveryContent();
    // 模拟数据 - 社区列表
    final List<Map<String, dynamic>> communities = [
        'name': '旅行爱好者',
        'icon': Icons.flight,
        'color': const Color(0xFF64B5F6),
        'members': 1289,
        'posts': 356,
        'name': '美食分享',
        'icon': Icons.restaurant,
        'color': const Color(0xFFFFB74D),
        'members': 2567,
        'posts': 892,
        'name': '读书会',
        'icon': Icons.book,
        'color': const Color(0xFF81C784),
        'members': 876,
        'posts': 245,
        'name': '摄影爱好者',
        'icon': Icons.camera_alt,
        'color': const Color(0xFFBA68C8),
        'members': 1543,
        'posts': 678,
        'name': '电影讨论',
        'icon': Icons.movie,
        'color': const Color(0xFFE57373),
        'members': 1876,
        'posts': 532,
        'name': '音乐分享',
        'icon': Icons.music_note,
        'color': const Color(0xFF4DB6AC),
        'members': 2134,
        'posts': 721,
          Text(
            '发现社区',
            style: Theme.of(context).textTheme.headlineSmall,
          const SizedBox(height: 8),
            '探索志同道合的朋友和有趣的内容',
            style: Theme.of(context).textTheme.bodyLarge,
          const SizedBox(height: 24),
          // 热门社区
            '热门社区',
            style: Theme.of(context).textTheme.titleLarge,
          // 社区网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            itemCount: communities.length,
              final community = communities[index];
              return _buildCommunityCard(context, community);
          // 附近的人
            '附近的人',
            child: Padding(
              padding: const EdgeInsets.all(16.0),
                  const Text('查看附近的人需要开启位置权限'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('位置权限请求已发送')),
                      );
                    },
                    child: const Text('开启位置权限'),
  Widget _buildCommunityCard(BuildContext context, Map<String, dynamic> community) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('进入${community['name']}社区')),
          );
        child: Padding(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
                community['icon'] as IconData,
                color: community['color'] as Color,
                size: 32,
                community['name'] as String,
                textAlign: TextAlign.center,
              const SizedBox(height: 4),
                '${community['members']}人 · ${community['posts']}条内容',
                style: Theme.of(context).textTheme.bodySmall,
/// 个人中心内容
class _ProfileContent extends StatefulWidget {
  const _ProfileContent();
  State<_ProfileContent> createState() => _ProfileContentState();
class _ProfileContentState extends State<_ProfileContent> {
  bool _hasShownPrivacyDialog = false;
    // 每次进入个人中心都显示隐私协议
      _showPrivacyDialog();
  /// 显示隐私协议弹窗
  Future<void> _showPrivacyDialog() async {
    if (_hasShownPrivacyDialog) return;
    _hasShownPrivacyDialog = true;
      // 获取隐私协议内容
      final response = await apiClient.post('/moments/privacy-policy');
      String privacyContent = '';
        privacyContent = response['data'] as String;
        privacyContent = '隐私协议加载失败，请稍后重试。';
      // 显示隐私协议弹窗
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('隐私协议'),
            content: SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.6,
              child: SingleChildScrollView(
                child: MarkdownBody(
                  data: privacyContent,
                  styleSheet: MarkdownStyleSheet(
                    p: const TextStyle(fontSize: 14),
                    h1: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    h2: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: const Text('拒绝'),
              ElevatedButton(
                  Navigator.of(context).pop(true);
                child: const Text('同意'),
      );
      if (result != true) {
        // 用户拒绝，切换到首页Moment页面
        if (mounted) {
          // 通过回调函数切换到首页
          final homePageState = context.findAncestorStateOfType<_HomePageState>();
          if (homePageState != null) {
            homePageState.setState(() {
              homePageState._selectedIndex = 0;
      print('获取隐私协议失败: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('隐私协议加载失败')),
    return ListView(
        // 用户信息卡片
        _buildUserInfoCard(context),
        // 功能列表
        _buildFunctionList(context),
        // 设置列表
        _buildSettingsList(context),
        // 版本信息
        ListTile(
          leading: const Icon(Icons.info_outline),
          title: const Text('版本信息'),
          subtitle: const Text('v1.0.0'),
          onTap: () {
            // 长按激活开发者模式
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('长按可激活开发者模式')),
            );
          onLongPress: () {
            // 开发者模式
              const SnackBar(content: Text('开发者模式已激活')),
        // 底部空白
        const SizedBox(height: 24),
  Widget _buildUserInfoCard(BuildContext context) {
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
            // 用户头像
              radius: 48,
                size: 48,
            const SizedBox(height: 16),
            // 用户名
            const Text(
              '默认用户',
              style: TextStyle(
                fontSize: 20,
            const SizedBox(height: 8),
            // 用户ID
              'ID: 10086',
                color: Colors.grey[600],
            // 编辑资料按钮
            OutlinedButton.icon(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('编辑资料功能即将上线')),
              icon: const Icon(Icons.edit),
              label: const Text('编辑资料'),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
  Widget _buildFunctionList(BuildContext context) {
      crossAxisAlignment: CrossAxisAlignment.start,
          padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
            '我的功能',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
          leading: Icon(
            Icons.favorite,
            color: Theme.of(context).colorScheme.primary,
          title: const Text('我的收藏'),
          trailing: const Icon(Icons.chevron_right),
              const SnackBar(content: Text('我的收藏功能即将上线')),
            Icons.history,
          title: const Text('浏览历史'),
              const SnackBar(content: Text('浏览历史功能即将上线')),
            Icons.cloud_upload,
          title: const Text('云同步'),
              const SnackBar(content: Text('云同步功能即将上线')),
  Widget _buildSettingsList(BuildContext context) {
            '设置',
            Icons.color_lens,
            color: Theme.of(context).colorScheme.secondary,
          title: const Text('主题设置'),
              const SnackBar(content: Text('主题设置功能即将上线')),
            Icons.notifications,
          title: const Text('通知设置'),
              const SnackBar(content: Text('通知设置功能即将上线')),
            Icons.privacy_tip,
          title: const Text('隐私设置'),
              const SnackBar(content: Text('隐私设置功能即将上线')),
            Icons.help,
          title: const Text('帮助与反馈'),
              const SnackBar(content: Text('帮助与反馈功能即将上线')),
            Icons.info,
          title: const Text('关于我们'),
              const SnackBar(content: Text('关于我们功能即将上线')),
/// 设置内容
class _SettingsContent extends StatelessWidget {
  const _SettingsContent();
          leading: const Icon(Icons.person_outline),
          title: const Text('个人资料'),
            // 个人资料
              const SnackBar(content: Text('个人资料功能即将上线')),
          leading: const Icon(Icons.color_lens_outlined),
            // 主题设置
          leading: const Icon(Icons.notifications_outlined),
            // 通知设置
          leading: const Icon(Icons.privacy_tip_outlined),
            // 隐私设置
          leading: const Icon(Icons.smart_toy_outlined),
          title: const Text('AI模型设置'),
            // AI模型设置
              const SnackBar(content: Text('AI模型设置功能即将上线')),
          leading: const Icon(Icons.backup_outlined),
          title: const Text('备份与恢复'),
            // 备份与恢复
              const SnackBar(content: Text('备份与恢复功能即将上线')),
          leading: const Icon(Icons.help_outline),
            // 帮助与反馈
          title: const Text('关于'),
            // 关于
              const SnackBar(content: Text('关于功能即将上线')),
        // 长按logo激活开发者模式
        GestureDetector(
          child: ListTile(
            leading: const Icon(Icons.code),
            title: const Text('版本信息'),
            subtitle: const Text('v1.0.0'),

import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
/// 内心页面
class MindPage extends StatelessWidget {
  const MindPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('内心'),
      ),
      body: GridView.builder(
        padding: const EdgeInsets.all(16.0),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16.0,
          mainAxisSpacing: 16.0,
        ),
        itemCount: AppConstants.agentConfigs.length,
        itemBuilder: (context, index) {
          final agent = AppConstants.agentConfigs[index];
          return _buildAgentCard(context, agent);
        },
    );
  }
  /// 构建Agent卡片
  Widget _buildAgentCard(BuildContext context, Map<String, String> agent) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      child: InkWell(
        onTap: () {
          // 导航到Agent聊天页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AgentChatPage(agent: agent),
            ),
          );
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Agent头像
              CircleAvatar(
                radius: 40,
                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                child: Image.asset(
                  agent['avatar'] ?? 'assets/images/agents/default.png',
                  width: 50,
                  height: 50,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.person,
                      size: 50,
                      color: Theme.of(context).colorScheme.primary,
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              
              // Agent名称
              Text(
                agent['name'] ?? '未知',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              const SizedBox(height: 8),
              // Agent描述
                agent['description'] ?? '',
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
            ],
          ),
}

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
/// 时间胶囊页面
class TimeCapsulePage extends StatefulWidget {
  const TimeCapsulePage({super.key});
  @override
  State<TimeCapsulePage> createState() => _TimeCapsulePageState();
}
class _TimeCapsulePageState extends State<TimeCapsulePage> {
  late final MomentService _momentService;
  final LoggerUtil _logger = LoggerUtil();
  bool _isLoading = true;
  Map<DateTime, List<MomentModel>> _events = {};
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<MomentModel> _selectedEvents = [];
  String _errorMessage = '';
  // 心情动画相关状态
  bool _showMoodIcon = false;
  double _moodIconOpacity = 0.0;
  MoodType _selectedDayMood = MoodType.neutral;
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _selectedDay = _focusedDay;
    _loadMonthEvents();
  }
  /// 加载月度瞬间
  Future<void> _loadMonthEvents() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      // 获取当月的第一天和最后一天
      final firstDay = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final lastDay = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
      _logger.i('加载月度瞬间: ${firstDay.toString()} - ${lastDay.toString()}');
      // 获取日期范围内的瞬间
      final moments = await _momentService.getMomentsByDateRange(firstDay, lastDay);
      _logger.i('获取到 ${moments.length} 个瞬间');
      // 按日期分组
      final events = <DateTime, List<MomentModel>>{};
      for (final moment in moments) {
        final date = DateTime(
          moment.createdAt.year,
          moment.createdAt.month,
          moment.createdAt.day,
        );
        if (events[date] == null) {
          events[date] = [];
        }
        events[date]!.add(moment);
        _logger.d('添加瞬间到日期 ${date.toString()}: ${moment.id}');
      }
        _events = events;
        _isLoading = false;
        _updateSelectedEvents();
      _logger.i('月度瞬间加载完成，共 ${events.length} 个日期有瞬间');
    } catch (e) {
      _logger.e('加载月度瞬间失败', e);
        _errorMessage = '加载月度瞬间失败: $e';
    }
  /// 更新选中日期的瞬间
  void _updateSelectedEvents() {
    if (_selectedDay != null) {
      final date = DateTime(
        _selectedDay!.year,
        _selectedDay!.month,
        _selectedDay!.day,
      );
      _selectedEvents = _events[date] ?? [];
    } else {
      _selectedEvents = [];
  /// 随机穿梭
  void _randomTravel() {
    // 获取所有有瞬间的日期
    final dates = _events.keys.toList();
    if (dates.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无可穿梭的日期')),
      return;
    // 随机选择一个日期
    final random = DateTime.now().millisecondsSinceEpoch % dates.length;
    final randomDate = dates[random];
    setState(() {
      _selectedDay = randomDate;
      _focusedDay = randomDate;
      _updateSelectedEvents();
    });
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时间胶囊'),
        actions: [
          IconButton(
            icon: const Icon(Icons.shuffle),
            onPressed: _randomTravel,
            tooltip: '随机穿梭',
          ),
        ],
      ),
      body: Column(
        children: [
          // 日历
          Card(
            margin: const EdgeInsets.all(8.0),
            child: TableCalendar<MomentModel>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) {
                return isSameDay(_selectedDay, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                  _updateSelectedEvents();
                });
                // 显示心情动画
                _showMoodAnimation(selectedDay);
              onPageChanged: (focusedDay) {
                _loadMonthEvents();
              eventLoader: (day) {
                final date = DateTime(day.year, day.month, day.day);
                return _events[date] ?? [];
              calendarStyle: CalendarStyle(
                markersMaxCount: 3,
                markerDecoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                // 根据心情变换日期颜色
                todayDecoration: BoxDecoration(
                  color: _getTodayMoodColor(),
                selectedDecoration: BoxDecoration(
                  color: _getSelectedDayMoodColor(),
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: Theme.of(context).textTheme.titleLarge!,
            ),
          // 心情动画显示区域
          if (_showMoodIcon)
            Container(
              height: 80,
              child: Center(
                child: AnimatedOpacity(
                  opacity: _moodIconOpacity,
                  duration: Duration(milliseconds: 500),
                  child: Text(
                    _getMoodIcon(_selectedDayMood),
                    style: TextStyle(fontSize: 48),
                  ),
          // 选中日期的瞬间列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _selectedEvents.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.event_busy,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  '该日期没有记录',
                                  style: Theme.of(context).textTheme.titleMedium,
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(8.0),
                            itemCount: _selectedEvents.length,
                            itemBuilder: (context, index) {
                              return _buildMomentCard(_selectedEvents[index]);
                            },
                          ),
    );
  /// 构建瞬间卡片
  Widget _buildMomentCard(MomentModel moment) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息和操作按钮
            Row(
              children: [
                CircleAvatar(
                  child: Text(moment.username.isNotEmpty ? moment.username[0] : '?'),
                const SizedBox(width: 8),
                Text(
                  moment.username,
                  style: Theme.of(context).textTheme.titleMedium,
                const Spacer(),
                // 编辑按钮
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  tooltip: '编辑',
                  onPressed: () => _editMoment(moment),
                // 删除按钮
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                  tooltip: '删除',
                  onPressed: () => _showDeleteConfirmation(moment),
              ],
            const SizedBox(height: 8),
            // 时间
            Text(
              DateFormat('yyyy年MM月dd日 HH:mm').format(moment.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
            // 内容
              moment.textContent,
              style: Theme.of(context).textTheme.bodyLarge,
            const SizedBox(height: 16),
            // 媒体
            if (moment.mediaType != MediaType.none && moment.mediaObjectKey != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: FadeInImage.assetNetwork(
                  placeholder: 'assets/images/placeholder.png',
                  image: moment.mediaProxyUrl ?? '',
                  height: 150, // 减小高度，使页面更紧凑
                  width: double.infinity,
                  fit: BoxFit.cover,
                  imageErrorBuilder: (context, error, stackTrace) {
                    _logger.e('加载图片失败: $error');
                    return Container(
                      height: 150,
                      width: double.infinity,
                      color: Colors.grey[300],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 48,
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: const TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                        ],
                      ),
                    );
                  },
            // 位置和天气
            if (moment.locationName != null || moment.weather != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    if (moment.locationName != null) ...[
                      const Icon(Icons.location_on, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        moment.locationName!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                      const SizedBox(width: 16),
                    ],
                    if (moment.weather != null) ...[
                      const Icon(Icons.cloud, size: 16, color: Colors.grey),
                        '${moment.weather!}${moment.temperature != null ? ' ${moment.temperature!.toStringAsFixed(1)}°C' : ''}',
                  ],
          ],
        ),
  /// 编辑瞬间
  void _editMoment(MomentModel moment) async {
    _logger.i('编辑瞬间: ${moment.id}');
    // 跳转到编辑页面，并传递瞬间数据
    final result = await GoRouter.of(context).pushNamed(
      'moment-edit',
      extra: moment,
      pathParameters: {'id': moment.id.toString()},
    // 如果编辑成功，重新加载数据
    if (result == true) {
      _loadMonthEvents();
  /// 显示删除确认对话框
  void _showDeleteConfirmation(MomentModel moment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条瞬间记录吗？此操作不可恢复。'),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
            onPressed: () {
              Navigator.of(context).pop();
              _deleteMoment(moment);
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
  /// 删除瞬间
  void _deleteMoment(MomentModel moment) async {
      _logger.i('删除瞬间: ${moment.id}');
      // 显示加载指示器
      // 调用删除API
      await _momentService.deleteMoment(moment.id);
      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('瞬间已删除')),
      // 重新加载数据
      _logger.e('删除瞬间失败', e);
      // 显示错误消息
          SnackBar(content: Text('删除失败: $e')),
        setState(() {
          _isLoading = false;
        });
  /// 显示心情动画
  void _showMoodAnimation(DateTime selectedDay) {
    // 获取选中日期的心情
    final date = DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final dayMoments = _events[date] ?? [];
    if (dayMoments.isNotEmpty) {
      // 使用第一个瞬间的心情，或者可以计算平均心情
      _selectedDayMood = dayMoments.first.mood;
        _showMoodIcon = true;
        _moodIconOpacity = 1.0;
      // 2秒后开始淡出
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _moodIconOpacity = 0.0;
          });
          // 淡出完成后隐藏
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted) {
              setState(() {
                _showMoodIcon = false;
              });
            }
  /// 获取今日心情颜色
  Color _getTodayMoodColor() {
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    final todayMoments = _events[todayDate] ?? [];
    if (todayMoments.isNotEmpty) {
      return _getMoodColor(todayMoments.first.mood);
    return Theme.of(context).colorScheme.primary;
  /// 获取选中日期心情颜色
  Color _getSelectedDayMoodColor() {
    if (_selectedDay == null) return Theme.of(context).colorScheme.primary;
    final date = DateTime(_selectedDay!.year, _selectedDay!.month, _selectedDay!.day);
      return _getMoodColor(dayMoments.first.mood);
  /// 根据心情获取颜色
  Color _getMoodColor(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return Colors.orange; // 开心 - 橙色
      case MoodType.excited:
        return Colors.red; // 兴奋 - 红色
      case MoodType.calm:
        return Colors.blue; // 平静 - 蓝色
      case MoodType.sad:
        return Colors.grey; // 难过 - 灰色
      case MoodType.anxious:
        return Colors.purple; // 焦虑 - 紫色
      case MoodType.neutral:
      default:
        return Colors.green; // 一般 - 绿色
  /// 根据心情获取图标
  String _getMoodIcon(MoodType mood) {
        return '😊'; // 开心
        return '🤩'; // 兴奋
        return '😌'; // 平静
        return '😢'; // 难过
        return '😰'; // 焦虑
        return '😐'; // 一般

import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/presentation/pages/community_page.dart';
/// 发现页面
class DiscoverPage extends StatelessWidget {
  const DiscoverPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('发现'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('搜索功能即将上线')),
              );
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // 社区标题
          Text(
            '即刻镇',
            style: Theme.of(context).textTheme.headlineSmall,
          const SizedBox(height: 8),
            '在这里，你可以找到志同道合的朋友',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey,
                ),
          const SizedBox(height: 16),
          
          // 社区列表
          ...AppConstants.communitiesMock.map((community) => _buildCommunityCard(context, community)),
    );
  }
  /// 构建社区卡片
  Widget _buildCommunityCard(BuildContext context, Map<String, dynamic> community) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      child: InkWell(
        onTap: () {
          // 导航到社区页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CommunityPage(community: community),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 社区头部
              Row(
                children: [
                  // 社区图标
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    child: Image.asset(
                      community['icon'] ?? 'assets/images/communities/default.png',
                      width: 32,
                      height: 32,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.group,
                          size: 32,
                          color: Theme.of(context).colorScheme.primary,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // 社区名称和描述
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          community['name'] ?? '未知社区',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                          community['description'] ?? '',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey,
                              ),
                      ],
                ],
              ),
              const SizedBox(height: 16),
              
              // 社区统计
                  Icon(
                    Icons.people,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  const SizedBox(width: 4),
                  Text(
                    '${community['memberCount'] ?? 0} 成员',
                    style: Theme.of(context).textTheme.bodySmall,
                    Icons.chat_bubble,
                    '${community['postCount'] ?? 0} 帖子',
              // 社区标签
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: (community['tags'] as List<dynamic>? ?? []).map((tag) {
                  return Chip(
                    label: Text(tag),
                    labelStyle: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                  );
                }).toList(),
              // 最新帖子预览
              if ((community['posts'] as List<dynamic>?)?.isNotEmpty ?? false)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '最新帖子',
                      style: Theme.of(context).textTheme.titleMedium,
                    const SizedBox(height: 8),
                    _buildPostPreview(context, (community['posts'] as List<dynamic>).first),
                  ],
            ],
        ),
  /// 构建帖子预览
  Widget _buildPostPreview(BuildContext context, Map<String, dynamic> post) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
          // 帖子内容
            post['content'] ?? '',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          // 帖子信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
              // 作者和时间
              Text(
                '${post['author'] ?? '匿名'} · ${post['time'] ?? ''}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
              // 点赞和评论
                    Icons.favorite,
                    size: 14,
                    '${post['likes'] ?? 0}',
                  const SizedBox(width: 8),
                  const Icon(
                    color: Colors.grey,
                    '${post['comments'] ?? 0}',
}

import 'package:provider/provider.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/services/agent_service.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';
import 'package:one_moment_app/presentation/providers/auth_provider.dart';
import 'package:one_moment_app/presentation/pages/login_page.dart';
/// Agent聊天页面
class AgentChatPage extends StatefulWidget {
  /// 会话ID
  final int? sessionId;
  /// 代理ID
  final int? agentId;
  /// 构造函数
  const AgentChatPage({
    Key? key,
    this.sessionId,
    this.agentId,
  }) : super(key: key);
  @override
  State<AgentChatPage> createState() => _AgentChatPageState();
}
class _AgentChatPageState extends State<AgentChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  void initState() {
    super.initState();
    _loadChatSession();
  }
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  /// 加载聊天会话
  Future<void> _loadChatSession() async {
    final provider = context.read<AgentProvider>();
    final authProvider = context.read<AuthProvider>();
    setState(() {
      _isLoading = true;
    });
    if (widget.sessionId != null) {
      // 加载现有会话
      await provider.fetchChatSessionDetail(widget.sessionId!);
    } else if (widget.agentId != null) {
      // 创建新会话前检查登录状态
      if (!authProvider.isAuthenticated) {
        // 显示登录对话框
        final loginResult = await _showLoginDialog();
        if (loginResult != true) {
          // 用户取消登录，返回上一页
          if (mounted) {
            Navigator.of(context).pop();
          }
          return;
        }
      }
      // 获取用户ID
      final userId = authProvider.user?.id ?? 1;
      // 创建新会话
      await provider.createChatSession(
        userId: userId,
        agentId: widget.agentId!,
        title: '新的对话',
      );
    }
      _isLoading = false;
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
  /// 显示登录对话框
  Future<bool?> _showLoginDialog() async {
    await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('AI对话功能需要登录后才能使用，是否现在登录？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const LoginPage(),
                ),
              );
              return result == true;
            },
            child: const Text('登录'),
        ],
      ),
    );
  /// 发送消息
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;
    _messageController.clear();
    await provider.sendChatMessage(content: message);
  /// 滚动到底部
  void _scrollToBottom() {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<AgentProvider>(
          builder: (context, provider, child) {
            final session = provider.currentSession;
            return Text(session?.agent?.name ?? '聊天');
          },
        ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // 显示Agent信息
              final provider = context.read<AgentProvider>();
              final agent = provider.currentSession?.agent;
              if (agent != null) {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(agent.name),
                    content: Text(agent.description ?? ''),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('关闭'),
                      ),
                    ],
                  ),
                );
              }
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: Consumer<AgentProvider>(
                    builder: (context, provider, child) {
                      final session = provider.currentSession;
                      final messages = session?.messages ?? [];
                      if (messages.isEmpty) {
                        return const Center(
                          child: Text('开始新的对话吧！'),
                        );
                      }
                      return ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          return _buildMessageItem(message);
                        },
                      );
                    },
                _buildInputArea(),
              ],
            ),
  /// 构建消息项
  Widget _buildMessageItem(AgentChatMessageModel message) {
    final isUser = message.role == MessageRole.user;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(message),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser ? Colors.white : null,
          if (isUser) _buildAvatar(message),
  /// 构建头像
  Widget _buildAvatar(AgentChatMessageModel message) {
    final session = provider.currentSession;
    final agent = session?.agent;
    if (message.role == MessageRole.user) {
      return CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: const Icon(Icons.person, color: Colors.white),
    } else {
        backgroundColor: Theme.of(context).colorScheme.secondary,
        child: agent?.avatar != null
            ? Image.asset(agent!.avatar!)
            : const Icon(Icons.smart_toy, color: Colors.white),
  /// 构建输入区域
  Widget _buildInputArea() {
    return Consumer<AgentProvider>(
      builder: (context, provider, child) {
        final isResponding = provider.isAiResponding;
        return Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, -1),
            ],
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isResponding)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      const SizedBox(width: 8),
                      Text(
                        'AI正在思考中...',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontSize: 12,
                        ),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      enabled: !isResponding,
                      decoration: InputDecoration(
                        hintText: isResponding ? '请等待AI回复...' : '输入消息...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                      textInputAction: TextInputAction.send,
                      onSubmitted: isResponding ? null : (_) => _sendMessage(),
                    ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    onPressed: isResponding ? null : _sendMessage,
                    mini: true,
                    child: isResponding
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.send),
      },

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
import 'package:provider/provider.dart';
/// 瞬间编辑页面
class MomentEditPage extends StatefulWidget {
  final MomentModel moment;
  const MomentEditPage({super.key, required this.moment});
  @override
  State<MomentEditPage> createState() => _MomentEditPageState();
}
class _MomentEditPageState extends State<MomentEditPage> {
  final LoggerUtil _logger = LoggerUtil();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();
  bool _isLoading = false;
  File? _selectedImage;
  String? _weather;
  String? _location;
  String _selectedMood = 'neutral';
  String _selectedMediaType = 'none';
  late MomentService _momentService;
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    // 初始化表单数据
    _contentController.text = widget.moment.textContent;
    _locationController.text = widget.moment.locationName ?? '';
    _location = widget.moment.locationName;
    _weather = widget.moment.weather;
    _selectedMood = widget.moment.mood.toString().split('.').last;
    _selectedMediaType = widget.moment.mediaType.toString().split('.').last;
    _logger.i('初始化瞬间编辑页面: ${widget.moment.id}');
  }
  void dispose() {
    _contentController.dispose();
    _locationController.dispose();
    super.dispose();
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑瞬间'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateMoment,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('保存'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 创建时间
                  Text(
                    '创建于 ${DateFormat('yyyy年MM月dd日 HH:mm').format(widget.moment.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                        ),
                  ),
                  const SizedBox(height: 16),
                  // 内容输入
                  TextField(
                    controller: _contentController,
                    maxLines: 5,
                    decoration: const InputDecoration(
                      hintText: '记录此刻的想法...',
                      border: OutlineInputBorder(),
                  // 媒体展示和选择
                  _buildMediaSection(),
                  // 位置输入
                    controller: _locationController,
                      labelText: '位置',
                      hintText: '添加位置',
                      prefixIcon: Icon(Icons.location_on),
                    onChanged: (value) {
                      setState(() {
                        _location = value.isEmpty ? null : value;
                      });
                    },
                  // 天气展示
                  if (_weather != null)
                    Card(
                      child: ListTile(
                        leading: const Icon(Icons.cloud),
                        title: Text('天气: $_weather'),
                        trailing: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _weather = null;
                            });
                          },
                      ),
                  // 心情选择
                    '心情',
                    style: Theme.of(context).textTheme.titleMedium,
                  const SizedBox(height: 8),
                  _buildMoodSelector(),
                ],
              ),
            ),
    );
  /// 构建媒体部分
  Widget _buildMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '媒体',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        // 当前媒体展示
        if (_selectedMediaType == 'image' && widget.moment.mediaObjectKey != null && widget.moment.mediaObjectKey!.isNotEmpty && _selectedImage == null)
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: FutureBuilder<String?>(
                  future: _getImageUrl(widget.moment.mediaObjectKey!),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[100],
                        child: const Center(
                          child: CircularProgressIndicator(),
                      );
                    }
                    if (snapshot.hasError || !snapshot.hasData) {
                        color: Colors.grey[300],
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                '图片加载失败',
                                style: TextStyle(color: Colors.grey),
                            ],
                          ),
                    return Image.network(
                      snapshot.data!,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        _logger.e('加载图片失败: $error');
                        return Container(
                          height: 200,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(color: Colors.grey),
                              ],
                            ),
                        );
                      },
                    );
                  },
                ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _selectedMediaType = 'none';
                    });
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.black54,
            ],
        // 新选择的图片
        if (_selectedImage != null)
                child: Image.file(
                  _selectedImage!,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                      _selectedImage = null;
        // 媒体选择按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.photo_library),
              label: const Text('从相册选择'),
            const SizedBox(width: 16),
              onPressed: _takePhoto,
              icon: const Icon(Icons.camera_alt),
              label: const Text('拍照'),
          ],
      ],
  /// 构建心情选择器
  Widget _buildMoodSelector() {
    return Wrap(
      spacing: 8,
        _buildMoodChip('happy', '开心'),
        _buildMoodChip('calm', '平静'),
        _buildMoodChip('sad', '难过'),
        _buildMoodChip('excited', '兴奋'),
        _buildMoodChip('anxious', '焦虑'),
        _buildMoodChip('neutral', '一般'),
  /// 构建心情选择芯片
  Widget _buildMoodChip(String mood, String label) {
    final isSelected = _selectedMood == mood;
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedMood = mood;
          });
        }
      },
  /// 从相册选择图片
  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );
      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
          _selectedMediaType = 'image';
        });
      }
    } catch (e) {
      _logger.e('选择图片失败', e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
    }
  /// 拍照
  Future<void> _takePhoto() async {
        source: ImageSource.camera,
      _logger.e('拍照失败', e);
        SnackBar(content: Text('拍照失败: $e')),
  /// 更新瞬间
  Future<void> _updateMoment() async {
    // 验证内容
    if (_contentController.text.trim().isEmpty) {
        const SnackBar(content: Text('请输入内容')),
      return;
    setState(() {
      _isLoading = true;
    });
      // 直接使用MomentService更新瞬间
      await _momentService.updateMoment(
        widget.moment.id,
        _contentController.text.trim(),
        location: _location,
        weather: _weather,
        mood: _getMoodTypeFromString(_selectedMood),
        isPrivate: widget.moment.isPrivate,
      // 更新成功，显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('瞬间更新成功！'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
        );
        // 等待提示显示后再返回
        await Future.delayed(const Duration(seconds: 1));
        // 返回上一页
        if (mounted) {
          context.pop(true);
      _logger.e('更新瞬间失败: $e');
          SnackBar(content: Text('更新失败: $e')),
    } finally {
      setState(() {
        _isLoading = false;
      });
  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      return null;
      _logger.e('获取图片URL失败: $e');
  /// 从字符串获取心情类型
  MoodType _getMoodTypeFromString(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return MoodType.happy;
      case 'calm':
        return MoodType.calm;
      case 'sad':
        return MoodType.sad;
      case 'excited':
        return MoodType.excited;
      case 'anxious':
        return MoodType.anxious;
      default:
        return MoodType.neutral;

import 'package:flutter/material.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:video_player/video_player.dart';
/// 瞬间详情卡片
class MomentDetailCard extends StatefulWidget {
  final MomentModel moment;
  const MomentDetailCard({super.key, required this.moment});
  @override
  State<MomentDetailCard> createState() => _MomentDetailCardState();
}
class _MomentDetailCardState extends State<MomentDetailCard> {
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  void initState() {
    super.initState();
    _initializeVideoController();
  }
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  /// 初始化视频控制器
  Future<void> _initializeVideoController() async {
    if (widget.moment.mediaType == MediaType.video && widget.moment.mediaPath != null) {
      _videoController = VideoPlayerController.file(File(widget.moment.mediaPath!));
      await _videoController!.initialize();
      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
        });
      }
    }
  /// 播放或暂停视频
  void _toggleVideo() {
    if (_videoController != null) {
      if (_videoController!.value.isPlaying) {
        _videoController!.pause();
      } else {
        _videoController!.play();
      setState(() {});
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy年MM月dd日 HH:mm');
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 时间和心情
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  dateFormat.format(widget.moment.createdAt),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                _buildMoodIcon(widget.moment.mood),
              ],
            ),
            const SizedBox(height: 16),
            // 内容
            Text(widget.moment.content),
            // 媒体
            if (widget.moment.mediaType != MediaType.none && widget.moment.mediaPath != null)
              _buildMedia(),
            // 位置和天气
            if (widget.moment.location != null || widget.moment.weather != null)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    if (widget.moment.location != null)
                      Expanded(
                        child: Row(
                          children: [
                            const Icon(Icons.location_on, size: 16),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.moment.location!,
                                style: Theme.of(context).textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (widget.moment.location != null && widget.moment.weather != null)
                      const SizedBox(width: 16),
                    if (widget.moment.weather != null)
                            const Icon(Icons.cloud, size: 16),
                                widget.moment.weather!,
                  ],
              ),
            // 私密标记
            if (widget.moment.isPrivate)
                    const Icon(Icons.lock, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '私密',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
            // 操作按钮
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      // 分享功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('分享功能即将上线')),
                      );
                    },
                  ),
                    icon: const Icon(Icons.edit),
                      // 编辑功能
                        const SnackBar(content: Text('编辑功能即将上线')),
                    icon: const Icon(Icons.delete),
                      // 删除功能
                        const SnackBar(content: Text('删除功能即将上线')),
                ],
          ],
        ),
      ),
    );
  /// 构建媒体
  Widget _buildMedia() {
    switch (widget.moment.mediaType) {
      case MediaType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(widget.moment.mediaPath!),
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        );
      case MediaType.video:
        return _isVideoInitialized
            ? AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: Stack(
                  alignment: Alignment.center,
                    VideoPlayer(_videoController!),
                    IconButton(
                      icon: Icon(
                        _videoController!.value.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        size: 48,
                        color: Colors.white,
                      onPressed: _toggleVideo,
              )
            : Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                child: const Center(
                  child: CircularProgressIndicator(),
              );
      case MediaType.audio:
        return Container(
          height: 80,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () {
                  // 播放音频
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('音频播放功能即将上线')),
                  );
                },
              const SizedBox(width: 8),
              const Text('点击播放音频'),
            ],
      case MediaType.none:
      default:
        return const SizedBox.shrink();
  /// 构建心情图标
  Widget _buildMoodIcon(MoodType mood) {
    IconData iconData;
    Color color;
    String label;
    switch (mood) {
      case MoodType.happy:
        iconData = Icons.sentiment_very_satisfied;
        color = Colors.amber;
        label = '开心';
        break;
      case MoodType.calm:
        iconData = Icons.sentiment_satisfied;
        color = Colors.green;
        label = '平静';
      case MoodType.sad:
        iconData = Icons.sentiment_dissatisfied;
        color = Colors.blue;
        label = '难过';
      case MoodType.excited:
        iconData = Icons.mood;
        color = Colors.orange;
        label = '兴奋';
      case MoodType.anxious:
        iconData = Icons.mood_bad;
        color = Colors.purple;
        label = '焦虑';
      case MoodType.neutral:
        iconData = Icons.sentiment_neutral;
        color = Colors.grey;
        label = '中性';
    return Row(
      children: [
        Icon(iconData, color: color),
        const SizedBox(width: 4),
        Text(label),
      ],

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/weather_model.dart';
import 'package:one_moment_app/data/services/weather_service.dart';
import 'package:provider/provider.dart';
/// 天气组件
class WeatherWidget extends StatefulWidget {
  const WeatherWidget({super.key});
  @override
  State<WeatherWidget> createState() => _WeatherWidgetState();
}
class _WeatherWidgetState extends State<WeatherWidget> {
  late final WeatherService _weatherService;
  bool _isLoading = true;
  WeatherModel? _weather;
  String _errorMessage = '';
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _weatherService = WeatherService(apiClient: apiClient);
    _loadWeather();
  }
  /// 加载天气
  Future<void> _loadWeather() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      // 获取最新天气
      final weather = await _weatherService.getLatestWeather();
        _weather = weather;
        _isLoading = false;
    } catch (e) {
        _errorMessage = '加载天气失败: $e';
    }
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.25, // 占据屏幕高度的四分之一
      width: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(_weather?.getWeatherBackground() ?? 'assets/images/weather_bg/default_bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(color: Colors.white),
                  ),
                )
              : _buildWeatherContent(),
    );
  /// 构建天气内容
  Widget _buildWeatherContent() {
    if (_weather == null) {
      return const Center(
        child: Text(
          '暂无天气数据',
          style: TextStyle(color: Colors.white),
      );
      padding: const EdgeInsets.all(16.0),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.black.withOpacity(0.1),
          ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期和城市
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('yyyy年MM月dd日 EEEE', 'zh_CN').format(DateTime.now()),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
                _weather!.cityName,
            ],
          ),
          const Spacer(),
          
          // 天气信息
              // 温度和天气状况
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_weather!.temperature.toStringAsFixed(1)}°C',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                    ),
                  const SizedBox(height: 4),
                    _weather!.weatherCondition,
                      fontSize: 18,
                  if (_weather!.windDirection != null)
                    Text(
                      '${_weather!.windDirection} ${_weather!.windSpeed?.toStringAsFixed(1) ?? ''}m/s',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                ],
              
              // 天气图标
              Image.asset(
                _weather!.getWeatherIcon(),
                width: 80,
                height: 80,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.wb_sunny,
                    color: Colors.white,
                    size: 80,
                  );
                },
        ],

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';
/// 时间胶囊组件
class TimeCapsuleWidget extends StatefulWidget {
  final VoidCallback onViewTimeCapsule;
  const TimeCapsuleWidget({
    super.key,
    required this.onViewTimeCapsule,
  });
  @override
  State<TimeCapsuleWidget> createState() => _TimeCapsuleWidgetState();
}
class _TimeCapsuleWidgetState extends State<TimeCapsuleWidget> {
  final LoggerUtil _logger = LoggerUtil();
  late final MomentService _momentService;
  bool _isLoading = true;
  List<MomentModel> _lastYearMoments = [];
  String _errorMessage = '';
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _loadLastYearMoments();
  }
  /// 加载去年今日瞬间
  Future<void> _loadLastYearMoments() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      // 获取去年今日的日期范围
      final now = DateTime.now();
      final lastYear = now.year - 1;
      final startOfDay = DateTime(lastYear, now.month, now.day);
      final endOfDay = DateTime(lastYear, now.month, now.day, 23, 59, 59);
      // 获取去年今日瞬间
      final moments = await _momentService.getMomentsByDateRange(startOfDay, endOfDay);
        _lastYearMoments = moments;
        _isLoading = false;
    } catch (e) {
        _errorMessage = '加载去年今日瞬间失败: $e';
    }
  Widget build(BuildContext context) {
    // 如果没有去年今日的瞬间，则不显示时间胶囊
    if (!_isLoading && _lastYearMoments.isEmpty && _errorMessage.isEmpty) {
      return const SizedBox.shrink();
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 时间胶囊标题
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.access_time),
                    const SizedBox(width: 8),
                    Text(
                      '时间胶囊',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ],
                ),
                Text(
                  '去年今日',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
              ],
            ),
            const Divider(),
            // 时间胶囊内容
            _isLoading
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                  )
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(_errorMessage),
                        ),
                      )
                    : _lastYearMoments.isEmpty
                        ? const Center(
                            child: Padding(
                              padding: EdgeInsets.all(32.0),
                              child: Text('去年今日没有记录'),
                            ),
                          )
                        : _buildLastYearMoment(_lastYearMoments.first),
            // 查看更多按钮
            if (!_isLoading && _lastYearMoments.isNotEmpty)
              Center(
                child: TextButton(
                  onPressed: widget.onViewTimeCapsule,
                  child: const Text('查看更多回忆'),
              ),
          ],
        ),
    );
  /// 构建去年今日瞬间
  Widget _buildLastYearMoment(MomentModel moment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间
          Text(
            DateFormat('yyyy年MM月dd日 HH:mm').format(moment.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
          ),
          const SizedBox(height: 8),
          // 内容
            moment.content,
            style: Theme.of(context).textTheme.bodyLarge,
          const SizedBox(height: 16),
          // 媒体
          if (moment.mediaType != MediaType.none && moment.mediaPath != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: FadeInImage.assetNetwork(
                placeholder: 'assets/images/placeholder.png',
                image: moment.mediaPath!,
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
                imageErrorBuilder: (context, error, stackTrace) {
                  _logger.e('加载图片失败: $error');
                  return Container(
                    height: 150,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 48,
                  );
                },
        ],

import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';
/// 今日日历组件
class TodayCalendarWidget extends StatefulWidget {
  final VoidCallback onCreateMoment;
  final VoidCallback onViewTimeCapsule;
  const TodayCalendarWidget({
    super.key,
    required this.onCreateMoment,
    required this.onViewTimeCapsule,
  });
  @override
  State<TodayCalendarWidget> createState() => _TodayCalendarWidgetState();
}
class _TodayCalendarWidgetState extends State<TodayCalendarWidget> {
  late final MomentService _momentService;
  bool _isLoading = true;
  List<MomentModel> _todayMoments = [];
  String _errorMessage = '';
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _loadTodayMoments();
  }
  /// 加载今日瞬间
  Future<void> _loadTodayMoments() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      // 获取今日的开始和结束时间
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
      // 获取今日瞬间
      final moments = await _momentService.getMomentsByDateRange(startOfDay, endOfDay);
        _todayMoments = moments;
        _isLoading = false;
    } catch (e) {
        _errorMessage = '加载今日瞬间失败: $e';
    }
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final weekday = DateFormat('EEEE').format(now); // 获取星期几
    final dayOfMonth = now.day.toString();
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日历标题
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '今日',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                  DateFormat('yyyy年MM月dd日').format(now),
                  style: Theme.of(context).textTheme.titleMedium,
              ],
            ),
            const Divider(),
            // 日历内容
            _isLoading
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(_errorMessage),
                        ),
                      )
                    : Column(
                        children: [
                          // 日历图片 - 类似即刻APP的日历样式
                          Container(
                            height: 180,
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(vertical: 16.0),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(16.0),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                // 日历背景
                                Positioned.fill(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(16.0),
                                    child: Container(
                                      color: Theme.of(context).colorScheme.primaryContainer,
                                    ),
                                  ),
                                // 日期信息
                                Positioned(
                                  top: 20,
                                  left: 20,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        weekday,
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                        DateFormat('yyyy年MM月').format(now),
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                                    ],
                                // 大日期数字
                                Center(
                                  child: Text(
                                    dayOfMonth,
                                    style: TextStyle(
                                      fontSize: 100,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
                                // 今日瞬间提示
                                  bottom: 20,
                                  right: 20,
                                    _todayMoments.isEmpty ? '今日暂无记录' : '今日已记录 ${_todayMoments.length} 条',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                          // 今日瞬间内容
                          if (_todayMoments.isNotEmpty)
                            _buildTodayMoment(_todayMoments.first),
                          // 记录瞬间按钮
                          if (_todayMoments.isEmpty)
                            Center(
                              child: ElevatedButton(
                                onPressed: widget.onCreateMoment,
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                child: const Text('记录瞬间'),
                              ),
                          const SizedBox(height: 16),
                          // 时间胶囊入口
                          InkWell(
                            onTap: widget.onViewTimeCapsule,
                            borderRadius: BorderRadius.circular(8.0),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12.0),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.calendar_month,
                                    color: Theme.of(context).colorScheme.primary,
                                  const SizedBox(width: 8),
                                  Text(
                                    '查看时间胶囊',
                                      color: Theme.of(context).colorScheme.primary,
                                ],
                        ],
                      ),
          ],
        ),
    );
  /// 构建今日瞬间
  Widget _buildTodayMoment(MomentModel moment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间
          Text(
            DateFormat('HH:mm').format(moment.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
          ),
          const SizedBox(height: 8),
          // 内容
            moment.content,
            style: Theme.of(context).textTheme.bodyLarge,
          const SizedBox(height: 16),
          // 媒体
          if (moment.mediaType != MediaType.none && moment.mediaPath != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                moment.mediaPath!,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 48,
                  );
                },
              ),
        ],

import 'package:flutter/material.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/network/api_client.dart';
/// 调试悬浮窗
class DebugOverlay extends StatefulWidget {
  final Widget child;
  final ApiClient apiClient;
  const DebugOverlay({
    Key? key,
    required this.child,
    required this.apiClient,
  }) : super(key: key);
  @override
  State<DebugOverlay> createState() => _DebugOverlayState();
}
class _DebugOverlayState extends State<DebugOverlay> {
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();
  bool _showOverlay = false;
  bool _showLogs = false;
  Timer? _refreshTimer;
  List<LogEntry> _logs = [];
  void initState() {
    super.initState();
    // 每秒刷新一次日志
    _refreshTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_showLogs && mounted) {
        setState(() {
          _logs = _logger.logBuffer;
        });
      }
    });
  }
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  Widget build(BuildContext context) {
    // 如果不是调试模式，直接返回子组件
    if (!_config.isDebugMode) {
      return widget.child;
    }
    return Stack(
      textDirection: TextDirection.ltr,
      children: [
        // 原始内容
        widget.child,
        // 调试按钮
        Positioned(
          right: 16,
          bottom: 16,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _showOverlay = !_showOverlay;
                if (_showOverlay) {
                  _logs = _logger.logBuffer;
                }
              });
            },
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.bug_report,
                color: Colors.white,
            ),
          ),
        ),
        // 调试面板
        if (_showOverlay)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
              height: MediaQuery.of(context).size.height * 0.6,
                color: Colors.black.withOpacity(0.9),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              child: Column(
                children: [
                  // 标题栏
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[800],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '调试面板 (${_config.environmentName})',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () {
                            setState(() {
                              _showOverlay = false;
                            });
                          },
                      ],
                  ),
                  // 选项卡
                    color: Colors.grey[900],
                        _buildTabButton('网络', !_showLogs),
                        _buildTabButton('日志', _showLogs),
                  // 内容区域
                  Expanded(
                    child: _showLogs ? _buildLogsPanel() : _buildNetworkPanel(),
                ],
      ],
    );
  /// 构建选项卡按钮
  Widget _buildTabButton(String title, bool isActive) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showLogs = title == '日志';
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isActive ? Colors.blue : Colors.transparent,
                width: 2,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isActive ? Colors.blue : Colors.white,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
      ),
  /// 构建日志面板
  Widget _buildLogsPanel() {
    return Column(
        // 工具栏
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: Colors.grey[850],
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '日志 (${_logs.length})',
                style: const TextStyle(color: Colors.white),
              Row(
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _logs = _logger.logBuffer;
                      });
                    },
                    icon: const Icon(Icons.delete, color: Colors.white),
                        _logger.clearLogBuffer();
                        _logs = [];
            ],
        // 日志列表
        Expanded(
          child: ListView.builder(
            itemCount: _logs.length,
            reverse: true,
            itemBuilder: (context, index) {
              final log = _logs[_logs.length - 1 - index];
              return _buildLogItem(log);
  /// 构建日志项
  Widget _buildLogItem(LogEntry log) {
    Color color;
    switch (log.level) {
      case 'ERROR':
        color = Colors.red;
        break;
      case 'WARN':
        color = Colors.orange;
      case 'INFO':
        color = Colors.green;
      case 'DEBUG':
        color = Colors.blue;
      case 'NETWORK':
        color = Colors.purple;
      default:
        color = Colors.white;
    final timeStr = '${log.timestamp.hour.toString().padLeft(2, '0')}:${log.timestamp.minute.toString().padLeft(2, '0')}:${log.timestamp.second.toString().padLeft(2, '0')}';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[800]!,
            width: 0.5,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                child: Text(
                  log.level,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
              const SizedBox(width: 8),
                timeStr,
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
          const SizedBox(height: 4),
          Text(
            log.message,
            style: const TextStyle(color: Colors.white),
          if (log.error != null) ...[
            const SizedBox(height: 4),
            Text(
              log.error.toString(),
              style: TextStyle(
                color: Colors.red[300],
                fontSize: 12,
          ],
        ],
  /// 构建网络面板
  Widget _buildNetworkPanel() {
        // 环境信息
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
                '环境: ${_config.environmentName}',
              const SizedBox(height: 8),
                'API地址: ${widget.apiClient.baseUrl}',
        // 操作按钮
        Padding(
              ElevatedButton(
                onPressed: () async {
                  final result = await widget.apiClient.testConnection();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(result ? 'API连接成功' : 'API连接失败'),
                        backgroundColor: result ? Colors.green : Colors.red,
                    );
                  }
                },
                child: const Text('测试API连接'),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'API地址',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    widget.apiClient.updateBaseUrl(value);
                    setState(() {});
                      const SnackBar(
                        content: Text('API地址已更新'),
