import 'package:one_moment_app/data/models/media_type.dart';import 'dart:async';
import 'package:flutter/material.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/network/api_client.dart';

/// 调试悬浮窗
class DebugOverlay extends StatefulWidget {
  final Widget child;
  final ApiClient apiClient;

  const DebugOverlay({
    Key? key,
    required this.child,
    required this.apiClient,
  }) : super(key: key);

  @override
  State<DebugOverlay> createState() => _DebugOverlayState();
}

class _DebugOverlayState extends State<DebugOverlay> {
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();

  bool _showOverlay = false;
  bool _showLogs = false;
  Timer? _refreshTimer;
  List<LogEntry> _logs = [];

  @override
  void initState() {
    super.initState();

    // 每秒刷新一次日志
    _refreshTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_showLogs && mounted) {
        setState(() {
          _logs = _logger.logBuffer;
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果不是调试模式，直接返回子组件
    if (!_config.isDebugMode) {
      return widget.child;
    }

    return Stack(
      textDirection: TextDirection.ltr,
      children: [
        // 原始内容
        widget.child,

        // 调试按钮
        Positioned(
          right: 16,
          bottom: 16,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _showOverlay = !_showOverlay;
                if (_showOverlay) {
                  _logs = _logger.logBuffer;
                }
              });
            },
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.bug_report,
                color: Colors.white,
              ),
            ),
          ),
        ),

        // 调试面板
        if (_showOverlay)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.9),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // 标题栏
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[800],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '调试面板 (${_config.environmentName})',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () {
                            setState(() {
                              _showOverlay = false;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  // 选项卡
                  Container(
                    color: Colors.grey[900],
                    child: Row(
                      children: [
                        _buildTabButton('网络', !_showLogs),
                        _buildTabButton('日志', _showLogs),
                      ],
                    ),
                  ),

                  // 内容区域
                  Expanded(
                    child: _showLogs ? _buildLogsPanel() : _buildNetworkPanel(),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// 构建选项卡按钮
  Widget _buildTabButton(String title, bool isActive) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showLogs = title == '日志';
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isActive ? Colors.blue : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isActive ? Colors.blue : Colors.white,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建日志面板
  Widget _buildLogsPanel() {
    return Column(
      children: [
        // 工具栏
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: Colors.grey[850],
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '日志 (${_logs.length})',
                style: const TextStyle(color: Colors.white),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _logs = _logger.logBuffer;
                      });
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _logger.clearLogBuffer();
                        _logs = [];
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ),

        // 日志列表
        Expanded(
          child: ListView.builder(
            itemCount: _logs.length,
            reverse: true,
            itemBuilder: (context, index) {
              final log = _logs[_logs.length - 1 - index];
              return _buildLogItem(log);
            },
          ),
        ),
      ],
    );
  }

  /// 构建日志项
  Widget _buildLogItem(LogEntry log) {
    Color color;
    switch (log.level) {
      case 'ERROR':
        color = Colors.red;
        break;
      case 'WARN':
        color = Colors.orange;
        break;
      case 'INFO':
        color = Colors.green;
        break;
      case 'DEBUG':
        color = Colors.blue;
        break;
      case 'NETWORK':
        color = Colors.purple;
        break;
      default:
        color = Colors.white;
    }

    final timeStr = '${log.timestamp.hour.toString().padLeft(2, '0')}:${log.timestamp.minute.toString().padLeft(2, '0')}:${log.timestamp.second.toString().padLeft(2, '0')}';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[800]!,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  log.level,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                timeStr,
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            log.message,
            style: const TextStyle(color: Colors.white),
          ),
          if (log.error != null) ...[
            const SizedBox(height: 4),
            Text(
              log.error.toString(),
              style: TextStyle(
                color: Colors.red[300],
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建网络面板
  Widget _buildNetworkPanel() {
    return Column(
      children: [
        // 环境信息
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[850],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '环境: ${_config.environmentName}',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 8),
              Text(
                'API地址: ${widget.apiClient.baseUrl}',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),

        // 操作按钮
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              ElevatedButton(
                onPressed: () async {
                  final result = await widget.apiClient.testConnection();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(result ? 'API连接成功' : 'API连接失败'),
                        backgroundColor: result ? Colors.green : Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('测试API连接'),
              ),
              const SizedBox(height: 16),
              TextField(
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'API地址',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue),
                  ),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    widget.apiClient.updateBaseUrl(value);
                    setState(() {});
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('API地址已更新'),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
