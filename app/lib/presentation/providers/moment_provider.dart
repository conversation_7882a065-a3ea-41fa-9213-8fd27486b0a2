import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/file_upload_service.dart';
import 'package:one_moment_app/data/services/oss_service.dart';

/// 瞬间提供者
class MomentProvider extends ChangeNotifier {
  final ApiClient _apiClient;
  final OssService _ossService;
  final FileUploadService _fileUploadService;
  final LoggerUtil _logger = LoggerUtil();

  /// 瞬间列表
  List<MomentModel> _moments = [];

  /// 是否正在加载
  bool _isLoading = false;

  /// 是否正在上传
  bool _isUploading = false;

  /// 错误信息
  String? _errorMessage;

  /// 已上传的文件URL
  String? _uploadedFileUrl;

  /// 已上传的文件对象键
  String? _uploadedFileObjectKey;

  /// 构造函数
  MomentProvider({
    required ApiClient apiClient,
    required OssService ossService,
  }) : _apiClient = apiClient,
       _ossService = ossService,
       _fileUploadService = FileUploadService(apiClient: apiClient);

  /// 获取瞬间列表
  List<MomentModel> get moments => _moments;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 是否正在上传
  bool get isUploading => _isUploading;

  /// 错误信息
  String? get errorMessage => _errorMessage;

  /// 已上传的文件URL
  String? get uploadedFileUrl => _uploadedFileUrl;

  /// 已上传的文件对象键
  String? get uploadedFileObjectKey => _uploadedFileObjectKey;

  /// 获取所有瞬间
  Future<void> fetchMoments() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _apiClient.post('/moments/getMomentList', data: {});

      _logger.i('获取瞬间列表响应: $response');

      List<dynamic> data;
      if (response is Map<String, dynamic>) {
        if (response.containsKey('content')) {
          // 分页响应
          data = response['content'] as List<dynamic>;
        } else if (response.containsKey('data')) {
          // 包含data字段的响应
          data = response['data'] as List<dynamic>;
        } else {
          _logger.e('获取瞬间列表响应格式错误: $response');
          _errorMessage = '获取瞬间列表失败: 响应格式错误';
          _isLoading = false;
          notifyListeners();
          return;
        }
      } else if (response is List<dynamic>) {
        // 直接返回的是列表
        data = response;
      } else {
        _logger.e('获取瞬间列表响应格式错误: $response');
        _errorMessage = '获取瞬间列表失败: 响应格式错误';
        _isLoading = false;
        notifyListeners();
        return;
      }

      _moments = data.map((item) => MomentModel.fromJson(item)).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _logger.e('获取瞬间列表失败: $e');
      _errorMessage = '获取瞬间列表失败: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 获取指定日期的瞬间
  Future<List<MomentModel>> fetchMomentsByDate(DateTime date) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _apiClient.post('/moments/getMomentsByDate', data: {
        'date': date.toIso8601String().split('T')[0],
      });

      _logger.i('获取指定日期瞬间响应: $response');

      List<dynamic> data;
      if (response is Map<String, dynamic> && response.containsKey('data')) {
        // 处理包含data字段的响应
        data = response['data'] as List<dynamic>;
      } else if (response is List<dynamic>) {
        // 直接返回的是列表
        data = response;
      } else {
        _logger.e('获取指定日期瞬间响应格式错误: $response');
        _errorMessage = '获取指定日期瞬间失败: 响应格式错误';
        _isLoading = false;
        notifyListeners();
        return [];
      }

      final moments = data.map((item) => MomentModel.fromJson(item)).toList();

      _isLoading = false;
      notifyListeners();

      return moments;
    } catch (e) {
      _logger.e('获取指定日期瞬间失败: $e');
      _errorMessage = '获取指定日期瞬间失败: $e';
      _isLoading = false;
      notifyListeners();

      // 返回空列表
      return [];
    }
  }

  /// 创建瞬间
  Future<bool> createMoment({
    required String content,
    required MediaType mediaType,
    required String location,
    required String weather,
    required MoodType mood,
  }) async {
    _isUploading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // 创建瞬间，使用已上传的文件URL和对象键
      final response = await _apiClient.post('/moments/createMoment', data: {
        'contentType': mediaType.toString().split('.').last,
        'textContent': content,
        'mediaUrl': _uploadedFileUrl,
        'mediaObjectKey': _uploadedFileObjectKey,
        'locationName': location,
        'weather': weather,
        'mood': mood.toString().split('.').last
      });

      _logger.i('创建瞬间响应: $response');

      // 处理响应
      if (response is Map<String, dynamic>) {
        Map<String, dynamic> momentData;

        // 检查是否有data字段
        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is Map<String, dynamic>) {
            momentData = data;
          } else {
            _logger.e('创建瞬间响应data字段格式错误: $data');
            _errorMessage = '创建瞬间失败: 响应格式错误';
            _isUploading = false;
            notifyListeners();
            return false;
          }
        } else {
          // 直接返回的是瞬间数据
          momentData = response;
        }

        // 添加到列表
        final newMoment = MomentModel.fromJson(momentData);
        _moments.insert(0, newMoment);

        _isUploading = false;
        notifyListeners();

        return true;
      }

      _logger.e('创建瞬间响应格式错误: $response');
      _errorMessage = '创建瞬间失败: 响应格式错误';
      _isUploading = false;
      notifyListeners();

      return false;
    } catch (e) {
      _logger.e('创建瞬间失败: $e');
      _errorMessage = '创建瞬间失败: $e';
      _isUploading = false;
      notifyListeners();

      return false;
    }
  }

  /// 更新瞬间
  Future<bool> updateMoment({
    required String id,
    required String content,
    required MediaType mediaType,
    File? mediaFile,
    String? location,
    String? weather,
    required MoodType mood,
  }) async {
    _isUploading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // 如果有新的媒体文件，先上传
      if (mediaFile != null) {
        final uploadSuccess = await uploadFile(file: mediaFile, mediaType: mediaType);
        if (!uploadSuccess) {
          _isUploading = false;
          notifyListeners();
          return false;
        }
      }

      // 更新瞬间
      final response = await _apiClient.post('/moments/updateMoment', data: {
        'momentId': id,
        'contentType': mediaType.toString().split('.').last,
        'textContent': content,
        'mediaUrl': _uploadedFileUrl,
        'mediaObjectKey': _uploadedFileObjectKey,
        'locationName': location,
        'weather': weather,
        'mood': mood.toString().split('.').last
      });

      _logger.i('更新瞬间响应: $response');

      // 处理响应
      if (response is Map<String, dynamic>) {
        Map<String, dynamic> momentData;

        // 检查是否有data字段
        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is Map<String, dynamic>) {
            momentData = data;
          } else {
            _logger.e('更新瞬间响应data字段格式错误: $data');
            _errorMessage = '更新瞬间失败: 响应格式错误';
            _isUploading = false;
            notifyListeners();
            return false;
          }
        } else {
          // 直接返回的是瞬间数据
          momentData = response;
        }

        // 更新列表中的瞬间
        final updatedMoment = MomentModel.fromJson(momentData);
        final index = _moments.indexWhere((moment) => moment.id == id);
        if (index != -1) {
          _moments[index] = updatedMoment;
        }

        _isUploading = false;
        notifyListeners();

        return true;
      }

      _logger.e('更新瞬间响应格式错误: $response');
      _errorMessage = '更新瞬间失败: 响应格式错误';
      _isUploading = false;
      notifyListeners();

      return false;
    } catch (e) {
      _logger.e('更新瞬间失败: $e');
      _errorMessage = '更新瞬间失败: $e';
      _isUploading = false;
      notifyListeners();

      return false;
    }
  }

  /// 删除瞬间
  Future<bool> deleteMoment(String momentId) async {
    try {
      await _apiClient.post('/moments/deleteMoment', data: {
        'momentId': momentId,
      });

      // 从列表中移除
      _moments.removeWhere((moment) => moment.id == momentId);
      notifyListeners();

      return true;
    } catch (e) {
      _logger.e('删除瞬间失败: $e');
      _errorMessage = '删除瞬间失败: $e';
      notifyListeners();

      return false;
    }
  }

  /// 清除错误
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 上传文件
  Future<bool> uploadFile({
    required File file,
    required MediaType mediaType,
  }) async {
    _isUploading = true;
    _errorMessage = null;
    _uploadedFileUrl = null;
    _uploadedFileObjectKey = null;
    notifyListeners();

    try {
      // 上传文件到后端
      final response = await _fileUploadService.uploadFile(file);

      // 保存文件URL和对象键
      _uploadedFileUrl = response.fileUrl;
      _uploadedFileObjectKey = response.objectKey;
      _isUploading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _logger.e('上传文件失败: $e');
      _errorMessage = '上传文件失败: $e';
      _isUploading = false;
      notifyListeners();

      return false;
    }
  }

  /// 清除已上传的文件
  void clearUploadedFile() {
    _uploadedFileUrl = null;
    _uploadedFileObjectKey = null;
    notifyListeners();
  }
}
