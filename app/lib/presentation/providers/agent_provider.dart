import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/services/agent_service.dart';
import 'package:one_moment_app/data/services/tongyi_service.dart';

/// Agent状态
enum AgentStatus {
  /// 初始状态
  initial,

  /// 加载中
  loading,

  /// 加载成功
  loaded,

  /// 加载失败
  error,
}

/// Agent提供者
class AgentProvider with ChangeNotifier {
  late final AgentService _agentService;
  late final TongyiService _tongyiService;
  final LoggerUtil _logger = LoggerUtil();

  AgentStatus _status = AgentStatus.initial;
  List<AgentConfigModel> _agentConfigs = [];
  List<AgentChatSessionModel> _chatSessions = [];
  AgentChatSessionModel? _currentSession;
  String? _errorMessage;
  bool _isAiResponding = false;

  /// 构造函数
  AgentProvider({
    AgentService? agentService,
    TongyiService? tongyiService,
    ApiClient? apiClient
  }) {
    if (agentService != null) {
      _agentService = agentService;
    } else if (apiClient != null) {
      _agentService = AgentService(apiClient: apiClient);
    } else {
      _agentService = AgentService();
    }

    if (tongyiService != null) {
      _tongyiService = tongyiService;
    } else if (apiClient != null) {
      _tongyiService = TongyiService(apiClient: apiClient);
    } else {
      // 使用默认的TongyiService
      _tongyiService = TongyiService(apiClient: ApiClient(localStorage: LocalStorage()));
    }
  }

  /// AI是否正在响应
  bool get isAiResponding => _isAiResponding;

  /// 获取状态
  AgentStatus get status => _status;

  /// 获取Agent配置列表
  List<AgentConfigModel> get agentConfigs => _agentConfigs;

  /// 获取聊天会话列表
  List<AgentChatSessionModel> get chatSessions => _chatSessions;

  /// 获取当前会话
  AgentChatSessionModel? get currentSession => _currentSession;

  /// 获取错误信息
  String? get errorMessage => _errorMessage;

  /// 获取所有Agent配置
  Future<void> fetchAgentConfigs() async {
    _status = AgentStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('获取所有Agent配置');

      // 在实际应用中，这里应该调用后端API
      // final configs = await _agentService.getAllAgentConfigs();

      // 使用模拟数据
      final configs = _agentService.getMockAgentConfigs();

      _agentConfigs = configs;
      _status = AgentStatus.loaded;
    } catch (e) {
      _logger.e('获取Agent配置失败', e);
      _errorMessage = '获取Agent配置失败，请稍后重试';
      _status = AgentStatus.error;
    }

    notifyListeners();
  }

  /// 获取用户的所有聊天会话
  Future<void> fetchUserChatSessions(int userId) async {
    _status = AgentStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('获取用户聊天会话: userId=$userId');

      // 在实际应用中，这里应该调用后端API
      // final sessions = await _agentService.getUserChatSessions(userId);

      // 使用模拟数据
      final sessions = _agentService.getMockUserChatSessions(userId);

      _chatSessions = sessions;
      _status = AgentStatus.loaded;
    } catch (e) {
      _logger.e('获取用户聊天会话失败', e);
      _errorMessage = '获取聊天会话失败，请稍后重试';
      _status = AgentStatus.error;
    }

    notifyListeners();
  }

  /// 获取聊天会话详情
  Future<void> fetchChatSessionDetail(int sessionId) async {
    _status = AgentStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('获取聊天会话详情: sessionId=$sessionId');

      // 在实际应用中，这里应该调用后端API
      // final session = await _agentService.getChatSessionDetail(sessionId);

      // 使用模拟数据
      final session = _agentService.getMockChatSessionDetail(sessionId);

      _currentSession = session;
      _status = AgentStatus.loaded;
    } catch (e) {
      _logger.e('获取聊天会话详情失败', e);
      _errorMessage = '获取聊天会话详情失败，请稍后重试';
      _status = AgentStatus.error;
    }

    notifyListeners();
  }

  /// 创建聊天会话
  Future<AgentChatSessionModel?> createChatSession({
    required int userId,
    required int agentId,
    required String title,
  }) async {
    _status = AgentStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('创建聊天会话: userId=$userId, agentId=$agentId, title=$title');

      // 在实际应用中，这里应该调用后端API
      // final session = await _agentService.createChatSession(
      //   userId: userId,
      //   agentId: agentId,
      //   title: title,
      // );

      // 使用模拟数据
      final agent = _agentConfigs.firstWhere((a) => a.id == agentId);
      final session = AgentChatSessionModel(
        id: _chatSessions.length + 1,
        userId: userId,
        agentId: agentId,
        title: title,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        agent: agent,
        messages: [
          AgentChatMessageModel(
            id: 1,
            sessionId: _chatSessions.length + 1,
            role: MessageRole.system,
            content: '欢迎使用One Moment的AI助手功能，我将尽力帮助你解决问题。',
            createdAt: DateTime.now(),
          ),
        ],
      );

      _chatSessions.add(session);
      _currentSession = session;
      _status = AgentStatus.loaded;

      notifyListeners();
      return session;
    } catch (e) {
      _logger.e('创建聊天会话失败', e);
      _errorMessage = '创建聊天会话失败，请稍后重试';
      _status = AgentStatus.error;

      notifyListeners();
      return null;
    }
  }

  /// 发送聊天消息
  Future<AgentChatMessageModel?> sendChatMessage({
    required String content,
  }) async {
    if (_currentSession == null) {
      _errorMessage = '当前没有活动的聊天会话';
      notifyListeners();
      return null;
    }

    try {
      _logger.i('发送聊天消息: sessionId=${_currentSession!.id}, content=$content');

      // 创建用户消息
      final userMessage = AgentChatMessageModel(
        id: _currentSession!.messages?.length ?? 0 + 1,
        sessionId: _currentSession!.id,
        role: MessageRole.user,
        content: content,
        createdAt: DateTime.now(),
      );

      // 添加到当前会话
      _currentSession!.messages?.add(userMessage);
      notifyListeners();

      // 设置AI响应状态
      _isAiResponding = true;
      notifyListeners();

      // 调用通义千问API
      final messages = _currentSession!.messages ?? [];
      final modelName = _currentSession!.agent?.modelName ?? 'qwen-turbo';

      String aiResponse = ''; // 初始化为空字符串
      try {
        _logger.d('准备调用通义千问API，消息数量: ${messages.length}');

        // 添加重试逻辑
        int retryCount = 0;
        const maxRetries = 2;
        Exception? lastException;

        while (retryCount <= maxRetries) {
          try {
            String response = await _tongyiService.sendMessage(
              messages: messages,
              modelName: modelName,
            );

            // 检查响应是否有效
            if (response.isNotEmpty) {
              aiResponse = response; // 赋值给aiResponse
              _logger.d('成功获取AI响应: ${aiResponse.substring(0, aiResponse.length > 50 ? 50 : aiResponse.length)}...');
              break; // 成功获取响应，跳出循环
            } else {
              throw Exception('AI响应为空');
            }
          } catch (e) {
            lastException = e as Exception;
            _logger.w('调用通义千问API失败 (尝试 ${retryCount + 1}/${maxRetries + 1}): $e');
            retryCount++;

            if (retryCount <= maxRetries) {
              // 等待一段时间后重试
              await Future.delayed(Duration(milliseconds: 500 * retryCount));
            }
          }
        }

        // 如果所有重试都失败，则抛出最后一个异常
        if (retryCount > maxRetries) {
          throw lastException ?? Exception('调用通义千问API失败，已重试 $maxRetries 次');
        }
      } catch (e) {
        _logger.e('调用通义千问API最终失败', e);
        aiResponse = '抱歉，我暂时无法回应您的消息。请稍后再试。(错误: ${e.toString()})';
      } finally {
        _isAiResponding = false;
      }

      // 创建AI回复消息
      final aiMessage = AgentChatMessageModel(
        id: _currentSession!.messages?.length ?? 0 + 1,
        sessionId: _currentSession!.id,
        role: MessageRole.assistant,
        content: aiResponse,
        createdAt: DateTime.now(),
      );

      // 添加到当前会话
      _currentSession!.messages?.add(aiMessage);
      notifyListeners();

      return aiMessage;
    } catch (e) {
      _logger.e('发送聊天消息失败', e);
      _errorMessage = '发送聊天消息失败，请稍后重试';
      _isAiResponding = false;

      // 即使发生错误，也创建一个错误消息并添加到会话中
      final errorMessage = AgentChatMessageModel(
        id: _currentSession!.messages?.length ?? 0 + 1,
        sessionId: _currentSession!.id,
        role: MessageRole.assistant,
        content: '抱歉，我暂时无法回应您的消息。请稍后再试。(错误: ${e.toString()})',
        createdAt: DateTime.now(),
      );

      // 添加到当前会话
      _currentSession!.messages?.add(errorMessage);

      notifyListeners();
      return errorMessage;
    }
  }



  /// 设置当前会话
  void setCurrentSession(AgentChatSessionModel session) {
    _currentSession = session;
    notifyListeners();
  }

  /// 清除当前会话
  void clearCurrentSession() {
    _currentSession = null;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _status = AgentStatus.initial;
    _agentConfigs = [];
    _chatSessions = [];
    _currentSession = null;
    _errorMessage = null;
    notifyListeners();
  }
}
