import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/foundation.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/auth_model.dart';
import 'package:one_moment_app/data/services/auth_service.dart';

/// 认证状态
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 认证Provider
class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  final LoggerUtil _logger = LoggerUtil();

  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;
  bool _isLoading = false;

  AuthProvider({required AuthService authService}) : _authService = authService {
    _logger.i('初始化认证Provider');
    _checkAuthStatus();
  }

  // Getters
  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated;

  /// 检查认证状态
  Future<void> _checkAuthStatus() async {
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        _user = await _authService.getUserInfo();
        _state = AuthState.authenticated;
      } else {
        _state = AuthState.unauthenticated;
      }
    } catch (e) {
      _logger.e('检查认证状态失败', e);
      _state = AuthState.unauthenticated;
    }
    notifyListeners();
  }

  /// 用户注册
  Future<bool> register({
    required String phone,
    required String password,
    String? nickname,
    String? email,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = RegisterRequest(
        phone: phone,
        password: password,
        nickname: nickname,
        email: email,
      );

      final response = await _authService.register(request);
      _user = response.user;
      _state = AuthState.authenticated;
      _setLoading(false);
      
      _logger.i('用户注册成功: ${_user?.username}');
      return true;
    } catch (e) {
      _logger.e('用户注册失败', e);
      _setError(e.toString());
      _state = AuthState.error;
      _setLoading(false);
      return false;
    }
  }

  /// 手机号登录
  Future<bool> loginWithPhone({
    required String phone,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = LoginRequest(
        loginType: 'phone',
        phone: phone,
        password: password,
      );

      final response = await _authService.login(request);
      _user = response.user;
      _state = AuthState.authenticated;
      _setLoading(false);
      
      _logger.i('用户登录成功: ${_user?.username}');
      return true;
    } catch (e) {
      _logger.e('用户登录失败', e);
      _setError(e.toString());
      _state = AuthState.error;
      _setLoading(false);
      return false;
    }
  }

  /// 微信登录
  Future<bool> loginWithWechat({
    required String wechatCode,
    String? wechatUserInfo,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = LoginRequest(
        loginType: 'wechat',
        wechatCode: wechatCode,
        wechatUserInfo: wechatUserInfo,
      );

      final response = await _authService.login(request);
      _user = response.user;
      _state = AuthState.authenticated;
      _setLoading(false);
      
      _logger.i('微信登录成功: ${_user?.username}');
      return true;
    } catch (e) {
      _logger.e('微信登录失败', e);
      _setError(e.toString());
      _state = AuthState.error;
      _setLoading(false);
      return false;
    }
  }

  /// 刷新令牌
  Future<bool> refreshToken() async {
    try {
      final response = await _authService.refreshToken();
      _user = response.user;
      _state = AuthState.authenticated;
      notifyListeners();
      
      _logger.i('令牌刷新成功');
      return true;
    } catch (e) {
      _logger.e('令牌刷新失败', e);
      await logout();
      return false;
    }
  }

  /// 检查AI功能登录状态
  Future<Map<String, dynamic>> checkAILoginStatus() async {
    try {
      return await _authService.checkAILoginStatus();
    } catch (e) {
      _logger.e('检查AI登录状态失败', e);
      return {'isLoggedIn': false, 'loginRequired': true, 'message': 'AI对话功能需要登录'};
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      await _authService.logout();
      _user = null;
      _state = AuthState.unauthenticated;
      _clearError();
      notifyListeners();
      
      _logger.i('用户登出成功');
    } catch (e) {
      _logger.e('用户登出失败', e);
    }
  }

  /// 初始化测试账号
  Future<String> initTestAccount() async {
    try {
      return await _authService.initTestAccount();
    } catch (e) {
      _logger.e('初始化测试账号失败', e);
      rethrow;
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
