import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/weather_model.dart';
import 'package:one_moment_app/data/services/weather_service.dart';

/// 天气状态
enum WeatherStatus {
  /// 初始状态
  initial,

  /// 加载中
  loading,

  /// 加载成功
  loaded,

  /// 加载失败
  error,
}

/// 天气提供者
class WeatherProvider with ChangeNotifier {
  late final WeatherService _weatherService;
  final LoggerUtil _logger = LoggerUtil();

  WeatherStatus _status = WeatherStatus.initial;
  WeatherModel? _weather;
  String? _errorMessage;

  /// 构造函数
  WeatherProvider({WeatherService? weatherService, ApiClient? apiClient}) {
    if (weatherService != null) {
      _weatherService = weatherService;
    } else if (apiClient != null) {
      _weatherService = WeatherService(apiClient: apiClient);
    } else {
      _weatherService = WeatherService();
    }
  }

  /// 获取天气状态
  WeatherStatus get status => _status;

  /// 获取天气数据
  WeatherModel? get weather => _weather;

  /// 获取错误信息
  String? get errorMessage => _errorMessage;

  /// 获取最新天气
  Future<void> fetchLatestWeather({String cityName = '北京'}) async {
    _status = WeatherStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('获取最新天气: $cityName');
      final weather = await _weatherService.getLatestWeather(cityName: cityName);

      if (weather != null) {
        _weather = weather;
        _status = WeatherStatus.loaded;
      } else {
        _errorMessage = '无法获取天气信息，请检查网络连接或定位权限';
        _status = WeatherStatus.error;
      }
    } catch (e) {
      _logger.e('获取天气失败', e);
      _errorMessage = '获取天气信息失败，请稍后重试';
      _status = WeatherStatus.error;
    }

    notifyListeners();
  }

  /// 获取指定日期的天气
  Future<void> fetchWeatherByDate({
    required DateTime date,
    String cityName = '北京',
  }) async {
    _status = WeatherStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      _logger.i('获取指定日期天气: $cityName, ${date.toIso8601String().split('T')[0]}');
      final weather = await _weatherService.getWeatherByDate(
        cityName: cityName,
        date: date,
      );

      if (weather != null) {
        _weather = weather;
        _status = WeatherStatus.loaded;
      } else {
        _errorMessage = '无法获取指定日期的天气信息';
        _status = WeatherStatus.error;
      }
    } catch (e) {
      _logger.e('获取指定日期天气失败', e);
      _errorMessage = '获取天气信息失败，请稍后重试';
      _status = WeatherStatus.error;
    }

    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _status = WeatherStatus.initial;
    _weather = null;
    _errorMessage = null;
    notifyListeners();
  }


}
