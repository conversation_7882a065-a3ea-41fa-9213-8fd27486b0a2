import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/utils/mood_number_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';

/// 时间胶囊日历页面
class TimeCapsuleCalendarPage extends StatefulWidget {
  const TimeCapsuleCalendarPage({Key? key}) : super(key: key);

  @override
  State<TimeCapsuleCalendarPage> createState() => _TimeCapsuleCalendarPageState();
}

class _TimeCapsuleCalendarPageState extends State<TimeCapsuleCalendarPage> {
  final LoggerUtil _logger = LoggerUtil();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<MomentModel> _selectedDayMoments = [];
  Map<DateTime, List<MomentModel>> _monthlyMoments = {};
  bool _isLoading = false;

  // 心情动画相关状态
  bool _showMoodAnimation = false;
  double _moodAnimationOpacity = 0.0;
  String _selectedDayMoodIcon = '';

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _loadMonthlyMoments();
    _loadMomentsForSelectedDay();
  }

  /// 加载当月的所有moment数据
  Future<void> _loadMonthlyMoments() async {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);

      // 获取当月第一天和最后一天
      final firstDay = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final lastDay = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);

      // 清空当前月度数据
      _monthlyMoments.clear();

      // 逐日加载数据
      for (int day = 1; day <= lastDay.day; day++) {
        final date = DateTime(_focusedDay.year, _focusedDay.month, day);
        try {
          final moments = await momentProvider.fetchMomentsByDate(date);
          if (moments.isNotEmpty) {
            _monthlyMoments[DateTime(date.year, date.month, date.day)] = moments;
          }
        } catch (e) {
          _logger.e('加载日期 $date 的moment失败: $e');
        }
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      _logger.e('加载月度moment数据失败: $e');
    }
  }

  /// 加载选定日期的时刻
  Future<void> _loadMomentsForSelectedDay() async {
    if (_selectedDay == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      _logger.i('加载选定日期的时刻: ${_selectedDay.toString()}');

      // 使用MomentProvider获取指定日期的时刻数据
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);

      // 使用Future.delayed避免在build过程中调用setState
      Future.delayed(Duration.zero, () async {
        try {
          final moments = await momentProvider.fetchMomentsByDate(_selectedDay!);
          _logger.i('获取到 ${moments.length} 个时刻');

          if (mounted) {
            setState(() {
              _selectedDayMoments = moments;
              _isLoading = false;
            });
          }
        } catch (e) {
          _logger.e('加载时刻失败: $e');
          if (mounted) {
            setState(() {
              _selectedDayMoments = [];
              _isLoading = false;
            });
          }
        }
      });
    } catch (e) {
      _logger.e('加载时刻失败: $e');
      setState(() {
        _selectedDayMoments = [];
        _isLoading = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 直接展示日历，去掉Banner
          _buildCalendar(),

          // 心情动画显示区域
          if (_showMoodAnimation)
            _buildMoodAnimation(),

          // 时刻列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _selectedDayMoments.isEmpty
                    ? _buildEmptyState()
                    : _buildMomentsList(),
          ),
        ],
      ),
    );
  }

  /// 构建Banner
  Widget _buildBanner() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.15,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.7),
          ],
        ),
      ),
      child: SafeArea(
        child: Center(
          child: Text(
            '时间胶囊',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建心情动画
  Widget _buildMoodAnimation() {
    return Container(
      height: 120,
      width: double.infinity,
      child: Center(
        child: AnimatedOpacity(
          opacity: _moodAnimationOpacity,
          duration: const Duration(milliseconds: 800),
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.black.withOpacity(0.1),
            ),
            child: Center(
              child: Text(
                _selectedDayMoodIcon,
                style: const TextStyle(fontSize: 60),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示心情动画
  void _showMoodAnimationForDay(DateTime day) {
    final moments = _monthlyMoments[DateTime(day.year, day.month, day.day)] ?? [];

    if (moments.isNotEmpty) {
      // 获取最新moment的心情
      final latestMoment = moments.reduce((a, b) =>
        a.createdAt.isAfter(b.createdAt) ? a : b);

      setState(() {
        _showMoodAnimation = true;
        _moodAnimationOpacity = 1.0;
        _selectedDayMoodIcon = _getMoodEmoji(latestMoment.mood);
      });

      // 2秒后开始淡出
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _moodAnimationOpacity = 0.0;
          });
        }
      });

      // 3秒后隐藏动画容器
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _showMoodAnimation = false;
          });
        }
      });
    }
  }

  /// 获取心情对应的emoji
  String _getMoodEmoji(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return '😊';
      case MoodType.excited:
        return '🎉';
      case MoodType.calm:
        return '😌';
      case MoodType.sad:
        return '😢';
      case MoodType.angry:
        return '😠';
      case MoodType.anxious:
        return '😰';
      case MoodType.grateful:
        return '🙏';
      case MoodType.neutral:
      default:
        return '😐';
    }
  }

  /// 构建日历
  Widget _buildCalendar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TableCalendar(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        calendarFormat: _calendarFormat,
        availableCalendarFormats: const {
          CalendarFormat.month: '月',
          CalendarFormat.twoWeeks: '2周',
          CalendarFormat.week: '周',
        },
        startingDayOfWeek: StartingDayOfWeek.monday,
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        },
        onDaySelected: (selectedDay, focusedDay) {
          if (!isSameDay(_selectedDay, selectedDay)) {
            setState(() {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
            });
            _loadMomentsForSelectedDay();
            // 显示心情动画
            _showMoodAnimationForDay(selectedDay);
          }
        },
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          setState(() {
            _focusedDay = focusedDay;
          });
          _loadMonthlyMoments();
        },
        calendarBuilders: CalendarBuilders(
          defaultBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, false, false);
          },
          todayBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, true, false);
          },
          selectedBuilder: (context, day, focusedDay) {
            return _buildCalendarDay(day, false, true);
          },
        ),
        calendarStyle: CalendarStyle(
          todayDecoration: BoxDecoration(
            color: Colors.transparent,
          ),
          selectedDecoration: BoxDecoration(
            color: Colors.transparent,
          ),
          defaultDecoration: BoxDecoration(
            color: Colors.transparent,
          ),
          markerDecoration: BoxDecoration(
            color: Colors.transparent,
          ),
        ),
        headerStyle: HeaderStyle(
          formatButtonTextStyle: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 14.0,
          ),
          formatButtonDecoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.primary),
            borderRadius: BorderRadius.circular(16.0),
          ),
        ),
      ),
    );
  }

  /// 构建自定义日历日期
  Widget _buildCalendarDay(DateTime day, bool isToday, bool isSelected) {
    final dateKey = DateTime(day.year, day.month, day.day);
    final moments = _monthlyMoments[dateKey] ?? [];

    if (moments.isNotEmpty) {
      // 如果有多个moment，使用最新的一个的情绪
      final latestMoment = moments.reduce((a, b) =>
        a.createdAt.isAfter(b.createdAt) ? a : b);

      return MoodNumberUtil.buildMoodNumber(
        number: day.day,
        mood: latestMoment.mood,
        isSelected: isSelected,
        isToday: isToday,
        onTap: () {
          setState(() {
            _selectedDay = day;
            _focusedDay = day;
          });
          _loadMomentsForSelectedDay();
        },
      );
    } else {
      // 没有moment的日期使用普通数字
      return MoodNumberUtil.buildNormalNumber(
        number: day.day,
        isSelected: isSelected,
        isToday: isToday,
        onTap: () {
          setState(() {
            _selectedDay = day;
            _focusedDay = day;
          });
          _loadMomentsForSelectedDay();
        },
      );
    }
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _selectedDay != null
                ? '${DateFormat('yyyy年MM月dd日').format(_selectedDay!)}没有记录'
                : '没有记录',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '选择其他日期查看更多时刻',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建时刻列表
  Widget _buildMomentsList() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时刻列表
          Expanded(
            child: ListView.builder(
              itemCount: _selectedDayMoments.length,
              itemBuilder: (context, index) {
                final moment = _selectedDayMoments[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 时间和心情
                        Row(
                          children: [
                            Icon(
                              _getMoodIcon(moment.mood.toString().split('.').last),
                              size: 20,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('HH:mm').format(moment.createdAt),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),
                            if (moment.locationName != null) ...[
                              Icon(
                                Icons.location_on,
                                size: 16,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                moment.locationName!,
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 12),

                        // 内容
                        Text(
                          moment.content,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),

                        // 图片（如果有）
                        if (moment.contentType == 'image' && moment.mediaObjectKey != null) ...[
                          const SizedBox(height: 12),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: FutureBuilder<String?>(
                              future: _getImageUrl(moment.mediaObjectKey!),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Container(
                                    height: 200,
                                    width: double.infinity,
                                    color: Colors.grey.shade200,
                                    child: const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }

                                if (snapshot.hasError || !snapshot.hasData) {
                                  return Container(
                                    height: 200,
                                    width: double.infinity,
                                    color: Colors.grey.shade200,
                                    child: Center(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.image_not_supported,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            '图片加载失败',
                                            style: TextStyle(color: Colors.grey),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                }

                                return Image.network(
                                  snapshot.data!,
                                  height: 200,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      height: 200,
                                      width: double.infinity,
                                      color: Colors.grey.shade200,
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.image_not_supported,
                                              size: 48,
                                              color: Colors.grey,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              '图片加载失败',
                                              style: TextStyle(color: Colors.grey),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ],

                        // 天气信息（如果有）
                        if (moment.weather != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.wb_sunny,
                                size: 16,
                                color: Colors.orange,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                moment.weather!,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                              if (moment.temperature != null) ...[
                                const SizedBox(width: 8),
                                Text(
                                  '${moment.temperature!.toInt()}°C',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 获取心情图标
  IconData _getMoodIcon(String mood) {
    switch (mood) {
      case 'happy':
        return Icons.sentiment_very_satisfied;
      case 'calm':
        return Icons.sentiment_satisfied;
      case 'sad':
        return Icons.sentiment_dissatisfied;
      case 'excited':
        return Icons.mood;
      case 'anxious':
        return Icons.mood_bad;
      case 'neutral':
      default:
        return Icons.sentiment_neutral;
    }
  }

  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
    try {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      }

      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      });

      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      }
      return null;
    } catch (e) {
      _logger.e('获取图片URL失败: $e');
      return null;
    }
  }
}
