import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

/// 时间胶囊页面
class TimeCapsulePage extends StatefulWidget {
  const TimeCapsulePage({super.key});

  @override
  State<TimeCapsulePage> createState() => _TimeCapsulePageState();
}

class _TimeCapsulePageState extends State<TimeCapsulePage> {
  late final MomentService _momentService;
  final LoggerUtil _logger = LoggerUtil();
  bool _isLoading = true;
  Map<DateTime, List<MomentModel>> _events = {};
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<MomentModel> _selectedEvents = [];
  String _errorMessage = '';

  // 心情动画相关状态
  bool _showMoodIcon = false;
  double _moodIconOpacity = 0.0;
  MoodType _selectedDayMood = MoodType.neutral;

  @override
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _selectedDay = _focusedDay;
    _loadMonthEvents();
  }

  /// 加载月度瞬间
  Future<void> _loadMonthEvents() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // 获取当月的第一天和最后一天
      final firstDay = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final lastDay = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);

      _logger.i('加载月度瞬间: ${firstDay.toString()} - ${lastDay.toString()}');

      // 获取日期范围内的瞬间
      final moments = await _momentService.getMomentsByDateRange(firstDay, lastDay);

      _logger.i('获取到 ${moments.length} 个瞬间');

      // 按日期分组
      final events = <DateTime, List<MomentModel>>{};
      for (final moment in moments) {
        final date = DateTime(
          moment.createdAt.year,
          moment.createdAt.month,
          moment.createdAt.day,
        );

        if (events[date] == null) {
          events[date] = [];
        }

        events[date]!.add(moment);
        _logger.d('添加瞬间到日期 ${date.toString()}: ${moment.id}');
      }

      setState(() {
        _events = events;
        _isLoading = false;
        _updateSelectedEvents();
      });

      _logger.i('月度瞬间加载完成，共 ${events.length} 个日期有瞬间');
    } catch (e) {
      _logger.e('加载月度瞬间失败', e);
      setState(() {
        _errorMessage = '加载月度瞬间失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 更新选中日期的瞬间
  void _updateSelectedEvents() {
    if (_selectedDay != null) {
      final date = DateTime(
        _selectedDay!.year,
        _selectedDay!.month,
        _selectedDay!.day,
      );
      _selectedEvents = _events[date] ?? [];
    } else {
      _selectedEvents = [];
    }
  }

  /// 随机穿梭
  void _randomTravel() {
    // 获取所有有瞬间的日期
    final dates = _events.keys.toList();
    if (dates.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无可穿梭的日期')),
      );
      return;
    }

    // 随机选择一个日期
    final random = DateTime.now().millisecondsSinceEpoch % dates.length;
    final randomDate = dates[random];

    setState(() {
      _selectedDay = randomDate;
      _focusedDay = randomDate;
      _updateSelectedEvents();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时间胶囊'),
        actions: [
          IconButton(
            icon: const Icon(Icons.shuffle),
            onPressed: _randomTravel,
            tooltip: '随机穿梭',
          ),
        ],
      ),
      body: Column(
        children: [
          // 日历
          Card(
            margin: const EdgeInsets.all(8.0),
            child: TableCalendar<MomentModel>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) {
                return isSameDay(_selectedDay, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                  _updateSelectedEvents();
                });

                // 显示心情动画
                _showMoodAnimation(selectedDay);
              },
              onPageChanged: (focusedDay) {
                setState(() {
                  _focusedDay = focusedDay;
                });
                _loadMonthEvents();
              },
              eventLoader: (day) {
                final date = DateTime(day.year, day.month, day.day);
                return _events[date] ?? [];
              },
              calendarStyle: CalendarStyle(
                markersMaxCount: 3,
                markerDecoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                // 根据心情变换日期颜色
                todayDecoration: BoxDecoration(
                  color: _getTodayMoodColor(),
                  shape: BoxShape.circle,
                ),
                selectedDecoration: BoxDecoration(
                  color: _getSelectedDayMoodColor(),
                  shape: BoxShape.circle,
                ),
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: Theme.of(context).textTheme.titleLarge!,
              ),
            ),
          ),

          // 心情动画显示区域
          if (_showMoodIcon)
            Container(
              height: 80,
              child: Center(
                child: AnimatedOpacity(
                  opacity: _moodIconOpacity,
                  duration: Duration(milliseconds: 500),
                  child: Text(
                    _getMoodIcon(_selectedDayMood),
                    style: TextStyle(fontSize: 48),
                  ),
                ),
              ),
            ),

          // 选中日期的瞬间列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _selectedEvents.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.event_busy,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  '该日期没有记录',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(8.0),
                            itemCount: _selectedEvents.length,
                            itemBuilder: (context, index) {
                              return _buildMomentCard(_selectedEvents[index]);
                            },
                          ),
          ),
        ],
      ),
    );
  }

  /// 构建瞬间卡片
  Widget _buildMomentCard(MomentModel moment) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息和操作按钮
            Row(
              children: [
                CircleAvatar(
                  child: Text(moment.username.isNotEmpty ? moment.username[0] : '?'),
                ),
                const SizedBox(width: 8),
                Text(
                  moment.username,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                // 编辑按钮
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  tooltip: '编辑',
                  onPressed: () => _editMoment(moment),
                ),
                // 删除按钮
                IconButton(
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                  tooltip: '删除',
                  onPressed: () => _showDeleteConfirmation(moment),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 时间
            Text(
              DateFormat('yyyy年MM月dd日 HH:mm').format(moment.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
            ),
            const SizedBox(height: 8),

            // 内容
            Text(
              moment.textContent,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),

            // 媒体
            if (moment.mediaType != MediaType.none && moment.mediaObjectKey != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: FadeInImage.assetNetwork(
                  placeholder: 'assets/images/placeholder.png',
                  image: moment.mediaProxyUrl ?? '',
                  height: 150, // 减小高度，使页面更紧凑
                  width: double.infinity,
                  fit: BoxFit.cover,
                  imageErrorBuilder: (context, error, stackTrace) {
                    _logger.e('加载图片失败: $error');
                    return Container(
                      height: 150,
                      width: double.infinity,
                      color: Colors.grey[300],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 48,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: const TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

            // 位置和天气
            if (moment.locationName != null || moment.weather != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    if (moment.locationName != null) ...[
                      const Icon(Icons.location_on, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        moment.locationName!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                      const SizedBox(width: 16),
                    ],
                    if (moment.weather != null) ...[
                      const Icon(Icons.cloud, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${moment.weather!}${moment.temperature != null ? ' ${moment.temperature!.toStringAsFixed(1)}°C' : ''}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 编辑瞬间
  void _editMoment(MomentModel moment) async {
    _logger.i('编辑瞬间: ${moment.id}');

    // 跳转到编辑页面，并传递瞬间数据
    final result = await context.pushNamed(
      'moment-edit',
      extra: moment,
      pathParameters: {'id': moment.id.toString()},
    );

    // 如果编辑成功，重新加载数据
    if (result == true) {
      _loadMonthEvents();
    }
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmation(MomentModel moment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条瞬间记录吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteMoment(moment);
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 删除瞬间
  void _deleteMoment(MomentModel moment) async {
    try {
      _logger.i('删除瞬间: ${moment.id}');

      // 显示加载指示器
      setState(() {
        _isLoading = true;
      });

      // 调用删除API
      await _momentService.deleteMoment(moment.id);

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('瞬间已删除')),
        );
      }

      // 重新加载数据
      _loadMonthEvents();
    } catch (e) {
      _logger.e('删除瞬间失败', e);

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );

        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示心情动画
  void _showMoodAnimation(DateTime selectedDay) {
    // 获取选中日期的心情
    final date = DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final dayMoments = _events[date] ?? [];

    if (dayMoments.isNotEmpty) {
      // 使用第一个瞬间的心情，或者可以计算平均心情
      _selectedDayMood = dayMoments.first.mood;

      setState(() {
        _showMoodIcon = true;
        _moodIconOpacity = 1.0;
      });

      // 2秒后开始淡出
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _moodIconOpacity = 0.0;
          });

          // 淡出完成后隐藏
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted) {
              setState(() {
                _showMoodIcon = false;
              });
            }
          });
        }
      });
    }
  }

  /// 获取今日心情颜色
  Color _getTodayMoodColor() {
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    final todayMoments = _events[todayDate] ?? [];

    if (todayMoments.isNotEmpty) {
      return _getMoodColor(todayMoments.first.mood);
    }

    return Theme.of(context).colorScheme.primary;
  }

  /// 获取选中日期心情颜色
  Color _getSelectedDayMoodColor() {
    if (_selectedDay == null) return Theme.of(context).colorScheme.primary;

    final date = DateTime(_selectedDay!.year, _selectedDay!.month, _selectedDay!.day);
    final dayMoments = _events[date] ?? [];

    if (dayMoments.isNotEmpty) {
      return _getMoodColor(dayMoments.first.mood);
    }

    return Theme.of(context).colorScheme.primary;
  }

  /// 根据心情获取颜色
  Color _getMoodColor(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return Colors.orange; // 开心 - 橙色
      case MoodType.excited:
        return Colors.red; // 兴奋 - 红色
      case MoodType.calm:
        return Colors.blue; // 平静 - 蓝色
      case MoodType.sad:
        return Colors.grey; // 难过 - 灰色
      case MoodType.anxious:
        return Colors.purple; // 焦虑 - 紫色
      case MoodType.neutral:
      default:
        return Colors.green; // 一般 - 绿色
    }
  }

  /// 根据心情获取图标
  String _getMoodIcon(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return '😊'; // 开心
      case MoodType.excited:
        return '🤩'; // 兴奋
      case MoodType.calm:
        return '😌'; // 平静
      case MoodType.sad:
        return '😢'; // 难过
      case MoodType.anxious:
        return '😰'; // 焦虑
      case MoodType.neutral:
      default:
        return '😐'; // 一般
    }
  }
}
