import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
import 'package:provider/provider.dart';

/// 瞬间编辑页面
class MomentEditPage extends StatefulWidget {
  final MomentModel moment;

  const MomentEditPage({super.key, required this.moment});

  @override
  State<MomentEditPage> createState() => _MomentEditPageState();
}

class _MomentEditPageState extends State<MomentEditPage> {
  final LoggerUtil _logger = LoggerUtil();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isLoading = false;
  File? _selectedImage;
  String? _weather;
  String? _location;
  String _selectedMood = 'neutral';
  String _selectedMediaType = 'none';

  late MomentService _momentService;

  @override
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);

    // 初始化表单数据
    _contentController.text = widget.moment.textContent;
    _locationController.text = widget.moment.locationName ?? '';
    _location = widget.moment.locationName;
    _weather = widget.moment.weather;
    _selectedMood = widget.moment.mood.toString().split('.').last;
    _selectedMediaType = widget.moment.mediaType.toString().split('.').last;

    _logger.i('初始化瞬间编辑页面: ${widget.moment.id}');
  }

  @override
  void dispose() {
    _contentController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑瞬间'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateMoment,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('保存'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 创建时间
                  Text(
                    '创建于 ${DateFormat('yyyy年MM月dd日 HH:mm').format(widget.moment.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                        ),
                  ),
                  const SizedBox(height: 16),

                  // 内容输入
                  TextField(
                    controller: _contentController,
                    maxLines: 5,
                    decoration: const InputDecoration(
                      hintText: '记录此刻的想法...',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 媒体展示和选择
                  _buildMediaSection(),
                  const SizedBox(height: 16),

                  // 位置输入
                  TextField(
                    controller: _locationController,
                    decoration: const InputDecoration(
                      labelText: '位置',
                      hintText: '添加位置',
                      prefixIcon: Icon(Icons.location_on),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _location = value.isEmpty ? null : value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // 天气展示
                  if (_weather != null)
                    Card(
                      child: ListTile(
                        leading: const Icon(Icons.cloud),
                        title: Text('天气: $_weather'),
                        trailing: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _weather = null;
                            });
                          },
                        ),
                      ),
                    ),
                  const SizedBox(height: 16),

                  // 心情选择
                  Text(
                    '心情',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  _buildMoodSelector(),
                ],
              ),
            ),
    );
  }

  /// 构建媒体部分
  Widget _buildMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '媒体',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),

        // 当前媒体展示
        if (_selectedMediaType == 'image' && widget.moment.mediaObjectKey != null && widget.moment.mediaObjectKey!.isNotEmpty && _selectedImage == null)
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: FutureBuilder<String?>(
                  future: _getImageUrl(widget.moment.mediaObjectKey!),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[100],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    if (snapshot.hasError || !snapshot.hasData) {
                      return Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                '图片加载失败',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return Image.network(
                      snapshot.data!,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        _logger.e('加载图片失败: $error');
                        return Container(
                          height: 200,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _selectedMediaType = 'none';
                    });
                  },
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.black54,
                  ),
                ),
              ),
            ],
          ),

        // 新选择的图片
        if (_selectedImage != null)
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  _selectedImage!,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _selectedImage = null;
                    });
                  },
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.black54,
                  ),
                ),
              ),
            ],
          ),

        const SizedBox(height: 8),

        // 媒体选择按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.photo_library),
              label: const Text('从相册选择'),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: _takePhoto,
              icon: const Icon(Icons.camera_alt),
              label: const Text('拍照'),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建心情选择器
  Widget _buildMoodSelector() {
    return Wrap(
      spacing: 8,
      children: [
        _buildMoodChip('happy', '开心'),
        _buildMoodChip('calm', '平静'),
        _buildMoodChip('sad', '难过'),
        _buildMoodChip('excited', '兴奋'),
        _buildMoodChip('anxious', '焦虑'),
        _buildMoodChip('neutral', '一般'),
      ],
    );
  }

  /// 构建心情选择芯片
  Widget _buildMoodChip(String mood, String label) {
    final isSelected = _selectedMood == mood;
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedMood = mood;
          });
        }
      },
    );
  }

  /// 从相册选择图片
  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
          _selectedMediaType = 'image';
        });
      }
    } catch (e) {
      _logger.e('选择图片失败', e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
      );
    }
  }

  /// 拍照
  Future<void> _takePhoto() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
          _selectedMediaType = 'image';
        });
      }
    } catch (e) {
      _logger.e('拍照失败', e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('拍照失败: $e')),
      );
    }
  }

  /// 更新瞬间
  Future<void> _updateMoment() async {
    // 验证内容
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入内容')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 直接使用MomentService更新瞬间
      await _momentService.updateMoment(
        widget.moment.id,
        _contentController.text.trim(),
        location: _location,
        weather: _weather,
        mood: _getMoodTypeFromString(_selectedMood),
        isPrivate: widget.moment.isPrivate,
      );

      // 更新成功，显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('瞬间更新成功！'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // 等待提示显示后再返回
        await Future.delayed(const Duration(seconds: 1));

        // 返回上一页
        if (mounted) {
          context.pop(true);
        }
      }
    } catch (e) {
      _logger.e('更新瞬间失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('更新失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
    try {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      }

      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      });

      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      }
      return null;
    } catch (e) {
      _logger.e('获取图片URL失败: $e');
      return null;
    }
  }

  /// 从字符串获取心情类型
  MoodType _getMoodTypeFromString(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return MoodType.happy;
      case 'calm':
        return MoodType.calm;
      case 'sad':
        return MoodType.sad;
      case 'excited':
        return MoodType.excited;
      case 'anxious':
        return MoodType.anxious;
      default:
        return MoodType.neutral;
    }
  }
}
