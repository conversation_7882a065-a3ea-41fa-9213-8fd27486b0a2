import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/media_type.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/presentation/pages/edit_moment_page.dart';

/// 瞬间详情页面
class MomentDetailPage extends StatefulWidget {
  final String momentId;

  const MomentDetailPage({
    super.key,
    required this.momentId,
  });

  @override
  State<MomentDetailPage> createState() => _MomentDetailPageState();
}

class _MomentDetailPageState extends State<MomentDetailPage> {
  final LoggerUtil _logger = LoggerUtil();
  late final MomentService _momentService;
  bool _isLoading = true;
  bool _isDeleting = false;
  MomentModel? _moment;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    _momentService = MomentService(apiClient: apiClient);
    _loadMoment();
  }

  /// 加载瞬间
  Future<void> _loadMoment() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final moment = await _momentService.getMomentById(widget.momentId);

      setState(() {
        _moment = moment;
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('加载瞬间失败', e);
      setState(() {
        _errorMessage = '加载瞬间失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 删除瞬间
  Future<void> _deleteMoment() async {
    try {
      setState(() {
        _isDeleting = true;
      });

      await _momentService.deleteMoment(widget.momentId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('瞬间已删除'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // 返回true表示已删除
      }
    } catch (e) {
      _logger.e('删除瞬间失败', e);
      setState(() {
        _isDeleting = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除瞬间失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 编辑瞬间
  Future<void> _editMoment() async {
    if (_moment == null) return;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => EditMomentPage(moment: _moment!),
      ),
    );

    if (result == true) {
      _loadMoment();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('瞬间详情'),
        actions: [
          if (_moment != null && !_isLoading && !_isDeleting)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _editMoment,
              tooltip: '编辑',
            ),
          if (_moment != null && !_isLoading && !_isDeleting)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmDialog();
              },
              tooltip: '删除',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMoment,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_moment == null) {
      return const Center(
        child: Text('瞬间不存在'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期和时间
          Text(
            DateFormat('yyyy年MM月dd日 HH:mm').format(_moment!.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                ),
          ),
          const SizedBox(height: 16),

          // 内容
          Text(
            _moment!.content,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),

          // 媒体内容
          if (_moment!.mediaType != MediaType.none && _moment!.mediaPath != null)
            _buildMediaContent(),

          const SizedBox(height: 16),

          // 位置和天气
          if (_moment!.location != null || _moment!.weather != null)
            Row(
              children: [
                if (_moment!.location != null)
                  Chip(
                    avatar: const Icon(Icons.location_on, size: 16),
                    label: Text(_moment!.location!),
                  ),
                const SizedBox(width: 8),
                if (_moment!.weather != null)
                  Chip(
                    avatar: const Icon(Icons.cloud, size: 16),
                    label: Text(_moment!.weather!),
                  ),
              ],
            ),

          const SizedBox(height: 16),

          // 心情
          _buildMoodChip(_getMoodString(_moment!.mood)),

          const SizedBox(height: 16),

          // 私密标记
          if (_moment!.isPrivate)
            const Chip(
              avatar: Icon(Icons.lock, size: 16),
              label: Text('私密'),
            ),
        ],
      ),
    );
  }

  /// 构建媒体内容
  Widget _buildMediaContent() {
    switch (_moment!.mediaType) {
      case MediaType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: FadeInImage.assetNetwork(
            placeholder: 'assets/images/placeholder.png',
            image: _moment!.mediaPath!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: 200,
            imageErrorBuilder: (context, error, stackTrace) {
              _logger.e('加载图片失败: $error');
              return Container(
                width: double.infinity,
                height: 200,
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(
                    Icons.broken_image,
                    size: 64,
                    color: Colors.grey,
                  ),
                ),
              );
            },
          ),
        );
      case MediaType.video:
        return Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(
              Icons.play_circle_filled,
              size: 64,
              color: Colors.grey,
            ),
          ),
        );
      case MediaType.audio:
        return Container(
          width: double.infinity,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(
              Icons.audiotrack,
              size: 48,
              color: Colors.grey,
            ),
          ),
        );
      case MediaType.none:
      default:
        return const SizedBox.shrink();
    }
  }

  /// 构建心情标签
  Widget _buildMoodChip(String mood) {
    IconData iconData;
    Color color;

    switch (mood) {
      case '开心':
        iconData = Icons.sentiment_very_satisfied;
        color = Colors.amber;
        break;
      case '平静':
        iconData = Icons.sentiment_satisfied;
        color = Colors.green;
        break;
      case '难过':
        iconData = Icons.sentiment_dissatisfied;
        color = Colors.blue;
        break;
      case '兴奋':
        iconData = Icons.mood;
        color = Colors.orange;
        break;
      case '焦虑':
        iconData = Icons.mood_bad;
        color = Colors.purple;
        break;
      case '中性':
      default:
        iconData = Icons.sentiment_neutral;
        color = Colors.grey;
        break;
    }

    return Chip(
      avatar: Icon(iconData, color: color, size: 18),
      label: Text(mood),
      backgroundColor: color.withOpacity(0.1),
    );
  }

  /// 获取心情字符串
  String _getMoodString(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return '开心';
      case MoodType.calm:
        return '平静';
      case MoodType.sad:
        return '难过';
      case MoodType.excited:
        return '兴奋';
      case MoodType.anxious:
        return '焦虑';
      case MoodType.neutral:
      default:
        return '中性';
    }
  }

  /// 显示删除确认对话框
  Future<void> _showDeleteConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条瞬间吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              '删除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (result == true) {
      await _deleteMoment();
    }
  }
}
