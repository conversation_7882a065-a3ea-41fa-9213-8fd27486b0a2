import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/data/models/agent_chat_message_model.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/services/agent_service.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';

/// Agent聊天页面
class AgentChatPage extends StatefulWidget {
  /// 会话ID
  final int? sessionId;

  /// 代理ID
  final int? agentId;

  /// 构造函数
  const AgentChatPage({
    Key? key,
    this.sessionId,
    this.agentId,
  }) : super(key: key);

  @override
  State<AgentChatPage> createState() => _AgentChatPageState();
}

class _AgentChatPageState extends State<AgentChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadChatSession();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载聊天会话
  Future<void> _loadChatSession() async {
    final provider = context.read<AgentProvider>();

    setState(() {
      _isLoading = true;
    });

    if (widget.sessionId != null) {
      // 加载现有会话
      await provider.fetchChatSessionDetail(widget.sessionId!);
    } else if (widget.agentId != null) {
      // 创建新会话
      await provider.createChatSession(
        userId: 1, // 假设用户ID为1
        agentId: widget.agentId!,
        title: '新的对话',
      );
    }

    setState(() {
      _isLoading = false;
    });

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    _messageController.clear();

    final provider = context.read<AgentProvider>();
    await provider.sendChatMessage(content: message);

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<AgentProvider>(
          builder: (context, provider, child) {
            final session = provider.currentSession;
            return Text(session?.agent?.name ?? '聊天');
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // 显示Agent信息
              final provider = context.read<AgentProvider>();
              final agent = provider.currentSession?.agent;
              if (agent != null) {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(agent.name),
                    content: Text(agent.description ?? ''),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('关闭'),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: Consumer<AgentProvider>(
                    builder: (context, provider, child) {
                      final session = provider.currentSession;
                      final messages = session?.messages ?? [];

                      if (messages.isEmpty) {
                        return const Center(
                          child: Text('开始新的对话吧！'),
                        );
                      }

                      return ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          return _buildMessageItem(message);
                        },
                      );
                    },
                  ),
                ),
                _buildInputArea(),
              ],
            ),
    );
  }

  /// 构建消息项
  Widget _buildMessageItem(AgentChatMessageModel message) {
    final isUser = message.role == MessageRole.user;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(message),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser ? Colors.white : null,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isUser) _buildAvatar(message),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(AgentChatMessageModel message) {
    final provider = context.read<AgentProvider>();
    final session = provider.currentSession;
    final agent = session?.agent;

    if (message.role == MessageRole.user) {
      return CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: const Icon(Icons.person, color: Colors.white),
      );
    } else {
      return CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        child: agent?.avatar != null
            ? Image.asset(agent!.avatar!)
            : const Icon(Icons.smart_toy, color: Colors.white),
      );
    }
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Consumer<AgentProvider>(
      builder: (context, provider, child) {
        final isResponding = provider.isAiResponding;

        return Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isResponding)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'AI正在思考中...',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      enabled: !isResponding,
                      decoration: InputDecoration(
                        hintText: isResponding ? '请等待AI回复...' : '输入消息...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      textInputAction: TextInputAction.send,
                      onSubmitted: isResponding ? null : (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    onPressed: isResponding ? null : _sendMessage,
                    mini: true,
                    child: isResponding
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.send),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
