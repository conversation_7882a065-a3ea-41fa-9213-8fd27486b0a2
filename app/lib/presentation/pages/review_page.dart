import 'package:flutter/material.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/core/storage/database_service.dart';
import 'package:one_moment_app/presentation/widgets/moment_detail_card.dart';

/// 回顾页面
class ReviewPage extends StatefulWidget {
  const ReviewPage({super.key});

  @override
  State<ReviewPage> createState() => _ReviewPageState();
}

class _ReviewPageState extends State<ReviewPage> {
  late DateTime _selectedDate;
  List<MomentModel> _moments = [];
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _loadMoments();
  }
  
  /// 加载指定日期的瞬间
  Future<void> _loadMoments() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      final dbService = await DatabaseService.instance;
      final moments = await dbService.getMomentsByDate(_selectedDate);
      
      setState(() {
        _moments = moments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }
  
  /// 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      
      _loadMoments();
    }
  }
  
  /// 上一天
  void _previousDay() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
    });
    
    _loadMoments();
  }
  
  /// 下一天
  void _nextDay() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    if (_selectedDate.isBefore(tomorrow)) {
      setState(() {
        _selectedDate = _selectedDate.add(const Duration(days: 1));
      });
      
      _loadMoments();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('回顾'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 日期选择器
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: _previousDay,
                ),
                GestureDetector(
                  onTap: _selectDate,
                  child: Text(
                    '${_selectedDate.year}年${_selectedDate.month}月${_selectedDate.day}日',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: _selectedDate.isBefore(DateTime.now())
                      ? _nextDay
                      : null,
                ),
              ],
            ),
          ),
          
          // 瞬间列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _moments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.hourglass_empty,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              '这一天没有记录',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            const Text('去记录一些美好瞬间吧'),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16.0),
                        itemCount: _moments.length,
                        itemBuilder: (context, index) {
                          final moment = _moments[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: MomentDetailCard(moment: moment),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
