import 'package:one_moment_app/data/models/media_type.dart';import 'package:flutter/material.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/core/config/environment_config.dart';
import 'package:one_moment_app/data/services/moment_service.dart';
import 'package:provider/provider.dart';

/// 调试页面
/// 用于测试API连接和数据获取
class DebugPage extends StatefulWidget {
  const DebugPage({Key? key}) : super(key: key);

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  final LoggerUtil _logger = LoggerUtil();
  final EnvironmentConfig _config = EnvironmentConfig();

  bool _isLoading = false;
  String _testResult = '';
  String _apiUrl = '';
  List<Map<String, dynamic>> _moments = [];
  bool _isLoggedIn = false;

  @override
  void initState() {
    super.initState();
    _apiUrl = _config.apiBaseUrl;
    _checkLoginStatus();
  }

  /// 检查登录状态
  void _checkLoginStatus() {
    final apiClient = Provider.of<ApiClient>(context, listen: false);
    setState(() {
      _isLoggedIn = apiClient.isLoggedIn();
    });
  }

  /// 测试API连接
  Future<void> _testApiConnection() async {
    setState(() {
      _isLoading = true;
      _testResult = '正在测试API连接...';
    });

    try {
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final result = await apiClient.testConnection();

      setState(() {
        _testResult = result ? 'API连接成功' : 'API连接失败';
      });
    } catch (e) {
      setState(() {
        _testResult = '测试过程中发生错误: $e';
      });
      _logger.e('测试API连接失败', e);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 获取瞬间列表
  Future<void> _getMoments() async {
    setState(() {
      _isLoading = true;
      _moments = [];
    });

    try {
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final momentService = MomentService(apiClient: apiClient);

      final moments = await momentService.getAllMoments();

      setState(() {
        _moments = moments.map((m) => {
          'id': m.id,
          'content': m.content,
          'createdAt': m.createdAt.toString(),
        }).toList();
      });

      _logger.i('成功获取${moments.length}个瞬间');
    } catch (e) {
      _logger.e('获取瞬间失败', e);

      // 显示错误信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('获取瞬间失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 更新API地址
  void _updateApiUrl() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      apiClient.updateBaseUrl(_apiUrl);

      // 测试新的连接
      await _testApiConnection();
    } catch (e) {
      _logger.e('更新API地址失败', e);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API调试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 环境信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('环境: ${_config.environmentName}', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Text('平台: ${_config.platform}', style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 8),
                    Text('API地址: ${_config.apiBaseUrl}', style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 8),
                    Text(
                      '登录功能已禁用',
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // API地址输入
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('修改API地址', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 8),
                    TextField(
                      controller: TextEditingController(text: _apiUrl),
                      decoration: const InputDecoration(
                        labelText: 'API地址',
                        hintText: 'http://localhost:8080/api',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _apiUrl = value;
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _updateApiUrl,
                      child: const Text('更新API地址'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 测试结果
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('API连接测试', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Text(_testResult, style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testApiConnection,
                      child: const Text('测试API连接'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 获取瞬间
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('获取瞬间', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _getMoments,
                      child: const Text('获取瞬间列表'),
                    ),
                    const SizedBox(height: 16),
                    if (_isLoading)
                      const Center(child: CircularProgressIndicator())
                    else if (_moments.isEmpty)
                      const Text('暂无数据')
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _moments.length,
                        itemBuilder: (context, index) {
                          final moment = _moments[index];
                          return ListTile(
                            title: Text(moment['content'] as String),
                            subtitle: Text(moment['createdAt'] as String),
                            leading: const Icon(Icons.photo),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
