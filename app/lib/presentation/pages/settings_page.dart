import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';

/// 设置页面
class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _isDarkMode = false;
  bool _isNotificationsEnabled = true;
  String _selectedAIModel = AppConstants.supportedAIModels.first;
  bool _isPrivacyModeEnabled = false;
  bool _isAutoBackupEnabled = false;
  String _appVersion = '';
  bool _isDeveloperMode = false;
  int _developerModeClickCount = 0;
  
  @override
  void initState() {
    super.initState();
    _loadAppInfo();
  }
  
  /// 加载应用信息
  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      
      setState(() {
        _appVersion = '${packageInfo.version} (${packageInfo.buildNumber})';
      });
    } catch (e) {
      setState(() {
        _appVersion = '未知';
      });
    }
  }
  
  /// 激活开发者模式
  void _activateDeveloperMode() {
    setState(() {
      _developerModeClickCount++;
      
      if (_developerModeClickCount >= 7) {
        _isDeveloperMode = true;
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('开发者模式已激活')),
        );
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        children: [
          // 外观设置
          _buildSectionHeader('外观设置'),
          SwitchListTile(
            title: const Text('深色模式'),
            subtitle: const Text('启用深色主题'),
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('主题设置功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('季节主题色'),
            subtitle: const Text('根据季节自动变化主题色'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('主题设置功能即将上线')),
              );
            },
          ),
          
          // 通知设置
          _buildSectionHeader('通知设置'),
          SwitchListTile(
            title: const Text('启用通知'),
            subtitle: const Text('接收应用通知'),
            value: _isNotificationsEnabled,
            onChanged: (value) {
              setState(() {
                _isNotificationsEnabled = value;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('通知设置功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('回顾提醒'),
            subtitle: const Text('设置回顾提醒时间'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('回顾提醒功能即将上线')),
              );
            },
          ),
          
          // AI设置
          _buildSectionHeader('AI设置'),
          ListTile(
            title: const Text('选择AI模型'),
            subtitle: Text('当前: $_selectedAIModel'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showAIModelSelectionDialog();
            },
          ),
          ListTile(
            title: const Text('API密钥管理'),
            subtitle: const Text('设置各AI服务商的API密钥'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('API密钥管理功能即将上线')),
              );
            },
          ),
          
          // 隐私设置
          _buildSectionHeader('隐私设置'),
          SwitchListTile(
            title: const Text('隐私模式'),
            subtitle: const Text('启用应用锁定'),
            value: _isPrivacyModeEnabled,
            onChanged: (value) {
              setState(() {
                _isPrivacyModeEnabled = value;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('隐私模式功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('数据管理'),
            subtitle: const Text('管理您的个人数据'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('数据管理功能即将上线')),
              );
            },
          ),
          
          // 备份与恢复
          _buildSectionHeader('备份与恢复'),
          SwitchListTile(
            title: const Text('自动备份'),
            subtitle: const Text('定期自动备份数据'),
            value: _isAutoBackupEnabled,
            onChanged: (value) {
              setState(() {
                _isAutoBackupEnabled = value;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('自动备份功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('手动备份'),
            subtitle: const Text('立即备份数据'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('手动备份功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('恢复数据'),
            subtitle: const Text('从备份中恢复数据'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('恢复数据功能即将上线')),
              );
            },
          ),
          
          // 关于
          _buildSectionHeader('关于'),
          ListTile(
            title: const Text('帮助与反馈'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('帮助与反馈功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('隐私政策'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('隐私政策功能即将上线')),
              );
            },
          ),
          ListTile(
            title: const Text('用户协议'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('用户协议功能即将上线')),
              );
            },
          ),
          // 长按激活开发者模式
          GestureDetector(
            onLongPress: _activateDeveloperMode,
            child: ListTile(
              title: const Text('版本信息'),
              subtitle: Text('v$_appVersion'),
            ),
          ),
          
          // 开发者模式（隐藏）
          if (_isDeveloperMode) ...[
            _buildSectionHeader('开发者模式'),
            ListTile(
              title: const Text('环境切换'),
              subtitle: const Text('切换开发/测试/生产环境'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('环境切换功能即将上线')),
                );
              },
            ),
            ListTile(
              title: const Text('日志查看'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('日志查看功能即将上线')),
                );
              },
            ),
            ListTile(
              title: const Text('性能监控'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('性能监控功能即将上线')),
                );
              },
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建分区标题
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
  
  /// 显示AI模型选择对话框
  Future<void> _showAIModelSelectionDialog() async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('选择AI模型'),
        children: AppConstants.supportedAIModels.map((model) {
          return SimpleDialogOption(
            onPressed: () {
              Navigator.pop(context, model);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Radio<String>(
                    value: model,
                    groupValue: _selectedAIModel,
                    onChanged: (value) {
                      Navigator.pop(context, value);
                    },
                  ),
                  Text(model),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
    
    if (result != null) {
      setState(() {
        _selectedAIModel = result;
      });
    }
  }
}
