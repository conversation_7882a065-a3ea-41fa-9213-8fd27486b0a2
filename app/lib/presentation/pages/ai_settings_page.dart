import 'package:one_moment_app/data/models/media_type.dart';import 'package:one_moment_app/core/config/ai_config.dart';import 'package:flutter/material.dart';
import 'package:one_moment_app/core/network/ai_service.dart';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';

/// AI设置页面
class AISettingsPage extends StatefulWidget {
  const AISettingsPage({super.key});

  @override
  State<AISettingsPage> createState() => _AISettingsPageState();
}

class _AISettingsPageState extends State<AISettingsPage> {
  final LoggerUtil _logger = LoggerUtil();
  final AIService _aiService = AIService();
  
  bool _isLoading = true;
  String _errorMessage = '';
  
  late AIModelConfig _currentConfig;
  List<AIModelConfig> _availableModels = [];
  
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _apiEndpointController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  
  @override
  void dispose() {
    _apiKeyController.dispose();
    _apiEndpointController.dispose();
    super.dispose();
  }
  
  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      
      // 加载当前配置
      _currentConfig = await _aiService.getCurrentModelConfig();
      _apiKeyController.text = _currentConfig.apiKey ?? '';
      _apiEndpointController.text = _currentConfig.apiEndpoint ?? '';
      
      // 加载可用模型
      _availableModels = await _aiService.getAllModelConfigs();
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('加载AI设置失败', e);
      setState(() {
        _errorMessage = '加载设置失败: $e';
        _isLoading = false;
      });
    }
  }
  
  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
      
      // 更新配置
      final updatedConfig = AIModelConfig(
        modelName: _currentConfig.modelName,
        provider: _currentConfig.provider,
        apiKey: _apiKeyController.text,
        apiEndpoint: _apiEndpointController.text,
      );
      
      // 保存配置
      final success = await _aiService.saveModelConfig(updatedConfig);
      
      setState(() {
        _isLoading = false;
      });
      
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('设置已保存'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = '保存设置失败';
        });
      }
    } catch (e) {
      _logger.e('保存AI设置失败', e);
      setState(() {
        _errorMessage = '保存设置失败: $e';
        _isLoading = false;
      });
    }
  }
  
  /// 选择模型
  void _selectModel(AIModelConfig model) {
    setState(() {
      _currentConfig = model;
      _apiEndpointController.text = model.apiEndpoint ?? '';
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI设置'),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSettings,
              tooltip: '保存',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }
  
  /// 构建页面主体
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8.0),
              margin: const EdgeInsets.only(bottom: 16.0),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          
          // 当前模型
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前AI模型',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16.0),
                  ListTile(
                    title: Text(_currentConfig.provider),
                    subtitle: Text(_currentConfig.modelName),
                    leading: const Icon(Icons.smart_toy),
                    trailing: const Icon(Icons.check_circle, color: Colors.green),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16.0),
          
          // API密钥
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'API配置',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16.0),
                  TextFormField(
                    controller: _apiKeyController,
                    decoration: const InputDecoration(
                      labelText: 'API密钥',
                      hintText: '输入您的API密钥',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.key),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16.0),
                  TextFormField(
                    controller: _apiEndpointController,
                    decoration: const InputDecoration(
                      labelText: 'API端点',
                      hintText: '输入API端点URL',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.link),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16.0),
          
          // 可用模型
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '可用AI模型',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16.0),
                  ..._availableModels.map((model) => ListTile(
                    title: Text(model.provider),
                    subtitle: Text(model.modelName),
                    leading: const Icon(Icons.smart_toy),
                    trailing: _currentConfig.provider == model.provider && 
                              _currentConfig.modelName == model.modelName
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.circle_outlined),
                    onTap: () => _selectModel(model),
                  )),
                ],
              ),
            ),
          ),
          const SizedBox(height: 32.0),
          
          // 保存按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _saveSettings,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
              ),
              child: const Text('保存设置'),
            ),
          ),
        ],
      ),
    );
  }
}
