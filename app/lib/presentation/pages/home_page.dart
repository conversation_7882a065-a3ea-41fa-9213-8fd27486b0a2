import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:one_moment_app/core/services/image_cache_service.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';
import 'package:one_moment_app/core/network/api_client.dart';
import 'package:one_moment_app/core/storage/local_storage.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/agent_config_model.dart';
import 'package:one_moment_app/data/models/agent_chat_session_model.dart';
import 'package:one_moment_app/data/services/tongyi_service.dart';
import 'package:one_moment_app/presentation/pages/agent_chat_page.dart';
import 'package:one_moment_app/presentation/pages/agent_list_page.dart';
import 'package:one_moment_app/presentation/pages/time_capsule_calendar_page.dart';
import 'package:one_moment_app/presentation/providers/agent_provider.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';
import 'package:one_moment_app/presentation/widgets/moment_calendar.dart';
import 'package:one_moment_app/presentation/widgets/moment_list.dart';
import 'package:one_moment_app/presentation/widgets/wow_moment_card.dart';

/// 主页
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  final GlobalKey<_InnerHeartContentState> _innerHeartKey = GlobalKey<_InnerHeartContentState>();

  // 底部导航栏项目
  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.camera_alt_outlined),
      activeIcon: Icon(Icons.camera_alt),
      label: 'Moment',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.favorite_outline),
      activeIcon: Icon(Icons.favorite),
      label: '悦己',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.history_outlined),
      activeIcon: Icon(Icons.history),
      label: '时间胶囊',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.person_outline),
      activeIcon: Icon(Icons.person),
      label: '我',
    ),
  ];

  // 页面内容
  List<Widget> get _pages => [
    const _MomentContent(),
    _InnerHeartContent(key: _innerHeartKey),
    const TimeCapsuleCalendarPage(),
    const _ProfileContent(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: () => _showLogoDialog(),
          child: Text(
            _selectedIndex == 0 ? 'One Moment' :
            _selectedIndex == 1 ? '悦己' :
            _selectedIndex == 2 ? '时间胶囊' : '个人中心',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 0,
        // 为不同页面添加对应的按钮
        actions: _selectedIndex == 1 ? [
          // 悦己页面的新对话按钮
          IconButton(
            onPressed: () {
              _innerHeartKey.currentState?._startNewConversation();
            },
            icon: const Icon(Icons.refresh),
            tooltip: '新的对话',
          ),
        ] : null,
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        items: _bottomNavItems,
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          // 只有当切换到不同页面时才刷新数据
          if (_selectedIndex != index) {
            setState(() {
              _selectedIndex = index;
            });
            // 任务5：每次切换页面时触发数据刷新
            _refreshCurrentPage(index);
          }
        },
      ),
      floatingActionButton: _selectedIndex == 1 ? null : FloatingActionButton(
        onPressed: () {
          // 跳转到记录页面
          context.pushNamed('moment-create');
        },
        tooltip: '添加记录',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// 检查是否有今日瞬间
  bool _checkHasTodayMoment() {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      // 从已有的moments中查找今日瞬间，而不是重新请求API
      final allMoments = momentProvider.moments;
      final todayMoments = allMoments.where((moment) {
        final momentDate = DateTime(
          moment.createdAt.year,
          moment.createdAt.month,
          moment.createdAt.day,
        );
        return momentDate.isAtSameMomentAs(todayDate);
      }).toList();

      if (todayMoments.isNotEmpty) {
        _todayMoment = todayMoments.first;
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  MomentModel? _todayMoment;

  /// 显示logo弹窗
  void _showLogoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.favorite,
                color: Theme.of(context).colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 8),
              const Text('One Moment'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '记录生活中的每一个美好瞬间',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 16),
              const Text('版本: v1.0.0'),
              const SizedBox(height: 8),
              const Text('开发者: One Moment Team'),
              const SizedBox(height: 8),
              Text(
                '© ${DateTime.now().year} One Moment App',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  /// 刷新当前页面数据
  void _refreshCurrentPage(int index) {
    // 使用Future.microtask确保在当前构建周期完成后执行
    Future.microtask(() {
      if (!mounted) return;

      switch (index) {
        case 0: // Moment页面
          try {
            final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
            final momentProvider = Provider.of<MomentProvider>(context, listen: false);

            // 异步刷新，避免阻塞UI
            weatherProvider.fetchLatestWeather(cityName: '北京');
            momentProvider.fetchMoments();
          } catch (e) {
            print('刷新Moment页面数据失败: $e');
          }
          break;

        case 1: // 悦己页面
          // 悦己页面不需要特殊刷新逻辑，聊天记录已经缓存
          print('切换到悦己页面');
          break;

        case 2: // 时间胶囊页面
          try {
            final momentProvider = Provider.of<MomentProvider>(context, listen: false);
            // 只刷新moments数据，时间胶囊页面会自动重新加载当前月份数据
            momentProvider.fetchMoments();
          } catch (e) {
            print('刷新时间胶囊页面数据失败: $e');
          }
          break;

        case 3: // 个人中心页面
          // 个人中心页面会在initState中显示隐私协议，不需要额外刷新
          print('切换到个人中心页面');
          break;
      }
    });
  }
}

/// Moment页面内容
class _MomentContent extends StatefulWidget {
  const _MomentContent();

  @override
  State<_MomentContent> createState() => _MomentContentState();
}

class _MomentContentState extends State<_MomentContent> {
  bool _isLoading = true;
  bool _hasTodayMoment = false;
  MomentModel? _todayMoment;

  @override
  void initState() {
    super.initState();
    // 使用Future.microtask确保在build完成后执行
    Future.microtask(() {
      _fetchWeather();
      _checkTodayMoment();
    });
  }

  Future<void> _fetchWeather() async {
    final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);

    try {
      await weatherProvider.fetchLatestWeather(cityName: '北京');
    } catch (e) {
      // 错误处理
      print('获取天气失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 检查今日是否已发布瞬间
  Future<void> _checkTodayMoment() async {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);

      // 获取今天的日期（只保留年月日）
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      // 获取今日瞬间
      final moments = await momentProvider.fetchMomentsByDate(todayDate);

      if (moments.isNotEmpty) {
        setState(() {
          _hasTodayMoment = true;
          _todayMoment = moments.first; // 获取最新的一条
        });
      }
    } catch (e) {
      print('检查今日瞬间失败: $e');
    }
  }

  String _getWeatherImage(String weatherCondition) {
    // 根据天气状况返回对应的图片资源
    switch (weatherCondition) {
      case '晴朗':
        return 'assets/images/weather/sunny.png';
      case '多云':
        return 'assets/images/weather/cloudy.png';
      case '阴天':
        return 'assets/images/weather/overcast.png';
      case '小雨':
      case '中雨':
        return 'assets/images/weather/rainy.png';
      case '大雨':
      case '暴雨':
        return 'assets/images/weather/heavy_rain.png';
      case '雷阵雨':
        return 'assets/images/weather/thunder.png';
      case '小雪':
      case '中雪':
      case '大雪':
        return 'assets/images/weather/snowy.png';
      default:
        return 'assets/images/weather/sunny.png';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 天气部分
          _buildWeatherSection(),

          // 日历部分
          _buildCalendarSection(),
        ],
      ),
    );
  }

  Widget _buildWeatherSection() {
    return Consumer<WeatherProvider>(
      builder: (context, weatherProvider, child) {
        final weather = weatherProvider.weather;
        final status = weatherProvider.status;

        return Container(
          height: MediaQuery.of(context).size.height * 0.18, // 减小高度，使三个模块都能在首屏显示
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.7),
              ],
            ),
          ),
          child: _isLoading || status == WeatherStatus.loading
              ? const Center(child: CircularProgressIndicator(color: Colors.white))
              : Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                weather?.cityName ?? '未知城市',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${weather?.temperature.toStringAsFixed(1) ?? '0.0'}°C · ${weather?.weatherCondition ?? '未知'}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${DateTime.now().year}年${DateTime.now().month}月${DateTime.now().day}日',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          Icon(
                            (weather?.weatherCondition ?? '').contains('雨')
                                ? Icons.water_drop
                                : (weather?.weatherCondition ?? '').contains('雪')
                                    ? Icons.ac_unit
                                    : (weather?.weatherCondition ?? '').contains('云')
                                        ? Icons.cloud
                                        : Icons.wb_sunny,
                            color: Colors.white,
                            size: 64,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _buildCalendarSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 今日日期和瞬间 - 扩大显示区域
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight: 300, // 设置最小高度，让内容充满屏幕
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0), // 增加内边距
                child: _hasTodayMoment ? _buildTodayMomentView() : _buildNoMomentView(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建今日已发布瞬间视图
  Widget _buildTodayMomentView() {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 显示瞬间内容
            Text(
              _todayMoment?.textContent ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
        const SizedBox(height: 16),

        // 如果有媒体，显示媒体
        if (_todayMoment?.mediaObjectKey != null) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: FutureBuilder<String?>(
              future: _getImageUrl(_todayMoment!.mediaObjectKey!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container(
                    height: 250,
                    width: double.infinity,
                    color: Colors.grey.shade200,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (snapshot.hasError || !snapshot.hasData) {
                  return Container(
                    height: 250,
                    width: double.infinity,
                    color: Colors.grey.shade200,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Image.network(
                  snapshot.data!,
                  height: 250,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 250,
                      width: double.infinity,
                      color: Colors.grey.shade200,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              size: 48,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '图片加载失败',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],

            // 显示时间和位置
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  DateFormat('HH:mm').format(_todayMoment?.createdAt ?? DateTime.now()),
                  style: TextStyle(color: Colors.grey),
                ),
                const SizedBox(width: 16),
                if (_todayMoment?.locationName != null) ...[
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _todayMoment?.locationName ?? '',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ],
            ),
          ],
        ),
        // 编辑按钮在右上角
        Positioned(
          top: 0,
          right: 0,
          child: IconButton(
            onPressed: () {
              if (_todayMoment != null) {
                context.pushNamed('moment-edit', extra: _todayMoment);
              }
            },
            icon: const Icon(Icons.edit),
            tooltip: '编辑今日瞬间',
            iconSize: 20,
          ),
        ),
      ],
    );
  }

  /// 获取图片URL
  Future<String?> _getImageUrl(String objectKey) async {
    try {
      // 先检查缓存
      final imageCache = ImageCacheService();
      final cachedUrl = imageCache.getCachedUrl(objectKey);
      if (cachedUrl != null) {
        return cachedUrl;
      }

      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/downloadImage', data: {
        'objectKey': objectKey,
      });

      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        final url = response['data'] as String;
        // 缓存URL
        imageCache.cacheUrl(objectKey, url);
        return url;
      }
      return null;
    } catch (e) {
      print('获取图片URL失败: $e');
      return null;
    }
  }

  /// 构建未发布瞬间视图
  Widget _buildNoMomentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '今天',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        Text(
          '${DateTime.now().day}',
          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
        const SizedBox(height: 16),
        const Text(
          '快来留住属于今天的一克拉时刻吧',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () {
            context.pushNamed('moment-create');
          },
          icon: const Icon(Icons.add_a_photo),
          label: const Text('记录瞬间'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}

/// 时间胶囊页面
class _TimeCapsulePage extends StatefulWidget {
  const _TimeCapsulePage();

  @override
  State<_TimeCapsulePage> createState() => _TimeCapsulePageState();
}

class _TimeCapsulePageState extends State<_TimeCapsulePage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  bool _isTimelineView = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时间胶囊'),
        actions: [
          IconButton(
            icon: Icon(_isTimelineView ? Icons.calendar_month : Icons.timeline),
            onPressed: () {
              setState(() {
                _isTimelineView = !_isTimelineView;
              });
            },
            tooltip: _isTimelineView ? '切换到日历视图' : '切换到时间轴视图',
          ),
        ],
      ),
      body: _isTimelineView ? _buildTimelineView() : _buildCalendarView(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.pushNamed('moment-create');
        },
        tooltip: '添加记录',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCalendarView() {
    return Column(
      children: [
        TableCalendar(
          firstDay: DateTime.utc(2020, 1, 1),
          lastDay: DateTime.utc(2030, 12, 31),
          focusedDay: _focusedDay,
          calendarFormat: _calendarFormat,
          selectedDayPredicate: (day) {
            return isSameDay(_selectedDay, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            setState(() {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
            });
          },
          onFormatChanged: (format) {
            setState(() {
              _calendarFormat = format;
            });
          },
          onPageChanged: (focusedDay) {
            _focusedDay = focusedDay;
          },
          calendarStyle: CalendarStyle(
            todayDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
              shape: BoxShape.circle,
            ),
            selectedDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            markerDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary,
              shape: BoxShape.circle,
            ),
          ),
          headerStyle: HeaderStyle(
            titleCentered: true,
            formatButtonVisible: true,
            formatButtonShowsNext: false,
            formatButtonDecoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        const Divider(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              Text(
                DateFormat('yyyy年MM月dd日').format(_selectedDay),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Spacer(),
              Text(
                '共3条记录',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        Expanded(
          child: const MomentList(limit: 10),
        ),
      ],
    );
  }

  Widget _buildTimelineView() {
    // 模拟数据
    final List<Map<String, dynamic>> timelineData = [
      {
        'date': DateTime.now(),
        'count': 2,
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 1)),
        'count': 1,
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 3)),
        'count': 3,
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 7)),
        'count': 1,
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 14)),
        'count': 2,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: timelineData.length,
      itemBuilder: (context, index) {
        final item = timelineData[index];
        final date = item['date'] as DateTime;
        final count = item['count'] as int;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                DateFormat('yyyy年MM月dd日').format(date),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            for (int i = 0; i < count; i++)
              Card(
                margin: const EdgeInsets.only(bottom: 12.0, left: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            child: const Icon(Icons.camera_alt, color: Colors.white),
                          ),
                          const SizedBox(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '记录瞬间',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              Text(
                                DateFormat('HH:mm').format(date),
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text('这是一个美好的瞬间，值得记录和回味...'),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          Chip(
                            label: const Text('快乐'),
                            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          ),
                          Chip(
                            label: const Text('生活'),
                            backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            const Divider(),
          ],
        );
      },
    );
  }
}

/// 悦己聊天内容
class _InnerHeartContent extends StatefulWidget {
  const _InnerHeartContent({Key? key}) : super(key: key);

  @override
  State<_InnerHeartContent> createState() => _InnerHeartContentState();
}

class _InnerHeartContentState extends State<_InnerHeartContent> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCachedMessages();
  }

  /// 加载缓存的对话内容
  Future<void> _loadCachedMessages() async {
    try {
      final localStorage = Provider.of<LocalStorage>(context, listen: false);
      final cachedMessages = await localStorage.getStringList('chat_messages') ?? [];

      if (cachedMessages.isEmpty) {
        // 如果没有缓存消息，添加欢迎消息
        _messages.add(ChatMessage(
          text: '你好！我是悦己，你的专属心理健康助手。我在这里倾听你的心声，陪伴你探索内心世界。有什么想要分享的吗？',
          isUser: false,
          timestamp: DateTime.now(),
        ));
      } else {
        // 加载缓存的消息
        for (String messageJson in cachedMessages) {
          try {
            final messageData = json.decode(messageJson);
            _messages.add(ChatMessage(
              text: messageData['text'],
              isUser: messageData['isUser'],
              timestamp: DateTime.parse(messageData['timestamp']),
            ));
          } catch (e) {
            print('解析缓存消息失败: $e');
          }
        }
      }

      if (mounted) {
        setState(() {});
        _scrollToBottom();
      }
    } catch (e) {
      print('加载缓存消息失败: $e');
      // 如果加载失败，添加欢迎消息
      _messages.add(ChatMessage(
        text: '你好！我是悦己，你的专属心理健康助手。我在这里倾听你的心声，陪伴你探索内心世界。有什么想要分享的吗？',
        isUser: false,
        timestamp: DateTime.now(),
      ));
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 保存对话内容到缓存
  Future<void> _saveCachedMessages() async {
    try {
      final localStorage = Provider.of<LocalStorage>(context, listen: false);
      final messageJsonList = _messages.map((message) {
        return json.encode({
          'text': message.text,
          'isUser': message.isUser,
          'timestamp': message.timestamp.toIso8601String(),
        });
      }).toList();

      await localStorage.setStringList('chat_messages', messageJsonList);
    } catch (e) {
      print('保存缓存消息失败: $e');
    }
  }

  /// 开始新的对话
  void _startNewConversation() {
    setState(() {
      _messages.clear();
      _messages.add(ChatMessage(
        text: '你好！我是悦己，你的专属心理健康助手。我在这里倾听你的心声，陪伴你探索内心世界。有什么想要分享的吗？',
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });
    _saveCachedMessages();
    _scrollToBottom();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _isLoading) return;

    // 添加用户消息
    setState(() {
      _messages.add(ChatMessage(
        text: text,
        isUser: true,
        timestamp: DateTime.now(),
      ));
      _isLoading = true;
    });

    _messageController.clear();
    _scrollToBottom();

    try {
      // 调用后端AI服务
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/ai/chat', data: {
        'message': text,
      });

      String aiResponseText = '';

      // 解析AI响应
      if (response is Map<String, dynamic>) {
        if (response['success'] == true && response['data'] != null) {
          final data = response['data'];
          if (data is Map<String, dynamic>) {
            // 如果data是对象，提取message字段
            aiResponseText = data['message'] ?? data['response'] ?? '抱歉，我无法理解您的消息。';
          } else if (data is String) {
            // 如果data是字符串，直接使用
            aiResponseText = data;
          } else {
            aiResponseText = '抱歉，我无法理解您的消息。';
          }
        } else {
          aiResponseText = response['message'] ?? '抱歉，我遇到了一些问题，请稍后再试。';
        }
      } else if (response is String) {
        // 如果响应直接是字符串
        try {
          final jsonResponse = json.decode(response);
          if (jsonResponse is Map<String, dynamic>) {
            aiResponseText = jsonResponse['message'] ?? jsonResponse['response'] ?? response;
          } else {
            aiResponseText = response;
          }
        } catch (e) {
          aiResponseText = response;
        }
      } else {
        aiResponseText = '抱歉，我遇到了一些问题，请稍后再试。';
      }

      setState(() {
        _messages.add(ChatMessage(
          text: aiResponseText,
          isUser: false,
          timestamp: DateTime.now(),
        ));
        _isLoading = false;
      });

      // 保存对话到缓存
      _saveCachedMessages();
      _scrollToBottom();
    } catch (e) {
      print('AI聊天失败: $e');
      setState(() {
        _messages.add(ChatMessage(
          text: '抱歉，我遇到了一些问题，请稍后再试。',
          isUser: false,
          timestamp: DateTime.now(),
        ));
        _isLoading = false;
      });

      // 即使失败也保存对话到缓存
      _saveCachedMessages();
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 聊天消息列表
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16.0),
            itemCount: _messages.length + (_isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _messages.length && _isLoading) {
                return _buildTypingIndicator();
              }
              return _buildMessageBubble(_messages[index]);
            },
          ),
        ),

        // 输入框
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: '输入你想说的话...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _isLoading ? null : _sendMessage,
                icon: Icon(
                  Icons.send,
                  color: _isLoading
                      ? Colors.grey
                      : Theme.of(context).colorScheme.primary,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: const CircleBorder(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: message.isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Icon(
                Icons.psychology,
                size: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(18).copyWith(
                  bottomLeft: message.isUser ? const Radius.circular(18) : const Radius.circular(4),
                  bottomRight: message.isUser ? const Radius.circular(4) : const Radius.circular(18),
                ),
                border: message.isUser ? null : Border.all(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 根据是否为用户消息决定是否使用Markdown渲染
                  if (message.isUser)
                    Text(
                      message.text,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    )
                  else
                    MarkdownBody(
                      data: message.text,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                        ),
                        strong: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        em: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                        ),
                        code: TextStyle(
                          backgroundColor: Colors.grey.shade200,
                          fontFamily: 'monospace',
                          fontSize: 14,
                        ),
                        codeblockDecoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      selectable: true,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('HH:mm').format(message.timestamp),
                    style: TextStyle(
                      color: message.isUser
                          ? Colors.white70
                          : Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
              child: Icon(
                Icons.person,
                size: 18,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            child: Icon(
              Icons.psychology,
              size: 18,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(18).copyWith(
                bottomLeft: const Radius.circular(4),
              ),
              border: Border.all(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '悦己正在思考...',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 聊天消息模型
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}

/// 发现内容
class _DiscoveryContent extends StatelessWidget {
  const _DiscoveryContent();

  @override
  Widget build(BuildContext context) {
    // 模拟数据 - 社区列表
    final List<Map<String, dynamic>> communities = [
      {
        'name': '旅行爱好者',
        'icon': Icons.flight,
        'color': const Color(0xFF64B5F6),
        'members': 1289,
        'posts': 356,
      },
      {
        'name': '美食分享',
        'icon': Icons.restaurant,
        'color': const Color(0xFFFFB74D),
        'members': 2567,
        'posts': 892,
      },
      {
        'name': '读书会',
        'icon': Icons.book,
        'color': const Color(0xFF81C784),
        'members': 876,
        'posts': 245,
      },
      {
        'name': '摄影爱好者',
        'icon': Icons.camera_alt,
        'color': const Color(0xFFBA68C8),
        'members': 1543,
        'posts': 678,
      },
      {
        'name': '电影讨论',
        'icon': Icons.movie,
        'color': const Color(0xFFE57373),
        'members': 1876,
        'posts': 532,
      },
      {
        'name': '音乐分享',
        'icon': Icons.music_note,
        'color': const Color(0xFF4DB6AC),
        'members': 2134,
        'posts': 721,
      },
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '发现社区',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            '探索志同道合的朋友和有趣的内容',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 24),

          // 热门社区
          Text(
            '热门社区',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),

          // 社区网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: communities.length,
            itemBuilder: (context, index) {
              final community = communities[index];
              return _buildCommunityCard(context, community);
            },
          ),

          const SizedBox(height: 24),

          // 附近的人
          Text(
            '附近的人',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const Text('查看附近的人需要开启位置权限'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('位置权限请求已发送')),
                      );
                    },
                    child: const Text('开启位置权限'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommunityCard(BuildContext context, Map<String, dynamic> community) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('进入${community['name']}社区')),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                community['icon'] as IconData,
                color: community['color'] as Color,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                community['name'] as String,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                '${community['members']}人 · ${community['posts']}条内容',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 个人中心内容
class _ProfileContent extends StatefulWidget {
  const _ProfileContent();

  @override
  State<_ProfileContent> createState() => _ProfileContentState();
}

class _ProfileContentState extends State<_ProfileContent> {
  bool _hasShownPrivacyDialog = false;

  @override
  void initState() {
    super.initState();
    // 每次进入个人中心都显示隐私协议
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showPrivacyDialog();
    });
  }

  /// 显示隐私协议弹窗
  Future<void> _showPrivacyDialog() async {
    if (_hasShownPrivacyDialog) return;
    _hasShownPrivacyDialog = true;

    try {
      // 获取隐私协议内容
      final apiClient = Provider.of<ApiClient>(context, listen: false);
      final response = await apiClient.post('/moments/privacy-policy');

      String privacyContent = '';
      if (response is Map<String, dynamic> &&
          response['success'] == true &&
          response['data'] != null) {
        privacyContent = response['data'] as String;
      } else {
        privacyContent = '隐私协议加载失败，请稍后重试。';
      }

      if (!mounted) return;

      // 显示隐私协议弹窗
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('隐私协议'),
            content: SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.6,
              child: SingleChildScrollView(
                child: MarkdownBody(
                  data: privacyContent,
                  styleSheet: MarkdownStyleSheet(
                    p: const TextStyle(fontSize: 14),
                    h1: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    h2: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: const Text('拒绝'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                child: const Text('同意'),
              ),
            ],
          );
        },
      );

      if (result != true) {
        // 用户拒绝，切换到首页Moment页面
        if (mounted) {
          // 通过回调函数切换到首页
          final homePageState = context.findAncestorStateOfType<_HomePageState>();
          if (homePageState != null) {
            homePageState.setState(() {
              homePageState._selectedIndex = 0;
            });
          }
        }
      }
    } catch (e) {
      print('获取隐私协议失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('隐私协议加载失败')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        // 用户信息卡片
        _buildUserInfoCard(context),

        const Divider(),

        // 功能列表
        _buildFunctionList(context),

        const Divider(),

        // 设置列表
        _buildSettingsList(context),

        // 版本信息
        ListTile(
          leading: const Icon(Icons.info_outline),
          title: const Text('版本信息'),
          subtitle: const Text('v1.0.0'),
          onTap: () {
            // 长按激活开发者模式
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('长按可激活开发者模式')),
            );
          },
          onLongPress: () {
            // 开发者模式
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('开发者模式已激活')),
            );
          },
        ),

        // 底部空白
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildUserInfoCard(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 用户头像
            CircleAvatar(
              radius: 48,
              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Icon(
                Icons.person,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // 用户名
            const Text(
              '默认用户',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // 用户ID
            Text(
              'ID: 10086',
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),

            // 编辑资料按钮
            OutlinedButton.icon(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('编辑资料功能即将上线')),
                );
              },
              icon: const Icon(Icons.edit),
              label: const Text('编辑资料'),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
          child: Text(
            '我的功能',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ListTile(
          leading: Icon(
            Icons.favorite,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('我的收藏'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('我的收藏功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.history,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('浏览历史'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('浏览历史功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.cloud_upload,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('云同步'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('云同步功能即将上线')),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSettingsList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
          child: Text(
            '设置',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ListTile(
          leading: Icon(
            Icons.color_lens,
            color: Theme.of(context).colorScheme.secondary,
          ),
          title: const Text('主题设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('主题设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.notifications,
            color: Theme.of(context).colorScheme.secondary,
          ),
          title: const Text('通知设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('通知设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.privacy_tip,
            color: Theme.of(context).colorScheme.secondary,
          ),
          title: const Text('隐私设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('隐私设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.help,
            color: Theme.of(context).colorScheme.secondary,
          ),
          title: const Text('帮助与反馈'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('帮助与反馈功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: Icon(
            Icons.info,
            color: Theme.of(context).colorScheme.secondary,
          ),
          title: const Text('关于我们'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('关于我们功能即将上线')),
            );
          },
        ),
      ],
    );
  }
}

/// 设置内容
class _SettingsContent extends StatelessWidget {
  const _SettingsContent();

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        ListTile(
          leading: const Icon(Icons.person_outline),
          title: const Text('个人资料'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 个人资料
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('个人资料功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.color_lens_outlined),
          title: const Text('主题设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 主题设置
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('主题设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.notifications_outlined),
          title: const Text('通知设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 通知设置
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('通知设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.privacy_tip_outlined),
          title: const Text('隐私设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 隐私设置
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('隐私设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.smart_toy_outlined),
          title: const Text('AI模型设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // AI模型设置
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('AI模型设置功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.backup_outlined),
          title: const Text('备份与恢复'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 备份与恢复
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('备份与恢复功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.help_outline),
          title: const Text('帮助与反馈'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 帮助与反馈
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('帮助与反馈功能即将上线')),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.info_outline),
          title: const Text('关于'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // 关于
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('关于功能即将上线')),
            );
          },
        ),
        // 长按logo激活开发者模式
        GestureDetector(
          onLongPress: () {
            // 开发者模式
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('开发者模式已激活')),
            );
          },
          child: ListTile(
            leading: const Icon(Icons.code),
            title: const Text('版本信息'),
            subtitle: const Text('v1.0.0'),
          ),
        ),
      ],
    );
  }
}
