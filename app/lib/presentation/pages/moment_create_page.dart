import 'package:one_moment_app/data/models/media_type.dart';import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:location/location.dart';
import 'package:provider/provider.dart';
import 'package:one_moment_app/core/utils/logger_util.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/data/models/mood_type.dart';
import 'package:one_moment_app/presentation/providers/moment_provider.dart';
import 'package:one_moment_app/presentation/providers/weather_provider.dart';

/// 创建瞬间页面
class MomentCreatePage extends StatefulWidget {
  const MomentCreatePage({Key? key}) : super(key: key);

  @override
  State<MomentCreatePage> createState() => _MomentCreatePageState();
}

class _MomentCreatePageState extends State<MomentCreatePage> {
  final TextEditingController _contentController = TextEditingController();

  MediaType _selectedMediaType = MediaType.none;
  File? _mediaFile;
  String _location = '未知位置';
  String _weather = '未知天气';
  MoodType _selectedMood = MoodType.neutral;

  bool _isLoading = false;
  bool _isLocationLoading = false;
  bool _isCheckingTodayMoment = true;
  MomentModel? _todayMoment;

  @override
  void initState() {
    super.initState();
    // 不再检查今日瞬间，每次都是新的创建页面
    _getLocationAndWeather();
    setState(() {
      _isCheckingTodayMoment = false;
    });
  }

  /// 检查今日是否已发布瞬间
  Future<void> _checkTodayMoment() async {
    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);

      // 获取今天的日期（只保留年月日）
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      // 获取今日瞬间
      final moments = await momentProvider.fetchMomentsByDate(todayDate);

      if (moments.isNotEmpty) {
        // 如果有多条记录，只取最新的一条
        final latestMoment = moments.reduce((a, b) =>
          a.createdAt.isAfter(b.createdAt) ? a : b);

        setState(() {
          _todayMoment = latestMoment;
          _isCheckingTodayMoment = false;
        });

        // 预填充编辑内容
        _contentController.text = latestMoment.textContent;
        _selectedMood = latestMoment.mood;
        _location = latestMoment.locationName ?? '未知位置';
        _weather = latestMoment.weather ?? '未知天气';
      } else {
        setState(() {
          _isCheckingTodayMoment = false;
        });
      }
    } catch (e) {
      print('检查今日瞬间失败: $e');
      setState(() {
        _isCheckingTodayMoment = false;
      });
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  /// 获取位置和天气
  Future<void> _getLocationAndWeather() async {
    setState(() {
      _isLocationLoading = true;
    });

    try {
      // 获取位置
      final location = await _getCurrentLocation();
      if (location != null) {
        setState(() {
          _location = '正在获取位置...';
        });

        // 根据位置获取地址
        // 这里应该调用地理编码API获取地址
        setState(() {
          _location = '北京市海淀区'; // 模拟数据
        });
      }

      // 获取天气
      final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
      final weather = weatherProvider.weather;
      if (weather != null) {
        setState(() {
          _weather = '${weather.weatherCondition}，${weather.temperature}°C';
        });
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('获取位置和天气失败: $e');
    } finally {
      setState(() {
        _isLocationLoading = false;
      });
    }
  }

  /// 获取当前位置
  Future<LocationData?> _getCurrentLocation() async {
    final location = Location();

    bool serviceEnabled;
    PermissionStatus permissionGranted;

    // 检查位置服务是否启用
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        return null;
      }
    }

    // 检查位置权限
    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        return null;
      }
    }

    // 获取位置
    return await location.getLocation();
  }

  /// 选择图片
  Future<void> _pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        setState(() {
          _isLoading = true;
        });

        // 上传文件到后端
        final momentProvider = Provider.of<MomentProvider>(context, listen: false);
        final file = File(pickedFile.path);
        final success = await momentProvider.uploadFile(
          file: file,
          mediaType: MediaType.image,
        );

        if (success) {
          setState(() {
            _mediaFile = file;
            _selectedMediaType = MediaType.image;
            _isLoading = false;
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('上传图片失败')),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('选择图片失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败')),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 选择视频
  Future<void> _pickVideo(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickVideo(source: source);

      if (pickedFile != null) {
        setState(() {
          _isLoading = true;
        });

        // 上传文件到后端
        final momentProvider = Provider.of<MomentProvider>(context, listen: false);
        final file = File(pickedFile.path);
        final success = await momentProvider.uploadFile(
          file: file,
          mediaType: MediaType.video,
        );

        if (success) {
          setState(() {
            _mediaFile = file;
            _selectedMediaType = MediaType.video;
            _isLoading = false;
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('上传视频失败')),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('选择视频失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择视频失败')),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 显示媒体选择底部弹窗
  void _showMediaPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择图片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.videocam),
                title: const Text('录制视频'),
                onTap: () {
                  Navigator.pop(context);
                  _pickVideo(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.video_library),
                title: const Text('从相册选择视频'),
                onTap: () {
                  Navigator.pop(context);
                  _pickVideo(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 发布或更新瞬间
  Future<void> _publishMoment() async {
    // 验证内容
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入内容')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final momentProvider = Provider.of<MomentProvider>(context, listen: false);

      // 总是创建新瞬间
      bool success = await momentProvider.createMoment(
        content: _contentController.text.trim(),
        mediaType: _selectedMediaType,
        location: _location,
        weather: _weather,
        mood: _selectedMood,
      );

      if (success) {
        // 发布/更新成功，显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_todayMoment != null ? '瞬间更新成功！' : '瞬间发布成功！'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );

          // 等待提示显示后再返回
          await Future.delayed(const Duration(seconds: 1));

          // 返回上一页
          if (mounted) {
            Navigator.pop(context, true);
          }
        }
      } else {
        // 发布/更新失败
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(_todayMoment != null ? '更新失败，请重试' : '发布失败，请重试')),
          );
        }
      }
    } catch (e) {
      final logger = LoggerUtil();
      logger.e('${_todayMoment != null ? '更新' : '发布'}瞬间失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${_todayMoment != null ? '更新' : '发布'}失败，请重试')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingTodayMoment) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('记录瞬间'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('记录瞬间'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _publishMoment,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('发布'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 内容输入
            TextField(
              controller: _contentController,
              maxLines: 5,
              maxLength: 500,
              decoration: const InputDecoration(
                hintText: '记录这一刻的想法...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // 媒体预览
            if (_mediaFile != null) ...[
              _selectedMediaType == MediaType.image
                  ? Image.file(
                      _mediaFile!,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    )
                  : Container(
                      height: 200,
                      width: double.infinity,
                      color: Colors.black,
                      child: const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white,
                          size: 48,
                        ),
                      ),
                    ),
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _mediaFile = null;
                    _selectedMediaType = MediaType.none;
                  });
                },
                icon: const Icon(Icons.delete),
                label: const Text('删除'),
              ),
              const SizedBox(height: 16),
            ],

            // 添加媒体按钮
            if (_mediaFile == null)
              OutlinedButton.icon(
                onPressed: _showMediaPicker,
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('添加图片/视频'),
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 48),
                ),
              ),

            const SizedBox(height: 24),

            // 位置和天气信息
            Row(
              children: [
                const Icon(Icons.location_on, size: 16),
                const SizedBox(width: 4),
                _isLocationLoading
                    ? const Text('正在获取位置...')
                    : Text(_location),
                const Spacer(),
                const Icon(Icons.wb_sunny, size: 16),
                const SizedBox(width: 4),
                Text(_weather),
              ],
            ),

            const SizedBox(height: 24),

            // 心情选择
            const Text(
              '选择心情',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 12,
              children: [
                _buildMoodChip(MoodType.happy, '开心', Icons.sentiment_very_satisfied),
                _buildMoodChip(MoodType.calm, '平静', Icons.sentiment_satisfied),
                _buildMoodChip(MoodType.sad, '难过', Icons.sentiment_dissatisfied),
                _buildMoodChip(MoodType.excited, '兴奋', Icons.mood),
                _buildMoodChip(MoodType.anxious, '焦虑', Icons.mood_bad),
                _buildMoodChip(MoodType.neutral, '一般', Icons.sentiment_neutral),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建心情选择芯片
  Widget _buildMoodChip(MoodType mood, String label, IconData icon) {
    final isSelected = _selectedMood == mood;

    return FilterChip(
      selected: isSelected,
      label: Text(label),
      avatar: Icon(
        icon,
        color: isSelected ? Colors.white : null,
      ),
      onSelected: (selected) {
        setState(() {
          _selectedMood = mood;
        });
      },
    );
  }
}
