import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'dart:convert';
import 'package:one_moment_app/data/models/ai_chat_model.dart';
import 'package:one_moment_app/core/network/ai_service.dart';
import 'package:one_moment_app/core/constants/app_constants.dart';

/// AI聊天页面
class AIChatPage extends StatefulWidget {
  const AIChatPage({super.key});

  @override
  State<AIChatPage> createState() => _AIChatPageState();
}

class _AIChatPageState extends State<AIChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AIService _aiService = AIService();

  ChatSession? _currentSession;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _createNewSession();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 创建新会话
  Future<void> _createNewSession() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 创建新会话
      final session = ChatSession(
        id: const Uuid().v4(),
        title: '新对话',
        messages: [
          ChatMessage(
            id: const Uuid().v4(),
            role: MessageRole.system,
            content: '你是一个友好的AI助手，可以帮助用户记录生活中的美好瞬间，提供情感支持和建议。',
            timestamp: DateTime.now(),
          ),
          ChatMessage(
            id: const Uuid().v4(),
            role: MessageRole.assistant,
            content: '你好！我是你的AI助手，很高兴能陪伴你记录生活中的美好瞬间。有什么我可以帮助你的吗？',
            timestamp: DateTime.now(),
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        modelConfig: AIModelConfig(
          modelName: '通义千问',
          provider: '通义千问',
          apiKey: 'YOUR_API_KEY', // 实际应用中应从安全存储中获取
          apiEndpoint: null,
        ),
      );

      // 保存会话
      final dbService = await DatabaseService.instance;
      await dbService.insertChatSession(session);

      setState(() {
        _currentSession = session;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建会话失败: $e')),
        );
      }
    }
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _currentSession == null) return;

    // 清空输入框
    _messageController.clear();

    // 创建用户消息
    final userMessage = ChatMessage(
      id: const Uuid().v4(),
      role: MessageRole.user,
      content: message,
      timestamp: DateTime.now(),
    );

    // 更新会话
    setState(() {
      _currentSession = _currentSession!.addMessage(userMessage);
      _isLoading = true;
    });

    // 滚动到底部
    _scrollToBottom();

    try {
      // 发送消息到AI服务
      final response = await _aiService.sendMessage(
        messages: _currentSession!.messages,
        modelConfig: _currentSession!.modelConfig,
      );

      // 创建AI回复消息
      final aiMessage = ChatMessage(
        id: const Uuid().v4(),
        role: MessageRole.assistant,
        content: response,
        timestamp: DateTime.now(),
      );

      // 更新会话
      setState(() {
        _currentSession = _currentSession!.addMessage(aiMessage);
        _isLoading = false;
      });

      // 保存会话
      final dbService = await DatabaseService.instance;
      await dbService.insertChatSession(_currentSession!);

      // 滚动到底部
      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('发送消息失败: $e')),
        );
      }
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI聊天'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'new') {
                _createNewSession();
              } else if (value == 'settings') {
                // 打开设置
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('AI设置功能即将上线')),
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new',
                child: Text('新对话'),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Text('AI设置'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading && _currentSession == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // AI模型信息
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  color: Theme.of(context).colorScheme.primaryContainer,
                  child: Row(
                    children: [
                      const Icon(Icons.smart_toy),
                      const SizedBox(width: 8),
                      Text(
                        '当前模型: ${_currentSession?.modelConfig.provider ?? '未知'} - ${_currentSession?.modelConfig.modelName ?? '未知'}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),

                // 聊天消息列表
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _currentSession?.messages.length ?? 0,
                    itemBuilder: (context, index) {
                      final message = _currentSession!.messages[index];

                      // 跳过系统消息
                      if (message.role == MessageRole.system) {
                        return const SizedBox.shrink();
                      }

                      return _ChatMessageBubble(message: message);
                    },
                  ),
                ),

                // 加载指示器
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: LinearProgressIndicator(),
                  ),

                // 输入框
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: const InputDecoration(
                            hintText: '输入消息...',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: null,
                          textInputAction: TextInputAction.send,
                          onSubmitted: (_) => _sendMessage(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.send),
                        onPressed: _isLoading ? null : _sendMessage,
                      ),
                    ],
                  ),
                ),

                // 免责声明
                Container(
                  padding: const EdgeInsets.all(8),
                  color: Colors.grey[200],
                  child: const Text(
                    '免责声明：AI生成的内容仅供参考，不代表本应用观点。',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
    );
  }
}

/// 聊天消息气泡
class _ChatMessageBubble extends StatelessWidget {
  final ChatMessage message;

  const _ChatMessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    final isUser = message.role == MessageRole.user;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser)
            CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(Icons.smart_toy, color: Colors.white),
            ),
          if (!isUser) const SizedBox(width: 8),

          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: _buildMessageContent(message, isUser, context),
            ),
          ),

          if (isUser) const SizedBox(width: 8),
          if (isUser)
            CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(Icons.person, color: Colors.white),
            ),
        ],
      ),
    );
  }

  /// 构建消息内容，支持JSON解析和Markdown渲染
  Widget _buildMessageContent(ChatMessage message, bool isUser, BuildContext context) {
    String content = message.content;

    // 尝试解析JSON
    if (!isUser && content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        final jsonData = json.decode(content);
        if (jsonData is Map<String, dynamic>) {
          // 提取实际的消息内容
          if (jsonData.containsKey('message')) {
            content = jsonData['message'].toString();
          } else if (jsonData.containsKey('content')) {
            content = jsonData['content'].toString();
          } else if (jsonData.containsKey('text')) {
            content = jsonData['text'].toString();
          } else if (jsonData.containsKey('response')) {
            content = jsonData['response'].toString();
          } else {
            // 如果没有找到标准字段，格式化显示JSON
            content = _formatJsonForDisplay(jsonData);
          }
        }
      } catch (e) {
        // JSON解析失败，保持原内容
      }
    }

    // 对于AI消息，使用Markdown渲染
    if (!isUser) {
      return MarkdownBody(
        data: content,
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontSize: 14,
          ),
          h1: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          h2: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          h3: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          code: TextStyle(
            backgroundColor: Theme.of(context).colorScheme.surface,
            color: Theme.of(context).colorScheme.onSurface,
            fontFamily: 'monospace',
          ),
          codeblockDecoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      );
    } else {
      // 用户消息使用普通文本
      return Text(
        content,
        style: TextStyle(
          color: isUser ? Colors.white : null,
        ),
      );
    }
  }

  /// 格式化JSON用于显示
  String _formatJsonForDisplay(Map<String, dynamic> jsonData) {
    final buffer = StringBuffer();
    jsonData.forEach((key, value) {
      buffer.writeln('**$key**: $value');
    });
    return buffer.toString();
  }
}