import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import 'package:one_moment_app/core/utils/media_service.dart';
import 'package:one_moment_app/core/utils/location_weather_service.dart';
import 'package:one_moment_app/data/models/moment_model.dart';
import 'package:one_moment_app/core/storage/database_service.dart';

/// 记录页面
class RecordPage extends StatefulWidget {
  const RecordPage({super.key});

  @override
  State<RecordPage> createState() => _RecordPageState();
}

class _RecordPageState extends State<RecordPage> {
  final TextEditingController _contentController = TextEditingController();
  final MediaService _mediaService = MediaService();
  final LocationWeatherService _locationWeatherService = LocationWeatherService();
  
  File? _mediaFile;
  MediaType _mediaType = MediaType.none;
  bool _isLoading = false;
  String? _location;
  String? _weather;
  MoodType _selectedMood = MoodType.neutral;
  bool _isPrivate = false;
  
  // 心情选择器项目
  final List<MoodItem> _moodItems = [
    MoodItem(type: MoodType.happy, icon: Icons.sentiment_very_satisfied, label: '开心'),
    MoodItem(type: MoodType.calm, icon: Icons.sentiment_satisfied, label: '平静'),
    MoodItem(type: MoodType.sad, icon: Icons.sentiment_dissatisfied, label: '难过'),
    MoodItem(type: MoodType.excited, icon: Icons.mood, label: '兴奋'),
    MoodItem(type: MoodType.anxious, icon: Icons.mood_bad, label: '焦虑'),
    MoodItem(type: MoodType.neutral, icon: Icons.sentiment_neutral, label: '中性'),
  ];
  
  @override
  void initState() {
    super.initState();
    _getLocationAndWeather();
  }
  
  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }
  
  /// 获取位置和天气信息
  Future<void> _getLocationAndWeather() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      final locationWeather = await _locationWeatherService.getLocationAndWeather();
      
      setState(() {
        _location = locationWeather['location'];
        _weather = locationWeather['weather'];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法获取位置和天气信息: $e')),
        );
      }
    }
  }
  
  /// 选择图片
  Future<void> _pickImage() async {
    try {
      final file = await _mediaService.pickImage();
      
      if (file != null) {
        setState(() {
          _mediaFile = file;
          _mediaType = MediaType.image;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法选择图片: $e')),
        );
      }
    }
  }
  
  /// 选择视频
  Future<void> _pickVideo() async {
    try {
      final file = await _mediaService.pickVideo();
      
      if (file != null) {
        setState(() {
          _mediaFile = file;
          _mediaType = MediaType.video;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法选择视频: $e')),
        );
      }
    }
  }
  
  /// 录制音频
  Future<void> _recordAudio() async {
    try {
      // 显示录音对话框
      final result = await showDialog<File?>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const _AudioRecordDialog(),
      );
      
      if (result != null) {
        setState(() {
          _mediaFile = result;
          _mediaType = MediaType.audio;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法录制音频: $e')),
        );
      }
    }
  }
  
  /// 清除媒体
  void _clearMedia() {
    setState(() {
      _mediaFile = null;
      _mediaType = MediaType.none;
    });
  }
  
  /// 保存瞬间
  Future<void> _saveMoment() async {
    if (_contentController.text.isEmpty && _mediaFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入内容或添加媒体')),
      );
      return;
    }
    
    try {
      setState(() {
        _isLoading = true;
      });
      
      // 创建瞬间模型
      final moment = MomentModel(
        id: const Uuid().v4(),
        content: _contentController.text,
        createdAt: DateTime.now(),
        location: _location,
        weather: _weather,
        mediaType: _mediaType,
        mediaPath: _mediaFile?.path,
        tags: [], // 暂不支持标签
        mood: _selectedMood,
        isPrivate: _isPrivate,
      );
      
      // 保存到数据库
      final dbService = await DatabaseService.instance;
      await dbService.insertMoment(moment);
      
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('瞬间已保存')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('记录瞬间'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _isLoading ? null : _saveMoment,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 位置和天气信息
                  if (_location != null || _weather != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Row(
                        children: [
                          if (_location != null)
                            Expanded(
                              child: Chip(
                                avatar: const Icon(Icons.location_on, size: 16),
                                label: Text(_location!, overflow: TextOverflow.ellipsis),
                              ),
                            ),
                          const SizedBox(width: 8),
                          if (_weather != null)
                            Expanded(
                              child: Chip(
                                avatar: const Icon(Icons.cloud, size: 16),
                                label: Text(_weather!, overflow: TextOverflow.ellipsis),
                              ),
                            ),
                        ],
                      ),
                    ),
                  
                  // 内容输入
                  TextField(
                    controller: _contentController,
                    maxLines: 5,
                    decoration: const InputDecoration(
                      hintText: '记录下这一刻的感受...',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // 媒体预览
                  if (_mediaFile != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Stack(
                        children: [
                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: _mediaType == MediaType.image
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.file(
                                      _mediaFile!,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                                : Center(
                                    child: Icon(
                                      _mediaType == MediaType.video
                                          ? Icons.videocam
                                          : Icons.mic,
                                      size: 48,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: _clearMedia,
                              color: Colors.white,
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // 媒体选择按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _pickImage,
                        icon: const Icon(Icons.image),
                        label: const Text('图片'),
                      ),
                      ElevatedButton.icon(
                        onPressed: _pickVideo,
                        icon: const Icon(Icons.videocam),
                        label: const Text('视频'),
                      ),
                      ElevatedButton.icon(
                        onPressed: _recordAudio,
                        icon: const Icon(Icons.mic),
                        label: const Text('录音'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // 心情选择器
                  Text(
                    '选择心情',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: _moodItems.map((item) {
                      final isSelected = _selectedMood == item.type;
                      return InkWell(
                        onTap: () {
                          setState(() {
                            _selectedMood = item.type;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                item.icon,
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey,
                              ),
                              const SizedBox(width: 4),
                              Text(item.label),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                  
                  // 隐私设置
                  SwitchListTile(
                    title: const Text('设为私密'),
                    subtitle: const Text('仅自己可见'),
                    value: _isPrivate,
                    onChanged: (value) {
                      setState(() {
                        _isPrivate = value;
                      });
                    },
                  ),
                ],
              ),
            ),
    );
  }
}

/// 心情项目
class MoodItem {
  final MoodType type;
  final IconData icon;
  final String label;
  
  MoodItem({
    required this.type,
    required this.icon,
    required this.label,
  });
}

/// 音频录制对话框
class _AudioRecordDialog extends StatefulWidget {
  const _AudioRecordDialog();

  @override
  State<_AudioRecordDialog> createState() => _AudioRecordDialogState();
}

class _AudioRecordDialogState extends State<_AudioRecordDialog> {
  final MediaService _mediaService = MediaService();
  bool _isRecording = false;
  int _recordDuration = 0;
  late DateTime _startTime;
  
  @override
  void initState() {
    super.initState();
    _startRecording();
  }
  
  @override
  void dispose() {
    if (_isRecording) {
      _stopRecording(save: false);
    }
    super.dispose();
  }
  
  /// 开始录音
  Future<void> _startRecording() async {
    try {
      await _mediaService.startAudioRecording();
      
      setState(() {
        _isRecording = true;
        _recordDuration = 0;
        _startTime = DateTime.now();
      });
      
      // 更新录音时长
      Future.doWhile(() async {
        await Future.delayed(const Duration(seconds: 1));
        if (!_isRecording) return false;
        
        setState(() {
          _recordDuration = DateTime.now().difference(_startTime).inSeconds;
        });
        
        // 最大录音时长为10秒
        if (_recordDuration >= 10) {
          await _stopRecording();
          return false;
        }
        
        return true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法开始录音: $e')),
        );
        Navigator.pop(context);
      }
    }
  }
  
  /// 停止录音
  Future<void> _stopRecording({bool save = true}) async {
    try {
      final audioFile = await _mediaService.stopAudioRecording();
      
      setState(() {
        _isRecording = false;
      });
      
      if (save && audioFile != null && mounted) {
        Navigator.pop(context, audioFile);
      } else if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法停止录音: $e')),
        );
        Navigator.pop(context);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final minutes = (_recordDuration ~/ 60).toString().padLeft(2, '0');
    final seconds = (_recordDuration % 60).toString().padLeft(2, '0');
    
    return AlertDialog(
      title: const Text('录制音频'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.mic,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '$minutes:$seconds',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _isRecording ? '正在录音...' : '录音已完成',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _isRecording ? () => _stopRecording() : null,
          child: const Text('完成'),
        ),
      ],
    );
  }
}
