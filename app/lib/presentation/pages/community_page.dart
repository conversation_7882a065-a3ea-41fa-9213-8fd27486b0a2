import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 社区页面
class CommunityPage extends StatefulWidget {
  final Map<String, dynamic> community;

  const CommunityPage({
    super.key,
    required this.community,
  });

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<Map<String, dynamic>> _posts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    // 加载帖子
    _loadPosts();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载帖子
  void _loadPosts() {
    setState(() {
      _isLoading = true;
    });

    // 模拟加载延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        // 使用社区中的帖子数据
        _posts.clear();
        _posts.addAll(List<Map<String, dynamic>>.from(widget.community['posts'] ?? []));
        _isLoading = false;
      });
    });
  }

  /// 发送消息
  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // 清空输入框
    _messageController.clear();

    // 添加新帖子
    setState(() {
      _posts.insert(0, {
        'id': 'p${DateTime.now().millisecondsSinceEpoch}',
        'content': message,
        'author': '我',
        'time': '刚刚',
        'likes': 0,
        'comments': 0,
      });
    });

    // 滚动到顶部
    _scrollToTop();
  }

  /// 滚动到顶部
  void _scrollToTop() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.community['name'] ?? '社区'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // 显示社区信息
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(widget.community['name'] ?? ''),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.community['description'] ?? ''),
                      const SizedBox(height: 16),
                      Text('成员: ${widget.community['memberCount'] ?? 0}'),
                      Text('帖子: ${widget.community['postCount'] ?? 0}'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('关闭'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 社区信息
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // 社区图标
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  child: Image.asset(
                    widget.community['icon'] ?? 'assets/images/communities/default.png',
                    width: 32,
                    height: 32,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.group,
                        size: 32,
                        color: Theme.of(context).colorScheme.primary,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // 社区名称和描述
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.community['name'] ?? '未知社区',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        widget.community['description'] ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 帖子列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _posts.isEmpty
                    ? const Center(child: Text('暂无帖子'))
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        itemCount: _posts.length,
                        itemBuilder: (context, index) {
                          return _buildPostCard(_posts[index]);
                        },
                      ),
          ),
          
          // 输入框
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: '分享你的想法...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 12.0,
                      ),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: _sendMessage,
                  mini: true,
                  child: const Icon(Icons.send),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建帖子卡片
  Widget _buildPostCard(Map<String, dynamic> post) {
    final isMyPost = post['author'] == '我';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 作者信息
            Row(
              children: [
                // 作者头像
                CircleAvatar(
                  radius: 16,
                  backgroundColor: isMyPost
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey,
                  child: const Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                
                // 作者名称和时间
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['author'] ?? '匿名',
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      Text(
                        post['time'] ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 帖子内容
            Text(
              post['content'] ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            
            // 点赞和评论
            Row(
              children: [
                // 点赞按钮
                InkWell(
                  onTap: () {
                    // 点赞功能
                    setState(() {
                      post['likes'] = (post['likes'] ?? 0) + 1;
                    });
                  },
                  borderRadius: BorderRadius.circular(16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post['likes'] ?? 0}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // 评论按钮
                InkWell(
                  onTap: () {
                    // 评论功能
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('评论功能即将上线')),
                    );
                  },
                  borderRadius: BorderRadius.circular(16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.chat_bubble_outline,
                          size: 16,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post['comments'] ?? 0}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
