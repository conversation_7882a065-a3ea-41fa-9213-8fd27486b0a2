import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const OneMomentApp());
}

class OneMomentApp extends StatelessWidget {
  const OneMomentApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'One Moment',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _status = '正在检查后端连接...';
  List<dynamic> _moments = [];

  @override
  void initState() {
    super.initState();
    _checkBackendConnection();
  }

  Future<void> _checkBackendConnection() async {
    try {
      // 测试健康检查
      final healthResponse = await http.post(
        Uri.parse('http://localhost:8080/api/health/check'),
        headers: {'Content-Type': 'application/json'},
      );

      if (healthResponse.statusCode == 200) {
        setState(() {
          _status = '后端连接成功！正在获取数据...';
        });

        // 测试获取moments数据
        final momentsResponse = await http.post(
          Uri.parse('http://localhost:8080/api/moments/getMomentList'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({'userId': 5}),
        );

        if (momentsResponse.statusCode == 200) {
          final data = json.decode(momentsResponse.body);
          setState(() {
            _status = '数据获取成功！';
            _moments = data['data'] ?? [];
          });
        } else {
          setState(() {
            _status = '数据获取失败: ${momentsResponse.statusCode}';
          });
        }
      } else {
        setState(() {
          _status = '后端连接失败: ${healthResponse.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = '连接错误: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('One Moment - 后端连接测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '连接状态',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_moments.isNotEmpty) ...[
              const Text(
                'Moments 数据',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _moments.length,
                  itemBuilder: (context, index) {
                    final moment = _moments[index];
                    return Card(
                      child: ListTile(
                        title: Text(moment['content'] ?? '无内容'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('用户ID: ${moment['userId']}'),
                            Text('创建时间: ${moment['createdAt']}'),
                            if (moment['mediaUrl'] != null)
                              Text('媒体: ${moment['mediaUrl']}'),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _checkBackendConnection,
        tooltip: '重新检查连接',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
