name: one_moment_app
description: 用最简单的方式，留住最细腻的幸福
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI
  cupertino_icons: ^1.0.5
  flutter_svg: ^2.0.5
  shimmer: ^3.0.0

  # State Management
  provider: ^6.0.5

  # Navigation
  go_router: ^10.0.0

  # Network
  dio: ^5.1.2

  # Storage
  shared_preferences: ^2.1.1
  path_provider: ^2.0.15

  # Utils
  intl: ^0.19.0
  logger: ^1.3.0
  uuid: ^3.0.7
  package_info_plus: ^4.0.2
  image_picker: ^1.0.4
  table_calendar: ^3.0.9
  sqflite: ^2.4.2
  video_player: ^2.9.5
  location: ^5.0.3
  flutter_markdown: ^0.6.18
  crypto: ^3.0.3
  http: ^1.1.0
  geolocator: ^10.1.0
  weather: ^3.1.1
  camera: ^0.10.5+5
  record: ^5.0.4
  flutter_secure_storage: ^9.0.0
  encrypt: ^5.0.1
  path: ^1.8.3
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1
  build_runner: ^2.4.4
  mockito: ^5.4.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/weather/
    - assets/images/weather_bg/
    - assets/images/agents/
    - assets/images/avatars/
    - assets/images/moments/
    - assets/images/communities/
    - assets/icons/
    - assets/animations/
    - assets/env/
    - assets/config/

  # fonts:
  #   - family: PingFang
  #     fonts:
  #       - asset: assets/fonts/PingFang-Regular.ttf
  #       - asset: assets/fonts/PingFang-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/PingFang-Bold.ttf
  #         weight: 700
