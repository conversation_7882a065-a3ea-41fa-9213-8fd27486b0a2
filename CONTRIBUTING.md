# 贡献指南

感谢您对 One Moment 项目的关注！我们欢迎各种形式的贡献，包括但不限于：

- 报告问题
- 提交功能请求
- 提交代码修复
- 提交新功能实现
- 改进文档

## 开发环境设置

### 前端开发环境

1. 安装 Flutter SDK（3.0 或更高版本）
2. 安装 Android Studio 或 Visual Studio Code
3. 安装 Flutter 和 Dart 插件
4. 克隆仓库并进入 app 目录
5. 运行 `flutter pub get` 安装依赖
6. 运行 `flutter run` 启动应用

### 后端开发环境

1. 安装 JDK 17
2. 安装 Maven
3. 安装 MySQL 8.0
4. 克隆仓库并进入 backend 目录
5. 运行 `mvn clean install -DskipTests` 构建项目
6. 运行 `mvn spring-boot:run` 启动后端服务

## 代码风格

### 前端代码风格

- 遵循 [Dart 风格指南](https://dart.dev/guides/language/effective-dart/style)
- 使用 `flutter format` 格式化代码
- 使用 `flutter analyze` 检查代码质量

### 后端代码风格

- 遵循 [Google Java 风格指南](https://google.github.io/styleguide/javaguide.html)
- 使用 Lombok 减少样板代码
- 使用 Spring 依赖注入而不是直接实例化
- 将业务逻辑放在 Service 层，而不是 Controller 层

## 提交流程

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 提交信息规范

提交信息应遵循以下格式：

```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

类型可以是：
- feat: 新功能
- fix: 修复 bug
- docs: 文档更改
- style: 不影响代码含义的更改（空格、格式化等）
- refactor: 既不修复 bug 也不添加功能的代码更改
- perf: 提高性能的代码更改
- test: 添加或修正测试
- chore: 对构建过程或辅助工具的更改

## 问题报告

报告问题时，请包含以下信息：

- 清晰的问题描述
- 复现步骤
- 预期行为
- 实际行为
- 截图（如适用）
- 环境信息（操作系统、设备型号、Flutter 版本等）

## 功能请求

提交功能请求时，请包含以下信息：

- 功能描述
- 使用场景
- 预期行为
- 可能的实现方式

## 许可证

通过贡献代码，您同意您的贡献将根据项目的许可证进行许可。
